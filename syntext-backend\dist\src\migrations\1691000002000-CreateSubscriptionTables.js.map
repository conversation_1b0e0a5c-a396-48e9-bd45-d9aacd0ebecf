{"version": 3, "file": "1691000002000-CreateSubscriptionTables.js", "sourceRoot": "", "sources": ["../../../src/migrations/1691000002000-CreateSubscriptionTables.ts"], "names": [], "mappings": ";;;AAAA,qCAA8F;AAE9F,MAAa,qCAAqC;IAChD,IAAI,GAAG,uCAAuC,CAAC;IAExC,KAAK,CAAC,EAAE,CAAC,WAAwB;QAEtC,MAAM,WAAW,CAAC,KAAK,CAAC;;KAEvB,CAAC,CAAC;QAGH,MAAM,WAAW,CAAC,KAAK,CAAC;;KAEvB,CAAC,CAAC;QAGH,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,IAAI,EAAE,oBAAoB;YAC1B,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAC9B;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,SAAS;oBACf,MAAM,EAAE,KAAK;oBACb,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,YAAY,CAAC;oBAChD,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,OAAO;oBACb,IAAI,EAAE,SAAS;oBACf,SAAS,EAAE,EAAE;oBACb,KAAK,EAAE,CAAC;oBACR,OAAO,EAAE,CAAC;iBACX;gBACD;oBACE,IAAI,EAAE,cAAc;oBACpB,IAAI,EAAE,KAAK;oBACX,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,OAAO;oBACb,OAAO,EAAE,aAAa;iBACvB;gBACD;oBACE,IAAI,EAAE,UAAU;oBAChB,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,IAAI;iBACd;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,mBAAmB;iBAC7B;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,mBAAmB;iBAC7B;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;QAGF,MAAM,WAAW,CAAC,WAAW,CAC3B,IAAI,eAAK,CAAC;YACR,IAAI,EAAE,oBAAoB;YAC1B,OAAO,EAAE;gBACP;oBACE,IAAI,EAAE,IAAI;oBACV,IAAI,EAAE,MAAM;oBACZ,SAAS,EAAE,IAAI;oBACf,kBAAkB,EAAE,MAAM;oBAC1B,OAAO,EAAE,oBAAoB;iBAC9B;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM;oBACZ,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,QAAQ;oBACd,IAAI,EAAE,MAAM;oBACZ,IAAI,EAAE,CAAC,QAAQ,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,CAAC;oBACpD,OAAO,EAAE,UAAU;iBACpB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,KAAK;oBACX,OAAO,EAAE,CAAC;iBACX;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,0BAA0B;oBAChC,UAAU,EAAE,KAAK;iBAClB;gBACD;oBACE,IAAI,EAAE,SAAS;oBACf,IAAI,EAAE,0BAA0B;oBAChC,UAAU,EAAE,IAAI;iBACjB;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,SAAS;oBACf,OAAO,EAAE,KAAK;iBACf;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,mBAAmB;iBAC7B;gBACD;oBACE,IAAI,EAAE,WAAW;oBACjB,IAAI,EAAE,0BAA0B;oBAChC,OAAO,EAAE,mBAAmB;iBAC7B;aACF;SACF,CAAC,EACF,IAAI,CACL,CAAC;QAGF,MAAM,WAAW,CAAC,gBAAgB,CAChC,oBAAoB,EACpB,IAAI,yBAAe,CAAC;YAClB,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,OAAO;YAC5B,QAAQ,EAAE,SAAS;SACpB,CAAC,CACH,CAAC;QAEF,MAAM,WAAW,CAAC,gBAAgB,CAChC,oBAAoB,EACpB,IAAI,yBAAe,CAAC;YAClB,WAAW,EAAE,CAAC,QAAQ,CAAC;YACvB,qBAAqB,EAAE,CAAC,IAAI,CAAC;YAC7B,mBAAmB,EAAE,oBAAoB;YACzC,QAAQ,EAAE,UAAU;SACrB,CAAC,CACH,CAAC;QAGF,MAAM,WAAW,CAAC,WAAW,CAC3B,oBAAoB,EACpB,IAAI,oBAAU,CAAC,EAAE,IAAI,EAAE,6BAA6B,EAAE,WAAW,EAAE,CAAC,MAAM,CAAC,EAAE,CAAC,CAC/E,CAAC;QAEF,MAAM,WAAW,CAAC,WAAW,CAC3B,oBAAoB,EACpB,IAAI,oBAAU,CAAC,EAAE,IAAI,EAAE,gCAAgC,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CACpF,CAAC;QACF,MAAM,WAAW,CAAC,WAAW,CAC3B,oBAAoB,EACpB,IAAI,oBAAU,CAAC,EAAE,IAAI,EAAE,+BAA+B,EAAE,WAAW,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CACnF,CAAC;IACJ,CAAC;IAEM,KAAK,CAAC,IAAI,CAAC,WAAwB;QACxC,MAAM,WAAW,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAClD,MAAM,WAAW,CAAC,SAAS,CAAC,oBAAoB,CAAC,CAAC;QAClD,MAAM,WAAW,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAC;QAC3D,MAAM,WAAW,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;IAC3D,CAAC;CACF;AArLD,sFAqLC"}