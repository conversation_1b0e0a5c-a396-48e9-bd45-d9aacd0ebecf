// 动画工具函数和配置

export interface AnimationConfig {
  duration?: number;
  delay?: number;
  easing?: string;
  iterations?: number;
  direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
  fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
}

// 预定义的动画配置
export const animationPresets = {
  // 淡入淡出
  fadeIn: {
    duration: 300,
    easing: 'ease-out',
    fillMode: 'both' as const,
  },
  fadeOut: {
    duration: 200,
    easing: 'ease-in',
    fillMode: 'both' as const,
  },
  
  // 滑动动画
  slideInUp: {
    duration: 400,
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    fillMode: 'both' as const,
  },
  slideInDown: {
    duration: 400,
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    fillMode: 'both' as const,
  },
  slideInLeft: {
    duration: 400,
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    fillMode: 'both' as const,
  },
  slideInRight: {
    duration: 400,
    easing: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
    fillMode: 'both' as const,
  },
  
  // 缩放动画
  scaleIn: {
    duration: 300,
    easing: 'cubic-bezier(0.175, 0.885, 0.32, 1.275)',
    fillMode: 'both' as const,
  },
  scaleOut: {
    duration: 200,
    easing: 'ease-in',
    fillMode: 'both' as const,
  },
  
  // 弹跳动画
  bounce: {
    duration: 600,
    easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)',
    fillMode: 'both' as const,
  },
  
  // 摇摆动画
  shake: {
    duration: 500,
    easing: 'ease-in-out',
    iterations: 3,
    fillMode: 'both' as const,
  },
  
  // 脉冲动画
  pulse: {
    duration: 1000,
    easing: 'ease-in-out',
    iterations: Infinity,
    direction: 'alternate' as const,
  },
  
  // 旋转动画
  spin: {
    duration: 1000,
    easing: 'linear',
    iterations: Infinity,
  },
  
  // 呼吸效果
  breathe: {
    duration: 2000,
    easing: 'ease-in-out',
    iterations: Infinity,
    direction: 'alternate' as const,
  },
} as const;

// 动画关键帧定义
export const keyframes = {
  fadeIn: [
    { opacity: 0 },
    { opacity: 1 }
  ],
  fadeOut: [
    { opacity: 1 },
    { opacity: 0 }
  ],
  slideInUp: [
    { transform: 'translateY(30px)', opacity: 0 },
    { transform: 'translateY(0)', opacity: 1 }
  ],
  slideInDown: [
    { transform: 'translateY(-30px)', opacity: 0 },
    { transform: 'translateY(0)', opacity: 1 }
  ],
  slideInLeft: [
    { transform: 'translateX(-30px)', opacity: 0 },
    { transform: 'translateX(0)', opacity: 1 }
  ],
  slideInRight: [
    { transform: 'translateX(30px)', opacity: 0 },
    { transform: 'translateX(0)', opacity: 1 }
  ],
  scaleIn: [
    { transform: 'scale(0.8)', opacity: 0 },
    { transform: 'scale(1)', opacity: 1 }
  ],
  scaleOut: [
    { transform: 'scale(1)', opacity: 1 },
    { transform: 'scale(0.8)', opacity: 0 }
  ],
  bounce: [
    { transform: 'translateY(0)' },
    { transform: 'translateY(-10px)' },
    { transform: 'translateY(0)' }
  ],
  shake: [
    { transform: 'translateX(0)' },
    { transform: 'translateX(-10px)' },
    { transform: 'translateX(10px)' },
    { transform: 'translateX(-10px)' },
    { transform: 'translateX(10px)' },
    { transform: 'translateX(0)' }
  ],
  pulse: [
    { transform: 'scale(1)', opacity: 1 },
    { transform: 'scale(1.05)', opacity: 0.8 }
  ],
  spin: [
    { transform: 'rotate(0deg)' },
    { transform: 'rotate(360deg)' }
  ],
  breathe: [
    { transform: 'scale(1)', opacity: 1 },
    { transform: 'scale(1.02)', opacity: 0.9 }
  ],
  ripple: [
    { transform: 'scale(0)', opacity: 1 },
    { transform: 'scale(4)', opacity: 0 }
  ],
  glow: [
    { boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)' },
    { boxShadow: '0 0 20px rgba(59, 130, 246, 0.8)' },
    { boxShadow: '0 0 5px rgba(59, 130, 246, 0.5)' }
  ],
  float: [
    { transform: 'translateY(0px)' },
    { transform: 'translateY(-6px)' },
    { transform: 'translateY(0px)' }
  ],
  wiggle: [
    { transform: 'rotate(0deg)' },
    { transform: 'rotate(3deg)' },
    { transform: 'rotate(-3deg)' },
    { transform: 'rotate(0deg)' }
  ],
};

// 动画执行函数
export const animate = (
  element: HTMLElement,
  animationName: keyof typeof keyframes,
  config: AnimationConfig = {}
): Animation => {
  const keyframeData = keyframes[animationName];
  const presetConfig = animationName in animationPresets
    ? animationPresets[animationName as keyof typeof animationPresets]
    : {};
  const animationConfig = { ...presetConfig, ...config };

  return element.animate(keyframeData, {
    duration: animationConfig.duration,
    delay: animationConfig.delay,
    easing: animationConfig.easing,
    iterations: animationConfig.iterations,
    direction: animationConfig.direction,
    fill: animationConfig.fillMode,
  });
};

// 序列动画
export const animateSequence = async (
  animations: Array<{
    element: HTMLElement;
    animation: keyof typeof keyframes;
    config?: AnimationConfig;
  }>
): Promise<void> => {
  for (const { element, animation, config } of animations) {
    const animationInstance = animate(element, animation, config);
    await animationInstance.finished;
  }
};

// 并行动画
export const animateParallel = (
  animations: Array<{
    element: HTMLElement;
    animation: keyof typeof keyframes;
    config?: AnimationConfig;
  }>
): Promise<void[]> => {
  const promises = animations.map(async ({ element, animation, config }) => {
    await animate(element, animation, config).finished;
  });

  return Promise.all(promises);
};

// 交错动画（适用于列表项）
export const animateStagger = (
  elements: HTMLElement[],
  animation: keyof typeof keyframes,
  config: AnimationConfig = {},
  staggerDelay: number = 100
): Promise<void[]> => {
  const promises = elements.map(async (element, index) => {
    await animate(element, animation, {
      ...config,
      delay: (config.delay || 0) + (index * staggerDelay),
    }).finished;
  });

  return Promise.all(promises);
};

// 观察器动画（当元素进入视口时触发）
export const createIntersectionAnimation = (
  element: HTMLElement,
  animation: keyof typeof keyframes,
  config: AnimationConfig = {},
  options: IntersectionObserverInit = {}
): IntersectionObserver => {
  const observer = new IntersectionObserver((entries) => {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        animate(entry.target as HTMLElement, animation, config);
        observer.unobserve(entry.target);
      }
    });
  }, {
    threshold: 0.1,
    ...options,
  });
  
  observer.observe(element);
  return observer;
};

// 缓动函数
export const easingFunctions = {
  linear: 'linear',
  easeIn: 'ease-in',
  easeOut: 'ease-out',
  easeInOut: 'ease-in-out',
  easeInSine: 'cubic-bezier(0.12, 0, 0.39, 0)',
  easeOutSine: 'cubic-bezier(0.61, 1, 0.88, 1)',
  easeInOutSine: 'cubic-bezier(0.37, 0, 0.63, 1)',
  easeInQuad: 'cubic-bezier(0.11, 0, 0.5, 0)',
  easeOutQuad: 'cubic-bezier(0.5, 1, 0.89, 1)',
  easeInOutQuad: 'cubic-bezier(0.45, 0, 0.55, 1)',
  easeInCubic: 'cubic-bezier(0.32, 0, 0.67, 0)',
  easeOutCubic: 'cubic-bezier(0.33, 1, 0.68, 1)',
  easeInOutCubic: 'cubic-bezier(0.65, 0, 0.35, 1)',
  easeInQuart: 'cubic-bezier(0.5, 0, 0.75, 0)',
  easeOutQuart: 'cubic-bezier(0.25, 1, 0.5, 1)',
  easeInOutQuart: 'cubic-bezier(0.76, 0, 0.24, 1)',
  easeInQuint: 'cubic-bezier(0.64, 0, 0.78, 0)',
  easeOutQuint: 'cubic-bezier(0.22, 1, 0.36, 1)',
  easeInOutQuint: 'cubic-bezier(0.83, 0, 0.17, 1)',
  easeInExpo: 'cubic-bezier(0.7, 0, 0.84, 0)',
  easeOutExpo: 'cubic-bezier(0.16, 1, 0.3, 1)',
  easeInOutExpo: 'cubic-bezier(0.87, 0, 0.13, 1)',
  easeInCirc: 'cubic-bezier(0.55, 0, 1, 0.45)',
  easeOutCirc: 'cubic-bezier(0, 0.55, 0.45, 1)',
  easeInOutCirc: 'cubic-bezier(0.85, 0, 0.15, 1)',
  easeInBack: 'cubic-bezier(0.36, 0, 0.66, -0.56)',
  easeOutBack: 'cubic-bezier(0.34, 1.56, 0.64, 1)',
  easeInOutBack: 'cubic-bezier(0.68, -0.6, 0.32, 1.6)',
} as const;
