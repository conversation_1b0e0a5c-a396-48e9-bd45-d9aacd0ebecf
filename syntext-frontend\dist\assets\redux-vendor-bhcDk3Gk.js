import{r as e}from"./react-vendor-CEjTMBxM.js";var t,r,n=e(),o={exports:{}},i={};var c=(r||(r=1,o.exports=function(){if(t)return i;t=1;var r=e(),n="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useSyncExternalStore,c=r.useRef,u=r.useEffect,a=r.useMemo,s=r.useDebugValue;return i.useSyncExternalStoreWithSelector=function(e,t,r,i,f){var l=c(null);if(null===l.current){var p={hasValue:!1,value:null};l.current=p}else p=l.current;l=a(function(){function e(e){if(!u){if(u=!0,o=e,e=i(e),void 0!==f&&p.hasValue){var t=p.value;if(f(t,e))return c=t}return c=e}if(t=c,n(o,e))return t;var r=i(e);return void 0!==f&&f(t,r)?(o=e,t):(o=e,c=r)}var o,c,u=!1,a=void 0===r?null:r;return[function(){return e(t())},null===a?void 0:function(){return e(a())}]},[t,r,i,f]);var d=o(e,l[0],l[1]);return u(function(){p.hasValue=!0,p.value=d},[d]),s(d),d},i}()),o.exports);var u={notify(){},get:()=>[]};var a=(()=>!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement))(),s=(()=>"undefined"!=typeof navigator&&"ReactNative"===navigator.product)(),f=(()=>a||s?n.useLayoutEffect:n.useEffect)(),l=Symbol.for("react-redux-context"),p="undefined"!=typeof globalThis?globalThis:{};function d(){if(!n.createContext)return{};const e=p[l]??=new Map;let t=e.get(n.createContext);return t||(t=n.createContext(null),e.set(n.createContext,t)),t}var y=d();var h=function(e){const{children:t,context:r,serverState:o,store:i}=e,c=n.useMemo(()=>{const e=function(e){let t,r=u,n=0,o=!1;function i(){s.onStateChange&&s.onStateChange()}function c(){n++,t||(t=e.subscribe(i),r=function(){let e=null,t=null;return{clear(){e=null,t=null},notify(){(()=>{let t=e;for(;t;)t.callback(),t=t.next})()},get(){const t=[];let r=e;for(;r;)t.push(r),r=r.next;return t},subscribe(r){let n=!0;const o=t={callback:r,next:null,prev:t};return o.prev?o.prev.next=o:e=o,function(){n&&null!==e&&(n=!1,o.next?o.next.prev=o.prev:t=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}())}function a(){n--,t&&0===n&&(t(),t=void 0,r.clear(),r=u)}const s={addNestedSub:function(e){c();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),a())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:i,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,c())},tryUnsubscribe:function(){o&&(o=!1,a())},getListeners:()=>r};return s}(i);return{store:i,subscription:e,getServerState:o?()=>o:void 0}},[i,o]),a=n.useMemo(()=>i.getState(),[i]);f(()=>{const{subscription:e}=c;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),a!==i.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}},[c,a]);const s=r||y;return n.createElement(s.Provider,{value:c},t)};function b(e=y){return function(){return n.useContext(e)}}var _=b();function m(e=y){const t=e===y?_:b(e),r=()=>{const{store:e}=t();return e};return Object.assign(r,{withTypes:()=>r}),r}var w=m();function v(e=y){const t=e===y?w:m(e),r=()=>t().dispatch;return Object.assign(r,{withTypes:()=>r}),r}var g=v(),S=(e,t)=>e===t;function E(e=y){const t=e===y?_:b(e),r=(e,r={})=>{const{equalityFn:o=S}="function"==typeof r?{equalityFn:r}:r,i=t(),{store:u,subscription:a,getServerState:s}=i;n.useRef(!0);const f=n.useCallback({[e.name]:t=>e(t)}[e.name],[e]),l=c.useSyncExternalStoreWithSelector(a.addNestedSub,u.getState,s||u.getState,f,o);return n.useDebugValue(l),l};return Object.assign(r,{withTypes:()=>r}),r}var O=E();function j(e){return`Minified Redux error #${e}; visit https://redux.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}var C=(()=>"function"==typeof Symbol&&Symbol.observable||"@@observable")(),x=()=>Math.random().toString(36).substring(7).split("").join("."),P={INIT:`@@redux/INIT${x()}`,REPLACE:`@@redux/REPLACE${x()}`,PROBE_UNKNOWN_ACTION:()=>`@@redux/PROBE_UNKNOWN_ACTION${x()}`};function R(e){if("object"!=typeof e||null===e)return!1;let t=e;for(;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t||null===Object.getPrototypeOf(e)}function N(e,t,r){if("function"!=typeof e)throw new Error(j(2));if("function"==typeof t&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(j(0));if("function"==typeof t&&void 0===r&&(r=t,t=void 0),void 0!==r){if("function"!=typeof r)throw new Error(j(1));return r(N)(e,t)}let n=e,o=t,i=new Map,c=i,u=0,a=!1;function s(){c===i&&(c=new Map,i.forEach((e,t)=>{c.set(t,e)}))}function f(){if(a)throw new Error(j(3));return o}function l(e){if("function"!=typeof e)throw new Error(j(4));if(a)throw new Error(j(5));let t=!0;s();const r=u++;return c.set(r,e),function(){if(t){if(a)throw new Error(j(6));t=!1,s(),c.delete(r),i=null}}}function p(e){if(!R(e))throw new Error(j(7));if(void 0===e.type)throw new Error(j(8));if("string"!=typeof e.type)throw new Error(j(17));if(a)throw new Error(j(9));try{a=!0,o=n(o,e)}finally{a=!1}return(i=c).forEach(e=>{e()}),e}p({type:P.INIT});return{dispatch:p,subscribe:l,getState:f,replaceReducer:function(e){if("function"!=typeof e)throw new Error(j(10));n=e,p({type:P.REPLACE})},[C]:function(){const e=l;return{subscribe(t){if("object"!=typeof t||null===t)throw new Error(j(11));function r(){const e=t;e.next&&e.next(f())}r();return{unsubscribe:e(r)}},[C](){return this}}}}}function A(e){const t=Object.keys(e),r={};for(let c=0;c<t.length;c++){const n=t[c];"function"==typeof e[n]&&(r[n]=e[n])}const n=Object.keys(r);let o;try{!function(e){Object.keys(e).forEach(t=>{const r=e[t];if(void 0===r(void 0,{type:P.INIT}))throw new Error(j(12));if(void 0===r(void 0,{type:P.PROBE_UNKNOWN_ACTION()}))throw new Error(j(13))})}(r)}catch(i){o=i}return function(e={},t){if(o)throw o;let i=!1;const c={};for(let o=0;o<n.length;o++){const u=n[o],a=r[u],s=e[u],f=a(s,t);if(void 0===f)throw t&&t.type,new Error(j(14));c[u]=f,i=i||f!==s}return i=i||n.length!==Object.keys(e).length,i?c:e}}function T(...e){return 0===e.length?e=>e:1===e.length?e[0]:e.reduce((e,t)=>(...r)=>e(t(...r)))}var k=Symbol.for("immer-nothing"),M=Symbol.for("immer-draftable"),z=Symbol.for("immer-state");function D(e,...t){throw new Error(`[Immer] minified error nr: ${e}. Full error at: https://bit.ly/3cXEKWf`)}var I=Object.getPrototypeOf;function F(e){return!!e&&!!e[z]}function W(e){return!!e&&($(e)||Array.isArray(e)||!!e[M]||!!e.constructor?.[M]||K(e)||X(e))}var q=Object.prototype.constructor.toString();function $(e){if(!e||"object"!=typeof e)return!1;const t=I(e);if(null===t)return!0;const r=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return r===Object||"function"==typeof r&&Function.toString.call(r)===q}function B(e,t){0===L(e)?Reflect.ownKeys(e).forEach(r=>{t(r,e[r],e)}):e.forEach((r,n)=>t(n,r,e))}function L(e){const t=e[z];return t?t.type_:Array.isArray(e)?1:K(e)?2:X(e)?3:0}function V(e,t){return 2===L(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function U(e,t,r){const n=L(e);2===n?e.set(t,r):3===n?e.add(r):e[t]=r}function K(e){return e instanceof Map}function X(e){return e instanceof Set}function G(e){return e.copy_||e.base_}function H(e,t){if(K(e))return new Map(e);if(X(e))return new Set(e);if(Array.isArray(e))return Array.prototype.slice.call(e);const r=$(e);if(!0===t||"class_only"===t&&!r){const t=Object.getOwnPropertyDescriptors(e);delete t[z];let r=Reflect.ownKeys(t);for(let n=0;n<r.length;n++){const o=r[n],i=t[o];!1===i.writable&&(i.writable=!0,i.configurable=!0),(i.get||i.set)&&(t[o]={configurable:!0,writable:!0,enumerable:i.enumerable,value:e[o]})}return Object.create(I(e),t)}{const t=I(e);if(null!==t&&r)return{...e};const n=Object.create(t);return Object.assign(n,e)}}function J(e,t=!1){return Y(e)||F(e)||!W(e)||(L(e)>1&&(e.set=e.add=e.clear=e.delete=Q),Object.freeze(e),t&&Object.entries(e).forEach(([e,t])=>J(t,!0))),e}function Q(){D(2)}function Y(e){return Object.isFrozen(e)}var Z,ee={};function te(e){const t=ee[e];return t||D(0),t}function re(){return Z}function ne(e,t){t&&(te("Patches"),e.patches_=[],e.inversePatches_=[],e.patchListener_=t)}function oe(e){ie(e),e.drafts_.forEach(ue),e.drafts_=null}function ie(e){e===Z&&(Z=e.parent_)}function ce(e){return Z={drafts_:[],parent_:Z,immer_:e,canAutoFreeze_:!0,unfinalizedDrafts_:0}}function ue(e){const t=e[z];0===t.type_||1===t.type_?t.revoke_():t.revoked_=!0}function ae(e,t){t.unfinalizedDrafts_=t.drafts_.length;const r=t.drafts_[0];return void 0!==e&&e!==r?(r[z].modified_&&(oe(t),D(4)),W(e)&&(e=se(t,e),t.parent_||le(t,e)),t.patches_&&te("Patches").generateReplacementPatches_(r[z].base_,e,t.patches_,t.inversePatches_)):e=se(t,r,[]),oe(t),t.patches_&&t.patchListener_(t.patches_,t.inversePatches_),e!==k?e:void 0}function se(e,t,r){if(Y(t))return t;const n=t[z];if(!n)return B(t,(o,i)=>fe(e,n,t,o,i,r)),t;if(n.scope_!==e)return t;if(!n.modified_)return le(e,n.base_,!0),n.base_;if(!n.finalized_){n.finalized_=!0,n.scope_.unfinalizedDrafts_--;const t=n.copy_;let o=t,i=!1;3===n.type_&&(o=new Set(t),t.clear(),i=!0),B(o,(o,c)=>fe(e,n,t,o,c,r,i)),le(e,t,!1),r&&e.patches_&&te("Patches").generatePatches_(n,r,e.patches_,e.inversePatches_)}return n.copy_}function fe(e,t,r,n,o,i,c){if(F(o)){const c=se(e,o,i&&t&&3!==t.type_&&!V(t.assigned_,n)?i.concat(n):void 0);if(U(r,n,c),!F(c))return;e.canAutoFreeze_=!1}else c&&r.add(o);if(W(o)&&!Y(o)){if(!e.immer_.autoFreeze_&&e.unfinalizedDrafts_<1)return;se(e,o),t&&t.scope_.parent_||"symbol"==typeof n||!Object.prototype.propertyIsEnumerable.call(r,n)||le(e,o)}}function le(e,t,r=!1){!e.parent_&&e.immer_.autoFreeze_&&e.canAutoFreeze_&&J(t,r)}var pe={get(e,t){if(t===z)return e;const r=G(e);if(!V(r,t))return function(e,t,r){const n=he(t,r);return n?"value"in n?n.value:n.get?.call(e.draft_):void 0}(e,r,t);const n=r[t];return e.finalized_||!W(n)?n:n===ye(e.base_,t)?(_e(e),e.copy_[t]=me(n,e)):n},has:(e,t)=>t in G(e),ownKeys:e=>Reflect.ownKeys(G(e)),set(e,t,r){const n=he(G(e),t);if(n?.set)return n.set.call(e.draft_,r),!0;if(!e.modified_){const n=ye(G(e),t),c=n?.[z];if(c&&c.base_===r)return e.copy_[t]=r,e.assigned_[t]=!1,!0;if(((o=r)===(i=n)?0!==o||1/o==1/i:o!=o&&i!=i)&&(void 0!==r||V(e.base_,t)))return!0;_e(e),be(e)}var o,i;return e.copy_[t]===r&&(void 0!==r||t in e.copy_)||Number.isNaN(r)&&Number.isNaN(e.copy_[t])||(e.copy_[t]=r,e.assigned_[t]=!0),!0},deleteProperty:(e,t)=>(void 0!==ye(e.base_,t)||t in e.base_?(e.assigned_[t]=!1,_e(e),be(e)):delete e.assigned_[t],e.copy_&&delete e.copy_[t],!0),getOwnPropertyDescriptor(e,t){const r=G(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n?{writable:!0,configurable:1!==e.type_||"length"!==t,enumerable:n.enumerable,value:r[t]}:n},defineProperty(){D(11)},getPrototypeOf:e=>I(e.base_),setPrototypeOf(){D(12)}},de={};function ye(e,t){const r=e[z];return(r?G(r):e)[t]}function he(e,t){if(!(t in e))return;let r=I(e);for(;r;){const e=Object.getOwnPropertyDescriptor(r,t);if(e)return e;r=I(r)}}function be(e){e.modified_||(e.modified_=!0,e.parent_&&be(e.parent_))}function _e(e){e.copy_||(e.copy_=H(e.base_,e.scope_.immer_.useStrictShallowCopy_))}B(pe,(e,t)=>{de[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),de.deleteProperty=function(e,t){return de.set.call(this,e,t,void 0)},de.set=function(e,t,r){return pe.set.call(this,e[0],t,r,e[0])};function me(e,t){const r=K(e)?te("MapSet").proxyMap_(e,t):X(e)?te("MapSet").proxySet_(e,t):function(e,t){const r=Array.isArray(e),n={type_:r?1:0,scope_:t?t.scope_:re(),modified_:!1,finalized_:!1,assigned_:{},parent_:t,base_:e,draft_:null,copy_:null,revoke_:null,isManual_:!1};let o=n,i=pe;r&&(o=[n],i=de);const{revoke:c,proxy:u}=Proxy.revocable(o,i);return n.draft_=u,n.revoke_=c,u}(e,t);return(t?t.scope_:re()).drafts_.push(r),r}function we(e){if(!W(e)||Y(e))return e;const t=e[z];let r;if(t){if(!t.modified_)return t.base_;t.finalized_=!0,r=H(e,t.scope_.immer_.useStrictShallowCopy_)}else r=H(e,!0);return B(r,(e,t)=>{U(r,e,we(t))}),t&&(t.finalized_=!1),r}var ve=new class{constructor(e){this.autoFreeze_=!0,this.useStrictShallowCopy_=!1,this.produce=(e,t,r)=>{if("function"==typeof e&&"function"!=typeof t){const r=t;t=e;const n=this;return function(e=r,...o){return n.produce(e,e=>t.call(this,e,...o))}}let n;if("function"!=typeof t&&D(6),void 0!==r&&"function"!=typeof r&&D(7),W(e)){const o=ce(this),i=me(e,void 0);let c=!0;try{n=t(i),c=!1}finally{c?oe(o):ie(o)}return ne(o,r),ae(n,o)}if(!e||"object"!=typeof e){if(n=t(e),void 0===n&&(n=e),n===k&&(n=void 0),this.autoFreeze_&&J(n,!0),r){const t=[],o=[];te("Patches").generateReplacementPatches_(e,n,t,o),r(t,o)}return n}D(1)},this.produceWithPatches=(e,t)=>{if("function"==typeof e)return(t,...r)=>this.produceWithPatches(t,t=>e(t,...r));let r,n;return[this.produce(e,t,(e,t)=>{r=e,n=t}),r,n]},"boolean"==typeof e?.autoFreeze&&this.setAutoFreeze(e.autoFreeze),"boolean"==typeof e?.useStrictShallowCopy&&this.setUseStrictShallowCopy(e.useStrictShallowCopy)}createDraft(e){W(e)||D(8),F(e)&&(e=function(e){F(e)||D(10);return we(e)}(e));const t=ce(this),r=me(e,void 0);return r[z].isManual_=!0,ie(t),r}finishDraft(e,t){const r=e&&e[z];r&&r.isManual_||D(9);const{scope_:n}=r;return ne(n,t),ae(void 0,n)}setAutoFreeze(e){this.autoFreeze_=e}setUseStrictShallowCopy(e){this.useStrictShallowCopy_=e}applyPatches(e,t){let r;for(r=t.length-1;r>=0;r--){const n=t[r];if(0===n.path.length&&"replace"===n.op){e=n.value;break}}r>-1&&(t=t.slice(r+1));const n=te("Patches").applyPatches_;return F(e)?n(e,t):this.produce(e,e=>n(e,t))}},ge=ve.produce;ve.produceWithPatches.bind(ve),ve.setAutoFreeze.bind(ve),ve.setUseStrictShallowCopy.bind(ve),ve.applyPatches.bind(ve),ve.createDraft.bind(ve),ve.finishDraft.bind(ve);var Se=e=>Array.isArray(e)?e:[e];function Ee(e){const t=Array.isArray(e[0])?e[0]:e;return function(e,t="expected all items to be functions, instead received the following types: "){if(!e.every(e=>"function"==typeof e)){const r=e.map(e=>"function"==typeof e?`function ${e.name||"unnamed"}()`:typeof e).join(", ");throw new TypeError(`${t}[${r}]`)}}(t,"createSelector expects all input-selectors to be functions, but received the following types: "),t}var Oe="undefined"!=typeof WeakRef?WeakRef:class{constructor(e){this.value=e}deref(){return this.value}};function je(){return{s:0,v:void 0,o:null,p:null}}function Ce(e,t={}){let r={s:0,v:void 0,o:null,p:null};const{resultEqualityCheck:n}=t;let o,i=0;function c(){let t=r;const{length:c}=arguments;for(let e=0,r=c;e<r;e++){const r=arguments[e];if("function"==typeof r||"object"==typeof r&&null!==r){let e=t.o;null===e&&(t.o=e=new WeakMap);const n=e.get(r);void 0===n?(t=je(),e.set(r,t)):t=n}else{let e=t.p;null===e&&(t.p=e=new Map);const n=e.get(r);void 0===n?(t=je(),e.set(r,t)):t=n}}const u=t;let a;if(1===t.s)a=t.v;else if(a=e.apply(null,arguments),i++,n){const e=o?.deref?.()??o;null!=e&&n(e,a)&&(a=e,0!==i&&i--);o="object"==typeof a&&null!==a||"function"==typeof a?new Oe(a):a}return u.s=1,u.v=a,a}return c.clearCache=()=>{r={s:0,v:void 0,o:null,p:null},c.resetResultsCount()},c.resultsCount=()=>i,c.resetResultsCount=()=>{i=0},c}function xe(e,...t){const r="function"==typeof e?{memoize:e,memoizeOptions:t}:e,n=(...e)=>{let t,n=0,o=0,i={},c=e.pop();"object"==typeof c&&(i=c,c=e.pop()),function(e,t="expected a function, instead received "+typeof e){if("function"!=typeof e)throw new TypeError(t)}(c,`createSelector expects an output function after the inputs, but received: [${typeof c}]`);const u={...r,...i},{memoize:a,memoizeOptions:s=[],argsMemoize:f=Ce,argsMemoizeOptions:l=[]}=u,p=Se(s),d=Se(l),y=Ee(e),h=a(function(){return n++,c.apply(null,arguments)},...p),b=f(function(){o++;const e=function(e,t){const r=[],{length:n}=e;for(let o=0;o<n;o++)r.push(e[o].apply(null,t));return r}(y,arguments);return t=h.apply(null,e),t},...d);return Object.assign(b,{resultFunc:c,memoizedResultFunc:h,dependencies:y,dependencyRecomputations:()=>o,resetDependencyRecomputations:()=>{o=0},lastResult:()=>t,recomputations:()=>n,resetRecomputations:()=>{n=0},memoize:a,argsMemoize:f})};return Object.assign(n,{withTypes:()=>n}),n}var Pe=xe(Ce),Re=Object.assign((e,t=Pe)=>{!function(e,t="expected an object, instead received "+typeof e){if("object"!=typeof e)throw new TypeError(t)}(e,"createStructuredSelector expects first argument to be an object where each property is a selector, instead received a "+typeof e);const r=Object.keys(e);return t(r.map(t=>e[t]),(...e)=>e.reduce((e,t,n)=>(e[r[n]]=t,e),{}))},{withTypes:()=>Re});function Ne(e){return({dispatch:t,getState:r})=>n=>o=>"function"==typeof o?o(t,r,e):n(o)}var Ae=Ne(),Te=Ne,ke="undefined"!=typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"==typeof arguments[0]?T:T.apply(null,arguments)};function Me(e,t){function r(...r){if(t){let n=t(...r);if(!n)throw new Error(rt(0));return{type:e,payload:n.payload,..."meta"in n&&{meta:n.meta},..."error"in n&&{error:n.error}}}return{type:e,payload:r[0]}}return r.toString=()=>`${e}`,r.type=e,r.match=t=>function(e){return R(e)&&"type"in e&&"string"==typeof e.type}(t)&&t.type===e,r}var ze=class e extends Array{constructor(...t){super(...t),Object.setPrototypeOf(this,e.prototype)}static get[Symbol.species](){return e}concat(...e){return super.concat.apply(this,e)}prepend(...t){return 1===t.length&&Array.isArray(t[0])?new e(...t[0].concat(this)):new e(...t.concat(this))}};function De(e){return W(e)?ge(e,()=>{}):e}function Ie(e,t,r){return e.has(t)?e.get(t):e.set(t,r(t)).get(t)}var Fe=e=>t=>{setTimeout(t,e)},We=e=>function(t){const{autoBatch:r=!0}=t??{};let n=new ze(e);return r&&n.push(((e={type:"raf"})=>t=>(...r)=>{const n=t(...r);let o=!0,i=!1,c=!1;const u=new Set,a="tick"===e.type?queueMicrotask:"raf"===e.type?"undefined"!=typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Fe(10):"callback"===e.type?e.queueNotification:Fe(e.timeout),s=()=>{c=!1,i&&(i=!1,u.forEach(e=>e()))};return Object.assign({},n,{subscribe(e){const t=n.subscribe(()=>o&&e());return u.add(e),()=>{t(),u.delete(e)}},dispatch(e){try{return o=!e?.meta?.RTK_autoBatch,i=!o,i&&(c||(c=!0,a(s))),n.dispatch(e)}finally{o=!0}}})})("object"==typeof r?r:void 0)),n};function qe(e){const t=function(e){const{thunk:t=!0,immutableCheck:r=!0,serializableCheck:n=!0,actionCreatorCheck:o=!0}=e??{};let i=new ze;return t&&("boolean"==typeof t?i.push(Ae):i.push(Te(t.extraArgument))),i},{reducer:r,middleware:n,devTools:o=!0,preloadedState:i,enhancers:c}=e||{};let u,a;if("function"==typeof r)u=r;else{if(!R(r))throw new Error(rt(1));u=A(r)}a="function"==typeof n?n(t):t();let s=T;o&&(s=ke({trace:!1,..."object"==typeof o&&o}));const f=function(...e){return t=>(r,n)=>{const o=t(r,n);let i=()=>{throw new Error(j(15))};const c={getState:o.getState,dispatch:(e,...t)=>i(e,...t)},u=e.map(e=>e(c));return i=T(...u)(o.dispatch),{...o,dispatch:i}}}(...a),l=We(f);return N(u,i,s(..."function"==typeof c?c(l):l()))}function $e(e){const t={},r=[];let n;const o={addCase(e,r){const n="string"==typeof e?e:e.type;if(!n)throw new Error(rt(28));if(n in t)throw new Error(rt(29));return t[n]=r,o},addMatcher:(e,t)=>(r.push({matcher:e,reducer:t}),o),addDefaultCase:e=>(n=e,o)};return e(o),[t,r,n]}function Be(...e){return t=>e.some(e=>((e,t)=>{return(r=e)&&"function"==typeof r.match?e.match(t):e(t);var r})(e,t))}var Le=["name","message","stack","code"],Ve=class{constructor(e,t){this.payload=e,this.meta=t}_type},Ue=class{constructor(e,t){this.payload=e,this.meta=t}_type},Ke=e=>{if("object"==typeof e&&null!==e){const t={};for(const r of Le)"string"==typeof e[r]&&(t[r]=e[r]);return t}return{message:String(e)}},Xe="External signal was aborted",Ge=(()=>{function e(e,t,r){const n=Me(e+"/fulfilled",(e,t,r,n)=>({payload:e,meta:{...n||{},arg:r,requestId:t,requestStatus:"fulfilled"}})),o=Me(e+"/pending",(e,t,r)=>({payload:void 0,meta:{...r||{},arg:t,requestId:e,requestStatus:"pending"}})),i=Me(e+"/rejected",(e,t,n,o,i)=>({payload:o,error:(r&&r.serializeError||Ke)(e||"Rejected"),meta:{...i||{},arg:n,requestId:t,rejectedWithValue:!!o,requestStatus:"rejected",aborted:"AbortError"===e?.name,condition:"ConditionError"===e?.name}}));return Object.assign(function(e,{signal:c}={}){return(u,a,s)=>{const f=r?.idGenerator?r.idGenerator(e):((e=21)=>{let t="",r=e;for(;r--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t})(),l=new AbortController;let p,d;function y(e){d=e,l.abort()}c&&(c.aborted?y(Xe):c.addEventListener("abort",()=>y(Xe),{once:!0}));const h=async function(){let c;try{let i=r?.condition?.(e,{getState:a,extra:s});if(null!==(h=i)&&"object"==typeof h&&"function"==typeof h.then&&(i=await i),!1===i||l.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};const b=new Promise((e,t)=>{p=()=>{t({name:"AbortError",message:d||"Aborted"})},l.signal.addEventListener("abort",p)});u(o(f,e,r?.getPendingMeta?.({requestId:f,arg:e},{getState:a,extra:s}))),c=await Promise.race([b,Promise.resolve(t(e,{dispatch:u,getState:a,extra:s,requestId:f,signal:l.signal,abort:y,rejectWithValue:(e,t)=>new Ve(e,t),fulfillWithValue:(e,t)=>new Ue(e,t)})).then(t=>{if(t instanceof Ve)throw t;return t instanceof Ue?n(t.payload,f,e,t.meta):n(t,f,e)})])}catch(b){c=b instanceof Ve?i(null,f,e,b.payload,b.meta):i(b,f,e)}finally{p&&l.signal.removeEventListener("abort",p)}var h;return r&&!r.dispatchConditionRejection&&i.match(c)&&c.meta.condition||u(c),c}();return Object.assign(h,{abort:y,requestId:f,arg:e,unwrap:()=>h.then(He)})}},{pending:o,rejected:i,fulfilled:n,settled:Be(i,n),typePrefix:e})}return e.withTypes=()=>e,e})();function He(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}var Je=Symbol.for("rtk-slice-createasyncthunk");function Qe(e,t){return`${e}/${t}`}function Ye({creators:e}={}){const t=e?.asyncThunk?.[Je];return function(e){const{name:r,reducerPath:n=r}=e;if(!r)throw new Error(rt(11));const o=("function"==typeof e.reducers?e.reducers(function(){function e(e,t){return{_reducerDefinitionType:"asyncThunk",payloadCreator:e,...t}}return e.withTypes=()=>e,{reducer:e=>Object.assign({[e.name]:(...t)=>e(...t)}[e.name],{_reducerDefinitionType:"reducer"}),preparedReducer:(e,t)=>({_reducerDefinitionType:"reducerWithPrepare",prepare:e,reducer:t}),asyncThunk:e}}()):e.reducers)||{},i=Object.keys(o),c={sliceCaseReducersByName:{},sliceCaseReducersByType:{},actionCreators:{},sliceMatchers:[]},u={addCase(e,t){const r="string"==typeof e?e:e.type;if(!r)throw new Error(rt(12));if(r in c.sliceCaseReducersByType)throw new Error(rt(13));return c.sliceCaseReducersByType[r]=t,u},addMatcher:(e,t)=>(c.sliceMatchers.push({matcher:e,reducer:t}),u),exposeAction:(e,t)=>(c.actionCreators[e]=t,u),exposeCaseReducer:(e,t)=>(c.sliceCaseReducersByName[e]=t,u)};function a(){const[t={},r=[],n]="function"==typeof e.extraReducers?$e(e.extraReducers):[e.extraReducers],o={...t,...c.sliceCaseReducersByType};return function(e,t){let r,[n,o,i]=$e(t);if("function"==typeof e)r=()=>De(e());else{const t=De(e);r=()=>t}function c(e=r(),t){let c=[n[t.type],...o.filter(({matcher:e})=>e(t)).map(({reducer:e})=>e)];return 0===c.filter(e=>!!e).length&&(c=[i]),c.reduce((e,r)=>{if(r){if(F(e)){const n=r(e,t);return void 0===n?e:n}if(W(e))return ge(e,e=>r(e,t));{const n=r(e,t);if(void 0===n){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return n}}return e},e)}return c.getInitialState=r,c}(e.initialState,e=>{for(let t in o)e.addCase(t,o[t]);for(let t of c.sliceMatchers)e.addMatcher(t.matcher,t.reducer);for(let t of r)e.addMatcher(t.matcher,t.reducer);n&&e.addDefaultCase(n)})}i.forEach(n=>{const i=o[n],c={reducerName:n,type:Qe(r,n),createNotation:"function"==typeof e.reducers};!function(e){return"asyncThunk"===e._reducerDefinitionType}(i)?function({type:e,reducerName:t,createNotation:r},n,o){let i,c;if("reducer"in n){if(r&&!function(e){return"reducerWithPrepare"===e._reducerDefinitionType}(n))throw new Error(rt(17));i=n.reducer,c=n.prepare}else i=n;o.addCase(e,i).exposeCaseReducer(t,i).exposeAction(t,c?Me(e,c):Me(e))}(c,i,u):function({type:e,reducerName:t},r,n,o){if(!o)throw new Error(rt(18));const{payloadCreator:i,fulfilled:c,pending:u,rejected:a,settled:s,options:f}=r,l=o(e,i,f);n.exposeAction(t,l),c&&n.addCase(l.fulfilled,c);u&&n.addCase(l.pending,u);a&&n.addCase(l.rejected,a);s&&n.addMatcher(l.settled,s);n.exposeCaseReducer(t,{fulfilled:c||tt,pending:u||tt,rejected:a||tt,settled:s||tt})}(c,i,u,t)});const s=e=>e,f=new Map,l=new WeakMap;let p;function d(e,t){return p||(p=a()),p(e,t)}function y(){return p||(p=a()),p.getInitialState()}function h(t,r=!1){function n(e){let o=e[t];return void 0===o&&r&&(o=Ie(l,n,y)),o}function o(t=s){const n=Ie(f,r,()=>new WeakMap);return Ie(n,t,()=>{const n={};for(const[o,i]of Object.entries(e.selectors??{}))n[o]=Ze(i,t,()=>Ie(l,t,y),r);return n})}return{reducerPath:t,getSelectors:o,get selectors(){return o(n)},selectSlice:n}}const b={name:r,reducer:d,actions:c.actionCreators,caseReducers:c.sliceCaseReducersByName,getInitialState:y,...h(n),injectInto(e,{reducerPath:t,...r}={}){const o=t??n;return e.inject({reducerPath:o,reducer:d},r),{...b,...h(o,!0)}}};return b}}function Ze(e,t,r,n){function o(o,...i){let c=t(o);return void 0===c&&n&&(c=r()),e(c,...i)}return o.unwrapped=e,o}var et=Ye();function tt(){}function rt(e){return`Minified Redux Toolkit error #${e}; visit https://redux-toolkit.js.org/Errors?code=${e} for the full message or use the non-minified dev environment for full errors. `}export{h as P,et as a,qe as b,Ge as c,O as d,n as r,g as u};
//# sourceMappingURL=redux-vendor-bhcDk3Gk.js.map
