import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '../hooks';

interface UserSettings {
  profile: {
    name: string;
    email: string;
    avatar?: string;
  };
  preferences: {
    defaultSourceLanguage: string;
    defaultTargetLanguage: string;
    theme: 'light' | 'dark' | 'system';
    language: string;
    autoTranslate: boolean;
    saveHistory: boolean;
    enableNotifications: boolean;
  };
  security: {
    twoFactorEnabled: boolean;
    lastPasswordChange: string;
  };
}

const SettingsPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);
  
  const [settings, setSettings] = useState<UserSettings>({
    profile: {
      name: '',
      email: '',
    },
    preferences: {
      defaultSourceLanguage: 'auto',
      defaultTargetLanguage: 'zh',
      theme: 'light',
      language: 'zh-CN',
      autoTranslate: false,
      saveHistory: true,
      enableNotifications: true,
    },
    security: {
      twoFactorEnabled: false,
      lastPasswordChange: '',
    },
  });

  const [activeTab, setActiveTab] = useState<'profile' | 'preferences' | 'security'>('profile');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const languages = [
    { code: 'auto', name: '自动检测' },
    { code: 'zh', name: '中文' },
    { code: 'en', name: 'English' },
    { code: 'ja', name: '日本語' },
    { code: 'ko', name: '한국어' },
    { code: 'fr', name: 'Français' },
    { code: 'de', name: 'Deutsch' },
    { code: 'es', name: 'Español' },
    { code: 'ru', name: 'Русский' },
    { code: 'pt', name: 'Português' },
    { code: 'it', name: 'Italiano' },
    { code: 'ar', name: 'العربية' },
  ];

  const interfaceLanguages = [
    { code: 'zh-CN', name: '简体中文' },
    { code: 'zh-TW', name: '繁體中文' },
    { code: 'en-US', name: 'English' },
    { code: 'ja-JP', name: '日本語' },
    { code: 'ko-KR', name: '한국어' },
  ];

  const themes = [
    { value: 'light', name: '浅色模式', description: '适合白天使用' },
    { value: 'dark', name: '深色模式', description: '适合夜间使用' },
    { value: 'system', name: '跟随系统', description: '自动切换主题' },
  ];

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    try {
      setLoading(true);
      // TODO: 调用实际API获取用户设置
      // const response = await userApi.getSettings();
      // setSettings(response.data);
      
      // 模拟数据
      setTimeout(() => {
        setSettings({
          profile: {
            name: user?.name || '张三',
            email: user?.email || '<EMAIL>',
          },
          preferences: {
            defaultSourceLanguage: 'auto',
            defaultTargetLanguage: 'zh',
            theme: 'light',
            language: 'zh-CN',
            autoTranslate: false,
            saveHistory: true,
            enableNotifications: true,
          },
          security: {
            twoFactorEnabled: false,
            lastPasswordChange: '2025-07-15T10:00:00Z',
          },
        });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('获取设置失败:', error);
      setLoading(false);
    }
  };

  const handleSaveSettings = async () => {
    try {
      setSaving(true);
      // TODO: 调用实际API保存设置
      // await userApi.updateSettings(settings);
      
      // 模拟保存
      setTimeout(() => {
        setSaving(false);
        setMessage({ type: 'success', text: '设置已保存' });
        setTimeout(() => setMessage(null), 3000);
      }, 1000);
    } catch (_error) {
      setSaving(false);
      setMessage({ type: 'error', text: '保存失败，请重试' });
      setTimeout(() => setMessage(null), 3000);
    }
  };

  const handleInputChange = (section: keyof UserSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value,
      },
    }));
  };

  const handleResetPassword = () => {
    // TODO: 实现重置密码功能
    setMessage({ type: 'success', text: '密码重置邮件已发送到您的邮箱' });
    setTimeout(() => setMessage(null), 3000);
  };

  const handleExportData = () => {
    // TODO: 实现数据导出功能
    setMessage({ type: 'success', text: '数据导出请求已提交，稍后将通过邮件发送' });
    setTimeout(() => setMessage(null), 3000);
  };

  const handleDeleteAccount = () => {
    // TODO: 实现账户删除功能
    if (window.confirm('确定要删除账户吗？此操作不可撤销。')) {
      setMessage({ type: 'error', text: '账户删除功能暂未开放，请联系客服' });
      setTimeout(() => setMessage(null), 3000);
    }
  };

  const tabs = [
    { id: 'profile', name: '个人信息', icon: '👤' },
    { id: 'preferences', name: '偏好设置', icon: '⚙️' },
    { id: 'security', name: '安全设置', icon: '🔒' },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex justify-center items-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="text-gray-500 hover:text-gray-700 mr-4"
              >
                ← 返回首页
              </button>
              <h1 className="text-xl font-bold text-gray-900">个人设置</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">{user?.name || user?.email}</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 消息提示 */}
        {message && (
          <div className={`mb-6 p-4 rounded-md ${
            message.type === 'success' 
              ? 'bg-green-50 border border-green-200 text-green-800' 
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            <div className="flex">
              <div className="flex-shrink-0">
                {message.type === 'success' ? (
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium">{message.text}</p>
              </div>
            </div>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8">
          {/* 侧边栏导航 */}
          <div className="lg:col-span-1">
            <nav className="bg-white shadow rounded-lg p-3 sm:p-4">
              <ul className="space-y-2">
                {tabs.map((tab) => (
                  <li key={tab.id}>
                    <button
                      onClick={() => setActiveTab(tab.id as any)}
                      className={`w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        activeTab === tab.id
                          ? 'bg-primary-100 text-primary-700'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <span className="mr-3">{tab.icon}</span>
                      {tab.name}
                    </button>
                  </li>
                ))}
              </ul>
            </nav>
          </div>

          {/* 主要内容 */}
          <div className="lg:col-span-3">
            <div className="bg-white shadow rounded-lg">
              {/* 个人信息 */}
              {activeTab === 'profile' && (
                <div className="p-4 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-6">个人信息</h3>
                  
                  <div className="space-y-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        姓名
                      </label>
                      <input
                        type="text"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        value={settings.profile.name}
                        onChange={(e) => handleInputChange('profile', 'name', e.target.value)}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        邮箱地址
                      </label>
                      <input
                        type="email"
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50"
                        value={settings.profile.email}
                        disabled
                      />
                      <p className="mt-1 text-sm text-gray-500">
                        邮箱地址不可修改，如需更换请联系客服
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        头像
                      </label>
                      <div className="flex items-center space-x-4">
                        <div className="h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center">
                          <span className="text-xl font-bold text-primary-600">
                            {settings.profile.name.charAt(0)}
                          </span>
                        </div>
                        <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                          更换头像
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 偏好设置 */}
              {activeTab === 'preferences' && (
                <div className="p-4 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-6">偏好设置</h3>
                  
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          默认源语言
                        </label>
                        <select
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                          value={settings.preferences.defaultSourceLanguage}
                          onChange={(e) => handleInputChange('preferences', 'defaultSourceLanguage', e.target.value)}
                        >
                          {languages.map((lang) => (
                            <option key={lang.code} value={lang.code}>
                              {lang.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          默认目标语言
                        </label>
                        <select
                          className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                          value={settings.preferences.defaultTargetLanguage}
                          onChange={(e) => handleInputChange('preferences', 'defaultTargetLanguage', e.target.value)}
                        >
                          {languages.filter(lang => lang.code !== 'auto').map((lang) => (
                            <option key={lang.code} value={lang.code}>
                              {lang.name}
                            </option>
                          ))}
                        </select>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        界面语言
                      </label>
                      <select
                        className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                        value={settings.preferences.language}
                        onChange={(e) => handleInputChange('preferences', 'language', e.target.value)}
                      >
                        {interfaceLanguages.map((lang) => (
                          <option key={lang.code} value={lang.code}>
                            {lang.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-3">
                        主题设置
                      </label>
                      <div className="space-y-3">
                        {themes.map((theme) => (
                          <label key={theme.value} className="flex items-center">
                            <input
                              type="radio"
                              name="theme"
                              value={theme.value}
                              checked={settings.preferences.theme === theme.value}
                              onChange={(e) => handleInputChange('preferences', 'theme', e.target.value)}
                              className="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
                            />
                            <div className="ml-3">
                              <div className="text-sm font-medium text-gray-900">{theme.name}</div>
                              <div className="text-sm text-gray-500">{theme.description}</div>
                            </div>
                          </label>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-gray-900">自动翻译</div>
                          <div className="text-sm text-gray-500">输入文本后自动开始翻译</div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.preferences.autoTranslate}
                            onChange={(e) => handleInputChange('preferences', 'autoTranslate', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-gray-900">保存翻译历史</div>
                          <div className="text-sm text-gray-500">自动保存您的翻译记录</div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.preferences.saveHistory}
                            onChange={(e) => handleInputChange('preferences', 'saveHistory', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <div className="text-sm font-medium text-gray-900">通知提醒</div>
                          <div className="text-sm text-gray-500">接收重要通知和更新</div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.preferences.enableNotifications}
                            onChange={(e) => handleInputChange('preferences', 'enableNotifications', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 安全设置 */}
              {activeTab === 'security' && (
                <div className="p-4 sm:p-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-6">安全设置</h3>
                  
                  <div className="space-y-6">
                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">密码管理</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-sm font-medium text-gray-900">密码</div>
                            <div className="text-sm text-gray-500">
                              上次修改: {new Date(settings.security.lastPasswordChange).toLocaleDateString('zh-CN')}
                            </div>
                          </div>
                          <button
                            onClick={handleResetPassword}
                            className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                          >
                            重置密码
                          </button>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">两步验证</h4>
                      <div className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="text-sm font-medium text-gray-900">两步验证</div>
                            <div className="text-sm text-gray-500">
                              {settings.security.twoFactorEnabled ? '已启用' : '未启用'}
                            </div>
                          </div>
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={settings.security.twoFactorEnabled}
                              onChange={(e) => handleInputChange('security', 'twoFactorEnabled', e.target.checked)}
                              className="sr-only peer"
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                          </label>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium text-gray-900 mb-3">数据管理</h4>
                      <div className="space-y-3">
                        <div className="bg-gray-50 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-sm font-medium text-gray-900">导出数据</div>
                              <div className="text-sm text-gray-500">下载您的翻译数据</div>
                            </div>
                            <button
                              onClick={handleExportData}
                              className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                            >
                              导出数据
                            </button>
                          </div>
                        </div>

                        <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="text-sm font-medium text-red-900">删除账户</div>
                              <div className="text-sm text-red-700">永久删除您的账户和所有数据</div>
                            </div>
                            <button
                              onClick={handleDeleteAccount}
                              className="px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 hover:bg-red-100"
                            >
                              删除账户
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* 保存按钮 */}
              <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg">
                <div className="flex justify-end">
                  <button
                    onClick={handleSaveSettings}
                    disabled={saving}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                  >
                    {saving ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        保存中...
                      </>
                    ) : (
                      '保存设置'
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsPage;