import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  MessageBody,
  ConnectedSocket,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import { Logger, UseGuards } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { TranslationService } from '../services/translation.service';
import { TranslateDto } from '../dto/translate.dto';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  userEmail?: string;
}

@WebSocketGateway({
  cors: {
    origin: true,
    credentials: true,
  },
  namespace: 'translation',
})
export class TranslationGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(TranslationGateway.name);
  private connectedUsers = new Map<string, AuthenticatedSocket>();

  constructor(
    private readonly jwtService: JwtService,
    private readonly translationService: TranslationService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token = client.handshake.auth?.token || client.handshake.headers?.authorization?.replace('Bearer ', '');
      
      if (!token) {
        this.logger.warn(`Client ${client.id} connected without token`);
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      client.userId = payload.sub;
      client.userEmail = payload.email;

      this.connectedUsers.set(client.userId!, client);
      this.logger.log(`User ${client.userEmail} connected via WebSocket`);

      client.emit('connected', {
        message: '连接成功',
        userId: client.userId,
      });
    } catch (error) {
      this.logger.error(`Authentication failed for client ${client.id}:`, error);
      client.emit('error', { message: '认证失败' });
      client.disconnect();
    }
  }

  handleDisconnect(client: AuthenticatedSocket) {
    if (client.userId) {
      this.connectedUsers.delete(client.userId);
      this.logger.log(`User ${client.userEmail} disconnected`);
    }
  }

  @SubscribeMessage('translation:start')
  async handleTranslationStart(
    @MessageBody() data: TranslateDto,
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    if (!client.userId) {
      client.emit('translation:error', { message: '用户未认证' });
      return;
    }

    try {
      this.logger.log(`Starting translation for user ${client.userId}`);
      
      // 发送开始翻译事件
      client.emit('translation:start', {
        message: '开始翻译...',
        sourceText: data.sourceText,
        sourceLanguage: data.sourceLanguage,
        targetLanguage: data.targetLanguage,
      });

      // 模拟翻译进度
      client.emit('translation:progress', {
        progress: 25,
        message: '正在分析文本...',
      });

      setTimeout(() => {
        client.emit('translation:progress', {
          progress: 50,
          message: '正在调用翻译服务...',
        });
      }, 500);

      setTimeout(() => {
        client.emit('translation:progress', {
          progress: 75,
          message: '正在处理翻译结果...',
        });
      }, 1000);

      // 执行实际翻译
      const result = await this.translationService.translateText({
        ...data,
        userId: client.userId,
      });

      // 发送翻译完成事件
      client.emit('translation:complete', {
        progress: 100,
        message: '翻译完成',
        result,
      });

      this.logger.log(`Translation completed for user ${client.userId}`);
    } catch (error) {
      this.logger.error(`Translation failed for user ${client.userId}:`, error);
      client.emit('translation:error', {
        message: error.message || '翻译失败',
      });
    }
  }

  @SubscribeMessage('translation:batch')
  async handleBatchTranslation(
    @MessageBody() data: { translations: TranslateDto[] },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    if (!client.userId) {
      client.emit('translation:error', { message: '用户未认证' });
      return;
    }

    try {
      this.logger.log(`Starting batch translation for user ${client.userId}`);
      
      const totalTranslations = data.translations.length;
      let completedTranslations = 0;

      client.emit('translation:batch:start', {
        message: '开始批量翻译...',
        total: totalTranslations,
      });

      const results: any[] = [];

      for (const translation of data.translations) {
        try {
          client.emit('translation:batch:progress', {
            progress: Math.round((completedTranslations / totalTranslations) * 100),
            completed: completedTranslations,
            total: totalTranslations,
            current: translation.sourceText.substring(0, 50) + '...',
          });

          const result = await this.translationService.translateText({
            ...translation,
            userId: client.userId,
          });

          results.push(result);
          completedTranslations++;

        } catch (error) {
          this.logger.error(`Batch translation item failed:`, error);
          results.push({
            error: error.message,
            sourceText: translation.sourceText,
          });
          completedTranslations++;
        }
      }

      client.emit('translation:batch:complete', {
        message: '批量翻译完成',
        results,
        total: totalTranslations,
        successful: results.filter(r => !(r as any).error).length,
        failed: results.filter(r => (r as any).error).length,
      });

      this.logger.log(`Batch translation completed for user ${client.userId}`);
    } catch (error) {
      this.logger.error(`Batch translation failed for user ${client.userId}:`, error);
      client.emit('translation:error', {
        message: error.message || '批量翻译失败',
      });
    }
  }

  @SubscribeMessage('translation:history')
  async handleGetHistory(
    @MessageBody() data: { page?: number; limit?: number },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    if (!client.userId) {
      client.emit('translation:error', { message: '用户未认证' });
      return;
    }

    try {
      const history = await this.translationService.getTranslationHistory(
        client.userId,
        data.page || 1,
        data.limit || 20,
      );

      client.emit('translation:history', {
        ...history,
        message: '获取历史记录成功',
      });
    } catch (error) {
      this.logger.error(`Get history failed for user ${client.userId}:`, error);
      client.emit('translation:error', {
        message: '获取历史记录失败',
      });
    }
  }

  @SubscribeMessage('translation:favorite')
  async handleToggleFavorite(
    @MessageBody() data: { translationId: string },
    @ConnectedSocket() client: AuthenticatedSocket,
  ) {
    if (!client.userId) {
      client.emit('translation:error', { message: '用户未认证' });
      return;
    }

    try {
      const result = await this.translationService.toggleFavorite(
        data.translationId,
        client.userId,
      );

      client.emit('translation:favorite:updated', {
        translation: result,
        message: result.isFavorite ? '已添加到收藏' : '已取消收藏',
      });
    } catch (error) {
      this.logger.error(`Toggle favorite failed for user ${client.userId}:`, error);
      client.emit('translation:error', {
        message: '操作失败',
      });
    }
  }

  // 向特定用户发送消息的方法
  sendToUser(userId: string, event: string, data: any) {
    const client = this.connectedUsers.get(userId);
    if (client) {
      client.emit(event, data);
    }
  }

  // 广播消息给所有连接的用户
  broadcast(event: string, data: any) {
    this.server.emit(event, data);
  }
}