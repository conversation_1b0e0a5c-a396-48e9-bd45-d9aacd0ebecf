"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateUserTable1691000000000 = void 0;
const typeorm_1 = require("typeorm");
class CreateUserTable1691000000000 {
    name = 'CreateUserTable1691000000000';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TYPE "user_role" AS ENUM('admin', 'user')
    `);
        await queryRunner.query(`
      CREATE TYPE "user_status" AS ENUM('active', 'inactive', 'suspended')
    `);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'users',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'email',
                    type: 'varchar',
                    length: '255',
                    isUnique: true,
                    isNullable: false,
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '100',
                    isNullable: true,
                },
                {
                    name: 'role',
                    type: 'enum',
                    enum: ['admin', 'user'],
                    default: "'user'",
                },
                {
                    name: 'status',
                    type: 'enum',
                    enum: ['active', 'inactive', 'suspended'],
                    default: "'active'",
                },
                {
                    name: 'translationCount',
                    type: 'int',
                    default: 0,
                },
                {
                    name: 'monthlyTranslationCount',
                    type: 'int',
                    default: 0,
                },
                {
                    name: 'lastLoginAt',
                    type: 'timestamp with time zone',
                    isNullable: true,
                },
                {
                    name: 'preferences',
                    type: 'jsonb',
                    default: "'{}'::jsonb",
                },
                {
                    name: 'createdAt',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createIndex('users', new typeorm_1.TableIndex({ name: 'IDX_users_email', columnNames: ['email'] }));
        await queryRunner.createIndex('users', new typeorm_1.TableIndex({ name: 'IDX_users_role', columnNames: ['role'] }));
        await queryRunner.createIndex('users', new typeorm_1.TableIndex({ name: 'IDX_users_status', columnNames: ['status'] }));
        await queryRunner.createIndex('users', new typeorm_1.TableIndex({ name: 'IDX_users_created_at', columnNames: ['createdAt'] }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('users');
        await queryRunner.query(`DROP TYPE "user_status"`);
        await queryRunner.query(`DROP TYPE "user_role"`);
    }
}
exports.CreateUserTable1691000000000 = CreateUserTable1691000000000;
//# sourceMappingURL=1691000000000-CreateUserTable.js.map