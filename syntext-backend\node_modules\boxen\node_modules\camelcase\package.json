{"name": "camelcase", "version": "6.3.0", "description": "Convert a dash/dot/underscore/space separated string to camelCase or PascalCase: `foo-bar` → `fooBar`", "license": "MIT", "repository": "sindresorhus/camelcase", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "engines": {"node": ">=10"}, "scripts": {"test": "xo && ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["camelcase", "camel-case", "camel", "case", "dash", "hyphen", "dot", "underscore", "separator", "string", "text", "convert", "pascalcase", "pascal-case"], "devDependencies": {"ava": "^1.4.1", "tsd": "^0.11.0", "xo": "^0.28.3"}}