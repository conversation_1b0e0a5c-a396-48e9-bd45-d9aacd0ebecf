import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Payment, PaymentStatus, PaymentMethod } from '../../entities/payment.entity';
import { UserSubscription } from '../../entities/user-subscription.entity';
import { PaymentGatewayService, PaymentRequest } from './payment-gateway.service';
import { SubscriptionService } from '../../subscription/services/subscription.service';

export interface CreatePaymentDto {
  userId: string;
  subscriptionId: string;
  amount: number;
  currency: string;
  method: PaymentMethod;
  description: string;
  couponId?: string;
  returnUrl?: string;
}

@Injectable()
export class PaymentService {
  private readonly logger = new Logger(PaymentService.name);

  constructor(
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
    @InjectRepository(UserSubscription)
    private userSubscriptionRepository: Repository<UserSubscription>,
    private paymentGatewayService: PaymentGatewayService,
    private subscriptionService: SubscriptionService,
  ) {}

  async createPayment(createDto: CreatePaymentDto) {
    // 验证订阅存在
    const subscription = await this.userSubscriptionRepository.findOne({
      where: { id: createDto.subscriptionId, userId: createDto.userId },
      relations: ['plan'],
    });

    if (!subscription) {
      throw new NotFoundException('订阅不存在');
    }

    // 生成交易ID
    const transactionId = this.generateTransactionId();

    // 创建支付记录
    const payment = this.paymentRepository.create({
      transactionId,
      amount: createDto.amount,
      discountAmount: 0, // 后续优惠码功能会更新这个值
      finalAmount: createDto.amount,
      currency: createDto.currency,
      status: PaymentStatus.PENDING,
      method: createDto.method,
      description: createDto.description,
      userId: createDto.userId,
      couponId: createDto.couponId,
      metadata: {
        subscriptionPlan: subscription.plan.name,
        duration: subscription.billingCycle === 'yearly' ? 12 : 1,
      },
    });

    const savedPayment = await this.paymentRepository.save(payment);

    // 调用支付网关
    const paymentRequest: PaymentRequest = {
      amount: createDto.amount,
      currency: createDto.currency,
      description: createDto.description,
      userId: createDto.userId,
      method: createDto.method,
      returnUrl: createDto.returnUrl,
      metadata: {
        paymentId: savedPayment.id,
        subscriptionId: createDto.subscriptionId,
      },
    };

    const gatewayResponse = await this.paymentGatewayService.createPayment(paymentRequest);

    if (!gatewayResponse.success) {
      // 更新支付状态为失败
      savedPayment.status = PaymentStatus.FAILED;
      savedPayment.failureReason = gatewayResponse.errorMessage;
      await this.paymentRepository.save(savedPayment);

      throw new BadRequestException(gatewayResponse.errorMessage || '支付创建失败');
    }

    // 更新支付记录的网关响应
    savedPayment.metadata = {
      ...savedPayment.metadata,
      gatewayResponse: gatewayResponse.gatewayResponse,
    };
    await this.paymentRepository.save(savedPayment);

    this.logger.log(`Created payment ${savedPayment.id} for user ${createDto.userId}`);

    return {
      paymentId: savedPayment.id,
      transactionId: savedPayment.transactionId,
      amount: savedPayment.finalAmount,
      currency: savedPayment.currency,
      method: savedPayment.method,
      paymentUrl: gatewayResponse.paymentUrl,
      qrCode: gatewayResponse.qrCode,
      status: savedPayment.status,
    };
  }

  async handlePaymentCallback(paymentId: string, callbackData: any): Promise<void> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
    });

    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }

    if (payment.status !== PaymentStatus.PENDING) {
      this.logger.warn(`Payment ${paymentId} is not in pending status: ${payment.status}`);
      return;
    }

    // 验证支付
    const isValid = await this.paymentGatewayService.verifyPayment(
      payment.transactionId,
      callbackData,
    );

    if (isValid) {
      await this.markPaymentAsCompleted(paymentId);
    } else {
      await this.markPaymentAsFailed(paymentId, '支付验证失败');
    }
  }

  async markPaymentAsCompleted(paymentId: string): Promise<void> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
    });

    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }

    payment.status = PaymentStatus.COMPLETED;
    payment.completedAt = new Date();
    await this.paymentRepository.save(payment);

    // 激活订阅
    if ((payment.metadata as any)?.subscriptionId) {
      try {
        await this.subscriptionService.activateSubscription((payment.metadata as any).subscriptionId);
        this.logger.log(`Activated subscription ${(payment.metadata as any).subscriptionId} for payment ${paymentId}`);
      } catch (error) {
        this.logger.error(`Failed to activate subscription: ${error.message}`);
      }
    }

    this.logger.log(`Payment ${paymentId} completed successfully`);
  }

  async markPaymentAsFailed(paymentId: string, reason: string): Promise<void> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
    });

    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }

    payment.status = PaymentStatus.FAILED;
    payment.failureReason = reason;
    await this.paymentRepository.save(payment);

    this.logger.log(`Payment ${paymentId} marked as failed: ${reason}`);
  }

  async getPaymentById(paymentId: string): Promise<Payment | null> {
    return this.paymentRepository.findOne({
      where: { id: paymentId },
      relations: ['user', 'coupon'],
    });
  }

  async getUserPayments(userId: string, limit: number = 20, offset: number = 0): Promise<Payment[]> {
    return this.paymentRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      take: limit,
      skip: offset,
      relations: ['coupon'],
    });
  }

  async refundPayment(paymentId: string, amount?: number, reason?: string): Promise<boolean> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
    });

    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }

    if (payment.status !== PaymentStatus.COMPLETED) {
      throw new BadRequestException('只能退款已完成的支付');
    }

    const refundAmount = amount || payment.finalAmount;
    
    const success = await this.paymentGatewayService.refundPayment(
      payment.transactionId,
      refundAmount,
      reason,
    );

    if (success) {
      payment.status = PaymentStatus.REFUNDED;
      payment.metadata = {
        ...payment.metadata,
        refundAmount,
        refundReason: reason,
        refundedAt: new Date(),
      } as any;
      await this.paymentRepository.save(payment);

      this.logger.log(`Payment ${paymentId} refunded successfully`);
    }

    return success;
  }

  async checkPaymentStatus(paymentId: string): Promise<Payment> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
    });

    if (!payment) {
      throw new NotFoundException('支付记录不存在');
    }

    if (payment.status === PaymentStatus.PENDING) {
      // 查询最新的支付状态
      const gatewayStatus = await this.paymentGatewayService.getPaymentStatus(payment.transactionId);
      
      if (gatewayStatus === 'completed') {
        await this.markPaymentAsCompleted(paymentId);
        payment.status = PaymentStatus.COMPLETED;
      } else if (gatewayStatus === 'failed') {
        await this.markPaymentAsFailed(paymentId, '支付失败');
        payment.status = PaymentStatus.FAILED;
      }
    }

    return payment;
  }

  private generateTransactionId(): string {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substr(2, 9);
    return `TXN_${timestamp}_${random}`.toUpperCase();
  }
}