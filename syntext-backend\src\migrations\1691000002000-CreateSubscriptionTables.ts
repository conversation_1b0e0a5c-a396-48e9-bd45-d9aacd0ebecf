import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateSubscriptionTables1691000002000 implements MigrationInterface {
  name = 'CreateSubscriptionTables1691000002000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建订阅计划枚举类型
    await queryRunner.query(`
      CREATE TYPE "subscription_plan" AS ENUM('free', 'basic', 'premium', 'enterprise')
    `);

    // 创建订阅状态枚举类型
    await queryRunner.query(`
      CREATE TYPE "subscription_status" AS ENUM('active', 'inactive', 'cancelled', 'expired')
    `);

    // 创建订阅计划表
    await queryRunner.createTable(
      new Table({
        name: 'subscription_plans',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'name',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'plan',
            type: 'enum',
            enum: ['free', 'basic', 'premium', 'enterprise'],
            isNullable: false,
          },
          {
            name: 'price',
            type: 'decimal',
            precision: 10,
            scale: 2,
            default: 0,
          },
          {
            name: 'monthlyQuota',
            type: 'int',
            isNullable: false,
          },
          {
            name: 'features',
            type: 'jsonb',
            default: "'{}'::jsonb",
          },
          {
            name: 'isActive',
            type: 'boolean',
            default: true,
          },
          {
            name: 'createdAt',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // 创建用户订阅表
    await queryRunner.createTable(
      new Table({
        name: 'user_subscriptions',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'userId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'planId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['active', 'inactive', 'cancelled', 'expired'],
            default: "'active'",
          },
          {
            name: 'usedQuota',
            type: 'int',
            default: 0,
          },
          {
            name: 'startDate',
            type: 'timestamp with time zone',
            isNullable: false,
          },
          {
            name: 'endDate',
            type: 'timestamp with time zone',
            isNullable: true,
          },
          {
            name: 'autoRenew',
            type: 'boolean',
            default: false,
          },
          {
            name: 'createdAt',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // 创建外键约束
    await queryRunner.createForeignKey(
      'user_subscriptions',
      new TableForeignKey({
        columnNames: ['userId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );

    await queryRunner.createForeignKey(
      'user_subscriptions',
      new TableForeignKey({
        columnNames: ['planId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'subscription_plans',
        onDelete: 'RESTRICT',
      }),
    );

    // 创建索引
    await queryRunner.createIndex(
      'subscription_plans',
      new TableIndex({ name: 'IDX_subscription_plans_plan', columnNames: ['plan'] }),
    );

    await queryRunner.createIndex(
      'user_subscriptions',
      new TableIndex({ name: 'IDX_user_subscriptions_user_id', columnNames: ['userId'] }),
    );
    await queryRunner.createIndex(
      'user_subscriptions',
      new TableIndex({ name: 'IDX_user_subscriptions_status', columnNames: ['status'] }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('user_subscriptions');
    await queryRunner.dropTable('subscription_plans');
    await queryRunner.query(`DROP TYPE "subscription_status"`);
    await queryRunner.query(`DROP TYPE "subscription_plan"`);
  }
}
