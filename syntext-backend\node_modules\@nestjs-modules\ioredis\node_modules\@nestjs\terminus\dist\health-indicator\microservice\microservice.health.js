"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MicroserviceHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const __1 = require("../");
const errors_1 = require("../../errors");
const health_check_error_1 = require("../../health-check/health-check.error");
const utils_1 = require("../../utils");
/**
 * The MicroserviceHealthIndicator is a health indicators
 * which is used for health checks related to microservices
 *
 * @publicApi
 * @module TerminusModule
 */
let MicroserviceHealthIndicator = class MicroserviceHealthIndicator extends __1.HealthIndicator {
    /**
     * Initializes the health indicator
     */
    constructor() {
        super();
        this.checkDependantPackages();
    }
    /**
     * Checks if the dependant packages are present
     */
    checkDependantPackages() {
        this.nestJsMicroservices = (0, utils_1.checkPackages)(['@nestjs/microservices'], this.constructor.name)[0];
    }
    pingMicroservice(options) {
        return __awaiter(this, void 0, void 0, function* () {
            const client = this.nestJsMicroservices.ClientProxyFactory.create(options);
            const checkConnection = () => __awaiter(this, void 0, void 0, function* () {
                yield client.connect();
                yield client.close();
            });
            return yield checkConnection();
        });
    }
    /**
     * Prepares and throw a HealthCheckError
     * @param key The key which will be used for the result object
     * @param error The thrown error
     * @param timeout The timeout in ms
     *
     * @throws {HealthCheckError}
     */
    generateError(key, error, timeout) {
        if (!error) {
            return;
        }
        if (error instanceof utils_1.TimeoutError) {
            throw new errors_1.TimeoutError(timeout, this.getStatus(key, false, {
                message: `timeout of ${timeout}ms exceeded`,
            }));
        }
        throw new health_check_error_1.HealthCheckError(error.message, this.getStatus(key, false, {
            message: error.message,
        }));
    }
    /**
     * Checks if the given microservice is up
     * @param key The key which will be used for the result object
     * @param options The options of the microservice
     *
     * @throws {HealthCheckError} If the microservice is not reachable
     *
     * @example
     * microservice.pingCheck<TcpClientOptions>('tcp', {
     *   transport: Transport.TCP,
     *   options: { host: 'localhost', port: 3001 },
     * })
     */
    pingCheck(key, options) {
        return __awaiter(this, void 0, void 0, function* () {
            let isHealthy = false;
            const timeout = options.timeout || 1000;
            if (options.transport === this.nestJsMicroservices.Transport.KAFKA) {
                options.options = Object.assign({ 
                    // We need to set the producerOnlyMode to true in order to speed
                    // up the connection process. https://github.com/nestjs/terminus/issues/1690
                    producerOnlyMode: true }, options.options);
            }
            try {
                yield (0, utils_1.promiseTimeout)(timeout, this.pingMicroservice(options));
                isHealthy = true;
            }
            catch (err) {
                if ((0, utils_1.isError)(err)) {
                    this.generateError(key, err, timeout);
                }
                const errorMsg = `${key} is not available`;
                throw new health_check_error_1.HealthCheckError(errorMsg, this.getStatus(key, false, { message: errorMsg }));
            }
            return this.getStatus(key, isHealthy);
        });
    }
};
exports.MicroserviceHealthIndicator = MicroserviceHealthIndicator;
exports.MicroserviceHealthIndicator = MicroserviceHealthIndicator = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.TRANSIENT }),
    __metadata("design:paramtypes", [])
], MicroserviceHealthIndicator);
//# sourceMappingURL=microservice.health.js.map