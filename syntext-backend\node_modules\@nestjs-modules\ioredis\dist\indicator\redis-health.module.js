"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisHealthModule = void 0;
const tslib_1 = require("tslib");
const common_1 = require("@nestjs/common");
const terminus_1 = require("@nestjs/terminus");
const redis_health_indicator_1 = require("./redis-health.indicator");
const redis_health_provider_1 = require("./redis-health.provider");
let RedisHealthModule = class RedisHealthModule {
};
exports.RedisHealthModule = RedisHealthModule;
exports.RedisHealthModule = RedisHealthModule = tslib_1.__decorate([
    (0, common_1.Module)({
        imports: [terminus_1.TerminusModule],
        providers: [
            redis_health_indicator_1.RedisHealthIndicator,
            redis_health_provider_1.redisHealthIndicatorProvider,
        ],
        exports: [redis_health_indicator_1.RedisHealthIndicator]
    })
], RedisHealthModule);
