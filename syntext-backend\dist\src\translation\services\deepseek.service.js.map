{"version": 3, "file": "deepseek.service.js", "sourceRoot": "", "sources": ["../../../../src/translation/services/deepseek.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,yCAA4C;AAC5C,2CAA+C;AAC/C,+BAAsC;AAgB/B,IAAM,eAAe,uBAArB,MAAM,eAAe;IAMP;IACA;IANF,MAAM,GAAG,IAAI,eAAM,CAAC,iBAAe,CAAC,IAAI,CAAC,CAAC;IAC1C,MAAM,CAAS;IACf,MAAM,CAAS;IAEhC,YACmB,WAAwB,EACxB,aAA4B;QAD5B,gBAAW,GAAX,WAAW,CAAa;QACxB,kBAAa,GAAb,aAAa,CAAe;QAE7C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC,IAAI,EAAE,CAAC;QACvE,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,kBAAkB,CAAC,IAAI,EAAE,CAAC;IACzE,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,OAAmC;QACrD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC,CAAC;YAEpD,MAAM,QAAQ,GAAG,MAAM,IAAA,qBAAc,EACnC,IAAI,CAAC,WAAW,CAAC,IAAI,CACnB,GAAG,IAAI,CAAC,MAAM,mBAAmB,EACjC;gBACE,KAAK,EAAE,eAAe;gBACtB,QAAQ,EAAE;oBACR;wBACE,IAAI,EAAE,QAAQ;wBACd,OAAO,EAAE,sDAAsD;qBAChE;oBACD;wBACE,IAAI,EAAE,MAAM;wBACZ,OAAO,EAAE,MAAM;qBAChB;iBACF;gBACD,WAAW,EAAE,GAAG;gBAChB,UAAU,EAAE,IAAI;gBAChB,MAAM,EAAE,KAAK;aACd,EACD;gBACE,OAAO,EAAE;oBACP,eAAe,EAAE,UAAU,IAAI,CAAC,MAAM,EAAE;oBACxC,cAAc,EAAE,kBAAkB;iBACnC;gBACD,OAAO,EAAE,KAAK;aACf,CACF,CACF,CAAC;YAEF,MAAM,cAAc,GAAG,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;YAE1E,IAAI,CAAC,cAAc,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;YAC3C,CAAC;YAED,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAG9C,MAAM,UAAU,GAAG,IAAI,CAAC,mBAAmB,CAAC,OAAO,CAAC,UAAU,EAAE,cAAc,CAAC,CAAC;YAEhF,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,4BAA4B,cAAc,IAAI,CAAC,CAAC;YAEhE,OAAO;gBACL,cAAc;gBACd,UAAU;gBACV,cAAc;aACf,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,IAAI,KAAK,CAAC,cAAc,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QACjD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAsC;QACzD,MAAM,OAAO,GAAkC,EAAE,CAAC;QAGlD,MAAM,SAAS,GAAG,CAAC,CAAC;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,IAAI,SAAS,EAAE,CAAC;YACpD,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,CAAC;YAC/C,MAAM,aAAa,GAAG,KAAK,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC;YAExE,IAAI,CAAC;gBACH,MAAM,YAAY,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;gBACtD,OAAO,CAAC,IAAI,CAAC,GAAG,YAAY,CAAC,CAAC;YAChC,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,sCAAsC,CAAC,GAAG,SAAS,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;gBACrF,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAEO,sBAAsB,CAAC,OAAmC;QAChE,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,cAAc,EAAE,OAAO,EAAE,GAAG,OAAO,CAAC;QAExE,IAAI,MAAM,GAAG,OAAO,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,QAAQ,UAAU,EAAE,CAAC;QAEzH,IAAI,OAAO,EAAE,CAAC;YACZ,MAAM,IAAI,aAAa,OAAO,EAAE,CAAC;QACnC,CAAC;QAED,MAAM,IAAI,0DAA0D,CAAC;QAErE,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,eAAe,CAAC,YAAoB;QAC1C,MAAM,WAAW,GAA2B;YAC1C,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,IAAI;YACV,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,MAAM;YACZ,IAAI,EAAE,KAAK;YACX,MAAM,EAAE,MAAM;SACf,CAAC;QAEF,OAAO,WAAW,CAAC,YAAY,CAAC,IAAI,YAAY,CAAC;IACnD,CAAC;IAEO,mBAAmB,CAAC,UAAkB,EAAE,cAAsB;QAEpE,IAAI,UAAU,GAAG,GAAG,CAAC;QAGrB,MAAM,WAAW,GAAG,cAAc,CAAC,MAAM,GAAG,UAAU,CAAC,MAAM,CAAC;QAC9D,IAAI,WAAW,GAAG,GAAG,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC;YACzC,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAGD,MAAM,aAAa,GAAG,CAAC,MAAM,EAAE,oBAAoB,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC;QACtE,MAAM,QAAQ,GAAG,aAAa,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAC5C,cAAc,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAC7D,CAAC;QAEF,IAAI,QAAQ,EAAE,CAAC;YACb,UAAU,IAAI,GAAG,CAAC;QACpB,CAAC;QAGD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;IAC9C,CAAC;CACF,CAAA;AAvJY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;qCAOqB,mBAAW;QACT,sBAAa;GAPpC,eAAe,CAuJ3B"}