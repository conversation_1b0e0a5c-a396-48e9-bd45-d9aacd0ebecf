import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { cn } from '../../utils/cn';

interface PageTransitionProps {
  children: React.ReactNode;
  className?: string;
  duration?: number;
  type?: 'fade' | 'slide' | 'scale' | 'blur';
}

const PageTransition: React.FC<PageTransitionProps> = ({
  children,
  className,
  duration = 300,
  type = 'fade',
}) => {
  const location = useLocation();
  const [isVisible, setIsVisible] = useState(false);
  const [displayLocation, setDisplayLocation] = useState(location);

  useEffect(() => {
    if (location !== displayLocation) {
      setIsVisible(false);
      
      const timer = setTimeout(() => {
        setDisplayLocation(location);
        setIsVisible(true);
      }, duration);

      return () => clearTimeout(timer);
    } else {
      setIsVisible(true);
    }
  }, [location, displayLocation, duration]);

  const getTransitionClasses = () => {
    const baseClasses = `transition-all duration-${duration} ease-in-out`;
    
    switch (type) {
      case 'slide':
        return cn(
          baseClasses,
          isVisible 
            ? 'translate-x-0 opacity-100' 
            : 'translate-x-4 opacity-0'
        );
      case 'scale':
        return cn(
          baseClasses,
          isVisible 
            ? 'scale-100 opacity-100' 
            : 'scale-95 opacity-0'
        );
      case 'blur':
        return cn(
          baseClasses,
          isVisible 
            ? 'blur-0 opacity-100' 
            : 'blur-sm opacity-0'
        );
      case 'fade':
      default:
        return cn(
          baseClasses,
          isVisible ? 'opacity-100' : 'opacity-0'
        );
    }
  };

  return (
    <div className={cn(getTransitionClasses(), className)}>
      {children}
    </div>
  );
};

// 路由级别的页面过渡包装器
export const RouteTransition: React.FC<{
  children: React.ReactNode;
  className?: string;
}> = ({ children, className }) => {
  return (
    <PageTransition type="fade" duration={200} className={className}>
      {children}
    </PageTransition>
  );
};

// 内容区域过渡
export const ContentTransition: React.FC<{
  children: React.ReactNode;
  show: boolean;
  className?: string;
  type?: 'fade' | 'slide' | 'scale';
}> = ({ children, show, className, type = 'fade' }) => {
  const getClasses = () => {
    const baseClasses = 'transition-all duration-300 ease-in-out';
    
    switch (type) {
      case 'slide':
        return cn(
          baseClasses,
          show 
            ? 'translate-y-0 opacity-100' 
            : '-translate-y-2 opacity-0'
        );
      case 'scale':
        return cn(
          baseClasses,
          show 
            ? 'scale-100 opacity-100' 
            : 'scale-95 opacity-0'
        );
      case 'fade':
      default:
        return cn(
          baseClasses,
          show ? 'opacity-100' : 'opacity-0'
        );
    }
  };

  return (
    <div className={cn(getClasses(), className)}>
      {children}
    </div>
  );
};

// 列表项动画
export const ListItemTransition: React.FC<{
  children: React.ReactNode;
  index: number;
  className?: string;
}> = ({ children, index, className }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const timer = setTimeout(() => {
      setIsVisible(true);
    }, index * 100); // 每个项目延迟100ms

    return () => clearTimeout(timer);
  }, [index]);

  return (
    <div
      className={cn(
        'transition-all duration-500 ease-out',
        isVisible 
          ? 'translate-y-0 opacity-100' 
          : 'translate-y-4 opacity-0',
        className
      )}
      style={{ transitionDelay: `${index * 50}ms` }}
    >
      {children}
    </div>
  );
};

// 悬停效果组件
export const HoverEffect: React.FC<{
  children: React.ReactNode;
  effect?: 'lift' | 'glow' | 'scale' | 'tilt';
  className?: string;
}> = ({ children, effect = 'lift', className }) => {
  const getHoverClasses = () => {
    switch (effect) {
      case 'glow':
        return 'hover:shadow-2xl hover:shadow-primary-500/25 transition-shadow duration-300';
      case 'scale':
        return 'hover:scale-105 transition-transform duration-300';
      case 'tilt':
        return 'hover:rotate-1 transition-transform duration-300';
      case 'lift':
      default:
        return 'hover:-translate-y-1 hover:shadow-lg transition-all duration-300';
    }
  };

  return (
    <div className={cn(getHoverClasses(), className)}>
      {children}
    </div>
  );
};

// 点击波纹效果
export const RippleEffect: React.FC<{
  children: React.ReactNode;
  className?: string;
  color?: string;
}> = ({ children, className, color = 'rgba(255, 255, 255, 0.6)' }) => {
  const [ripples, setRipples] = useState<Array<{
    x: number;
    y: number;
    id: number;
  }>>([]);

  const handleClick = (event: React.MouseEvent<HTMLDivElement>) => {
    const rect = event.currentTarget.getBoundingClientRect();
    const x = event.clientX - rect.left;
    const y = event.clientY - rect.top;
    const id = Date.now();

    setRipples(prev => [...prev, { x, y, id }]);

    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== id));
    }, 600);
  };

  return (
    <div
      className={cn('relative overflow-hidden', className)}
      onClick={handleClick}
    >
      {children}
      {ripples.map(ripple => (
        <span
          key={ripple.id}
          className="absolute rounded-full animate-ping pointer-events-none"
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
            backgroundColor: color,
            animation: 'ripple 0.6s linear',
          }}
        />
      ))}
    </div>
  );
};

// 加载骨架动画
export const SkeletonAnimation: React.FC<{
  className?: string;
  lines?: number;
  avatar?: boolean;
}> = ({ className, lines = 3, avatar = false }) => {
  return (
    <div className={cn('animate-pulse', className)}>
      {avatar && (
        <div className="flex items-center space-x-4 mb-4">
          <div className="w-10 h-10 bg-secondary-300 rounded-full"></div>
          <div className="flex-1">
            <div className="h-4 bg-secondary-300 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-secondary-300 rounded w-1/3"></div>
          </div>
        </div>
      )}
      <div className="space-y-3">
        {Array.from({ length: lines }).map((_, index) => (
          <div
            key={index}
            className={cn(
              'h-4 bg-secondary-300 rounded',
              index === lines - 1 ? 'w-2/3' : 'w-full'
            )}
          ></div>
        ))}
      </div>
    </div>
  );
};

export default PageTransition;
