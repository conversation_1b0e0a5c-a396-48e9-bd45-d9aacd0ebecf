// 开发环境专用的简化性能监控工具

export class DevPerformanceMonitor {
  private static instance: DevPerformanceMonitor;
  private metrics: Map<string, number> = new Map();
  private startTimes: Map<string, number> = new Map();
  private isEnabled: boolean;

  constructor() {
    // 只在开发环境启用
    this.isEnabled = import.meta.env.DEV;
  }

  static getInstance(): DevPerformanceMonitor {
    if (!DevPerformanceMonitor.instance) {
      DevPerformanceMonitor.instance = new DevPerformanceMonitor();
    }
    return DevPerformanceMonitor.instance;
  }

  // 开始计时
  startTiming(label: string): void {
    if (!this.isEnabled) return;
    this.startTimes.set(label, performance.now());
  }

  // 结束计时
  endTiming(label: string): number {
    if (!this.isEnabled) return 0;
    
    const startTime = this.startTimes.get(label);
    if (!startTime) {
      // 只在开发环境显示警告
      if (import.meta.env.DEV) {
        console.warn(`⚠️ No start time found for ${label}`);
      }
      return 0;
    }

    const duration = performance.now() - startTime;
    this.metrics.set(label, duration);
    this.startTimes.delete(label);

    // 在开发环境输出性能指标
    if (import.meta.env.DEV) {
      console.log(`⏱️ ${label}: ${duration.toFixed(2)}ms`);
    }

    return duration;
  }

  // 获取指标
  getMetric(label: string): number | undefined {
    return this.metrics.get(label);
  }

  // 获取所有指标
  getAllMetrics(): Map<string, number> {
    return new Map(this.metrics);
  }

  // 清除指标
  clearMetrics(): void {
    this.metrics.clear();
    this.startTimes.clear();
  }

  // 记录自定义指标
  recordMetric(label: string, value: number): void {
    if (!this.isEnabled) return;
    this.metrics.set(label, value);
  }

  // 安全的Web Vitals监控
  initWebVitals(): void {
    if (!this.isEnabled || !('PerformanceObserver' in window)) {
      return;
    }

    try {
      // 只监控基本的性能指标，避免不支持的类型
      const supportedTypes = PerformanceObserver.supportedEntryTypes || [];
      
      // 监控导航时间
      if (supportedTypes.includes('navigation')) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const navEntry = entry as PerformanceNavigationTiming;
            this.recordMetric('DOM_Content_Loaded', navEntry.domContentLoadedEventEnd - navEntry.startTime);
            this.recordMetric('Load_Complete', navEntry.loadEventEnd - navEntry.startTime);
          }
        });
        observer.observe({ entryTypes: ['navigation'] });
      }

      // 监控资源加载时间
      if (supportedTypes.includes('resource')) {
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            const resourceEntry = entry as PerformanceResourceTiming;
            if (resourceEntry.duration > 100) { // 只记录较慢的资源
              this.recordMetric(`Resource_${resourceEntry.name.split('/').pop()}`, resourceEntry.duration);
            }
          }
        });
        observer.observe({ entryTypes: ['resource'] });
      }

    } catch (error) {
      // 静默处理错误，避免控制台噪音
      if (import.meta.env.DEV) {
        console.warn('Performance monitoring initialization failed:', error);
      }
    }
  }

  // 导出性能报告
  exportReport(): string {
    if (!this.isEnabled) return '{}';
    
    const report = {
      timestamp: new Date().toISOString(),
      metrics: Object.fromEntries(this.getAllMetrics()),
      userAgent: navigator.userAgent,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    };

    return JSON.stringify(report, null, 2);
  }
}

// React组件性能监控Hook - 简化版
import { useEffect, useRef, useCallback } from 'react';

export function useDevPerformanceMonitor(componentName: string) {
  const renderStartTime = useRef<number>(0);
  const monitor = DevPerformanceMonitor.getInstance();

  useEffect(() => {
    renderStartTime.current = performance.now();
    
    return () => {
      const renderTime = performance.now() - renderStartTime.current;
      monitor.recordMetric(`${componentName}_render`, renderTime);
    };
  });

  const startOperation = useCallback((operationName: string) => {
    monitor.startTiming(`${componentName}_${operationName}`);
  }, [componentName, monitor]);

  const endOperation = useCallback((operationName: string) => {
    return monitor.endTiming(`${componentName}_${operationName}`);
  }, [componentName, monitor]);

  return { startOperation, endOperation };
}

// 全局实例
export const devPerformanceMonitor = DevPerformanceMonitor.getInstance();

// 自动初始化
if (typeof window !== 'undefined') {
  devPerformanceMonitor.initWebVitals();
}
