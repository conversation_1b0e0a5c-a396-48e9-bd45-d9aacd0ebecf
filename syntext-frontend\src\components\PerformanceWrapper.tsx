import React, { memo, forwardRef } from 'react';
import { usePerformanceMonitor } from '../utils/performance';

interface PerformanceWrapperProps {
  name: string;
  children: React.ReactNode;
  className?: string;
  enableMemoryMonitoring?: boolean;
  enableRenderCount?: boolean;
}

// 性能包装器组件
export const PerformanceWrapper: React.FC<PerformanceWrapperProps> = memo(({
  name,
  children,
  className,
  enableMemoryMonitoring = false,
  enableRenderCount = false
}) => {
  const { startOperation, endOperation } = usePerformanceMonitor(name);
  const renderCountRef = React.useRef(0);

  React.useEffect(() => {
    startOperation('component_mount');
    
    return () => {
      endOperation('component_mount');
    };
  }, []);

  React.useEffect(() => {
    if (enableRenderCount) {
      renderCountRef.current += 1;
      console.log(`${name} rendered ${renderCountRef.current} times`);
    }
  });

  React.useEffect(() => {
    if (enableMemoryMonitoring) {
      const checkMemory = () => {
        if ('memory' in performance) {
          const memory = (performance as any).memory;
          console.log(`${name} Memory Usage:`, {
            used: Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB',
            total: Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB'
          });
        }
      };

      const interval = setInterval(checkMemory, 5000);
      return () => clearInterval(interval);
    }
  }, [name, enableMemoryMonitoring]);

  return (
    <div className={className} data-performance-component={name}>
      {children}
    </div>
  );
});

PerformanceWrapper.displayName = 'PerformanceWrapper';

// 高阶组件版本
export function withPerformanceMonitoring<P extends object>(
  Component: React.ComponentType<P>,
  componentName?: string
) {
  const WrappedComponent = forwardRef<any, P>((props, ref) => {
    const name = componentName || Component.displayName || Component.name || 'Anonymous';
    const { startOperation, endOperation } = usePerformanceMonitor(name);

    React.useEffect(() => {
      startOperation('hoc_mount');
      return () => {
        endOperation('hoc_mount');
      };
    }, []);

    return <Component {...(props as any)} ref={ref} />;
  });

  WrappedComponent.displayName = `withPerformanceMonitoring(${componentName || Component.displayName || Component.name})`;
  
  return memo(WrappedComponent);
}

export default PerformanceWrapper;