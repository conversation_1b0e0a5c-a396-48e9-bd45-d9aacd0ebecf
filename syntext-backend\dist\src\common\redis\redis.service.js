"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.RedisService = void 0;
const common_1 = require("@nestjs/common");
const ioredis_1 = require("ioredis");
let RedisService = class RedisService {
    redis;
    constructor(redis) {
        this.redis = redis;
    }
    async get(key) {
        return this.redis.get(key);
    }
    async set(key, value, ttl) {
        if (ttl) {
            await this.redis.setex(key, ttl, value);
        }
        else {
            await this.redis.set(key, value);
        }
    }
    async del(key) {
        await this.redis.del(key);
    }
    async exists(key) {
        const result = await this.redis.exists(key);
        return result === 1;
    }
    async expire(key, ttl) {
        await this.redis.expire(key, ttl);
    }
    async ttl(key) {
        return this.redis.ttl(key);
    }
    async hget(key, field) {
        return this.redis.hget(key, field);
    }
    async hset(key, field, value) {
        await this.redis.hset(key, field, value);
    }
    async hgetall(key) {
        return this.redis.hgetall(key);
    }
    async hdel(key, field) {
        await this.redis.hdel(key, field);
    }
    async sadd(key, member) {
        await this.redis.sadd(key, member);
    }
    async srem(key, member) {
        await this.redis.srem(key, member);
    }
    async smembers(key) {
        return this.redis.smembers(key);
    }
    async sismember(key, member) {
        const result = await this.redis.sismember(key, member);
        return result === 1;
    }
    async cacheTranslation(sourceText, sourceLang, targetLang, translatedText, ttl = 3600) {
        const key = this.getTranslationCacheKey(sourceText, sourceLang, targetLang);
        await this.set(key, translatedText, ttl);
    }
    async getCachedTranslation(sourceText, sourceLang, targetLang) {
        const key = this.getTranslationCacheKey(sourceText, sourceLang, targetLang);
        return this.get(key);
    }
    getTranslationCacheKey(sourceText, sourceLang, targetLang) {
        const hash = Buffer.from(sourceText).toString('base64').slice(0, 32);
        return `translation:${sourceLang}:${targetLang}:${hash}`;
    }
    async incrementRateLimit(identifier, windowSize = 60, limit = 100) {
        const key = `rate_limit:${identifier}`;
        const now = Math.floor(Date.now() / 1000);
        const windowStart = now - (now % windowSize);
        const windowKey = `${key}:${windowStart}`;
        const count = await this.redis.incr(windowKey);
        if (count === 1) {
            await this.redis.expire(windowKey, windowSize);
        }
        const remaining = Math.max(0, limit - count);
        const resetTime = windowStart + windowSize;
        return { count, remaining, resetTime };
    }
    async checkRateLimit(identifier, windowSize = 60) {
        const key = `rate_limit:${identifier}`;
        const now = Math.floor(Date.now() / 1000);
        const windowStart = now - (now % windowSize);
        const windowKey = `${key}:${windowStart}`;
        const count = await this.redis.get(windowKey);
        return count ? parseInt(count, 10) : 0;
    }
    async setSession(sessionId, data, ttl = 86400) {
        const key = `session:${sessionId}`;
        await this.set(key, JSON.stringify(data), ttl);
    }
    async getSession(sessionId) {
        const key = `session:${sessionId}`;
        const data = await this.get(key);
        return data ? JSON.parse(data) : null;
    }
    async deleteSession(sessionId) {
        const key = `session:${sessionId}`;
        await this.del(key);
    }
    async setVerificationCode(email, code, ttl = 300) {
        const key = `verification:${email}`;
        await this.set(key, code, ttl);
    }
    async getVerificationCode(email) {
        const key = `verification:${email}`;
        return this.get(key);
    }
    async deleteVerificationCode(email) {
        const key = `verification:${email}`;
        await this.del(key);
    }
    async incrementUserUsage(userId, type, period = 'monthly') {
        const now = new Date();
        const key = period === 'monthly'
            ? `usage:${type}:${userId}:${now.getFullYear()}-${now.getMonth() + 1}`
            : `usage:${type}:${userId}:${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}`;
        const count = await this.redis.incr(key);
        const ttl = period === 'monthly' ? 86400 * 32 : 86400 * 2;
        if (count === 1) {
            await this.redis.expire(key, ttl);
        }
        return count;
    }
    async getUserUsage(userId, type, period = 'monthly') {
        const now = new Date();
        const key = period === 'monthly'
            ? `usage:${type}:${userId}:${now.getFullYear()}-${now.getMonth() + 1}`
            : `usage:${type}:${userId}:${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}`;
        const count = await this.get(key);
        return count ? parseInt(count, 10) : 0;
    }
};
exports.RedisService = RedisService;
exports.RedisService = RedisService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)('REDIS_CLIENT')),
    __metadata("design:paramtypes", [ioredis_1.Redis])
], RedisService);
//# sourceMappingURL=redis.service.js.map