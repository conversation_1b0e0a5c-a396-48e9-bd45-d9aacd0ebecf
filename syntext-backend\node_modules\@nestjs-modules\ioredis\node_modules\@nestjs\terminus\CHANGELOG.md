

# [10.2.0](https://github.com/nestjs/terminus/compare/10.2.0-beta.0...10.2.0) (2023-11-27)

# [10.2.0-beta.0](https://github.com/nestjs/terminus/compare/10.1.1...10.2.0-beta.0) (2023-11-27)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.9.11 ([8b9477e](https://github.com/nestjs/terminus/commit/8b9477e7fb8ca1338a777a6ec766cf3af68b6e4f))
* **deps:** update dependency @grpc/grpc-js to v1.9.3 ([8205d8e](https://github.com/nestjs/terminus/commit/8205d8e2eee975ac732a9d4cbd690ce9bba17fae))
* **deps:** update dependency @grpc/proto-loader to v0.7.10 ([#2398](https://github.com/nestjs/terminus/issues/2398)) ([c16da49](https://github.com/nestjs/terminus/commit/c16da49191df6d54fc05aad8591d5d51b6fd75e3))
* **deps:** update dependency @mikro-orm/nestjs to v5.2.2 ([0c72183](https://github.com/nestjs/terminus/commit/0c72183db557664745c6e65553181d9a5323143a))
* **deps:** update dependency @nestjs/axios to v3 ([6f1d4bb](https://github.com/nestjs/terminus/commit/6f1d4bbf24f45f5ad72eb691a4ef351a5ffe9b60))
* **deps:** update dependency @nestjs/axios to v3.0.1 ([#2438](https://github.com/nestjs/terminus/issues/2438)) ([7239bb0](https://github.com/nestjs/terminus/commit/7239bb0abf31e0856b11785751d4966df5e25874))
* **deps:** update dependency @nestjs/mongoose to v10.0.1 ([9c4baef](https://github.com/nestjs/terminus/commit/9c4baef9cbc9496e99927cc36bf0601054a18085))
* **deps:** update dependency @nestjs/mongoose to v10.0.2 ([8e70686](https://github.com/nestjs/terminus/commit/8e706863a546e89b16e56cd7ecc7b21d07fbfb2d))
* **deps:** update dependency @nestjs/sequelize to v10 ([100d8fa](https://github.com/nestjs/terminus/commit/100d8fa4874e7eed2fb181f7e80853a913b8ffe2))
* **deps:** update dependency @nestjs/typeorm to v10 ([77972fe](https://github.com/nestjs/terminus/commit/77972fe46b23185baae5d59cd6824af91a43ea17))
* **deps:** update dependency @nestjs/typeorm to v10.0.1 ([3cf5c15](https://github.com/nestjs/terminus/commit/3cf5c15d4f915e0f9a35ac7959b380e25c3c7c9e))
* **deps:** update dependency mongoose to v7.6.4 ([51cb375](https://github.com/nestjs/terminus/commit/51cb375a28fde30258c59684503127c5a2cd4cee))
* **deps:** update dependency mongoose to v7.6.5 ([af11baf](https://github.com/nestjs/terminus/commit/af11baf292876927b7268fab1cf7ca67a4a813d1))
* **deps:** update dependency redis to v4.6.8 ([5c35747](https://github.com/nestjs/terminus/commit/5c357473655329a6e34936d49a29e432ab2fa624))
* **deps:** update dependency rimraf to v5 ([46cd5be](https://github.com/nestjs/terminus/commit/46cd5be7f9f6f09c1a93d0cd08d982a63817ed5a))
* **deps:** update dependency sequelize to v6.33.0 ([b362509](https://github.com/nestjs/terminus/commit/b362509c792eb9262340026f3b279dfa59d27182))
* **deps:** update dependency typeorm to v0.3.17 ([d07b83a](https://github.com/nestjs/terminus/commit/d07b83a98fa52409536d9b8ac777e6b54e96d85f))
* **deps:** update mikro-orm monorepo to v5.8.1 ([2a0d68a](https://github.com/nestjs/terminus/commit/2a0d68a9838259b280c235560f243c91b615f558))
* **deps:** update nest monorepo ([c25b594](https://github.com/nestjs/terminus/commit/c25b5946d13800e028d4ae56ecbf099f2c7c792b))
* **deps:** update prisma monorepo to v4.16.2 ([3933016](https://github.com/nestjs/terminus/commit/3933016d306e17d60fc443466485ff501d0e2992))
* Fixed typeorm mongodb health check fails with mongodb>=5.0 ([#2399](https://github.com/nestjs/terminus/issues/2399)) ([b6026c2](https://github.com/nestjs/terminus/commit/b6026c2e863c0a509177749cd6ee82a0a5bc2030))


### Features

* graceful shutdown timeout ([#2422](https://github.com/nestjs/terminus/issues/2422)) ([cc3d402](https://github.com/nestjs/terminus/commit/cc3d402294f7da1cdc91d4f475c1280c17855cb1))

## [10.1.1](https://github.com/nestjs/terminus/compare/10.1.1-beta.0...10.1.1) (2023-09-14)

## [10.1.1-beta.0](https://github.com/nestjs/terminus/compare/10.1.0...10.1.1-beta.0) (2023-09-14)


### Bug Fixes

* @nestjs/microservices as direct dependency ([acfdfb3](https://github.com/nestjs/terminus/commit/acfdfb32ec085168e2dbc838efbc15afa024c4da))

# [10.1.0](https://github.com/nestjs/terminus/compare/10.0.1...10.1.0) (2023-09-14)


### Bug Fixes

* **deps:** update dependency @grpc/proto-loader to v0.7.9 ([#2309](https://github.com/nestjs/terminus/issues/2309)) ([e049c7e](https://github.com/nestjs/terminus/commit/e049c7ecefc7bd0e2cb67665d5420e44b000481d))
* **deps:** update dependency @mikro-orm/nestjs to v5.2.1 ([#2316](https://github.com/nestjs/terminus/issues/2316)) ([64b0e69](https://github.com/nestjs/terminus/commit/64b0e6950708ccb3a574becad2ddce62065bf68b))
* **deps:** update dependency mysql2 to v3.6.1 ([5c9c2b1](https://github.com/nestjs/terminus/commit/5c9c2b142a534e5c20c28c1e7c9f2b933ba554e0))
* **deps:** update dependency redis to v4.6.7 ([#2283](https://github.com/nestjs/terminus/issues/2283)) ([c8be03b](https://github.com/nestjs/terminus/commit/c8be03bcc41fb7863cf5cb9141ca7a4fda588bb8))
* **deps:** update dependency rimraf to v4.4.1 ([#2319](https://github.com/nestjs/terminus/issues/2319)) ([43b194e](https://github.com/nestjs/terminus/commit/43b194e7af6dad8f057c471b0b17d19f0b27d579))
* **deps:** update dependency rxjs to v7.8.1 ([#2320](https://github.com/nestjs/terminus/issues/2320)) ([706d8e2](https://github.com/nestjs/terminus/commit/706d8e20658dbbb119b01894a246d09adf94a81f))
* fails if unexpected error type thrown ([b641d2e](https://github.com/nestjs/terminus/commit/b641d2e1867563a1f468fbd4c28606ba17ed396e)), closes [#2256](https://github.com/nestjs/terminus/issues/2256)
* **grpc:** Memory leak due to open channel references ([781c00e](https://github.com/nestjs/terminus/commit/781c00e8c4a53f5a8227bd21d0dc5eae72641d1a)), closes [#2329](https://github.com/nestjs/terminus/issues/2329)
* **microservice:** connect to kafka with `producerOnlyMode` per default ([c6d8f7c](https://github.com/nestjs/terminus/commit/c6d8f7c9b77c574e899183f87e88e2a06fc3bbb6)), closes [#1690](https://github.com/nestjs/terminus/issues/1690)
* **mikro-orm.health:** mikro-orm connection `type` is deprecated ([1c0eff9](https://github.com/nestjs/terminus/commit/1c0eff92f87121499f3e76231e14b256ed569f5b)), closes [#2325](https://github.com/nestjs/terminus/issues/2325)


### Features

* Do not cache health checks per default ([#2335](https://github.com/nestjs/terminus/issues/2335)) ([262c808](https://github.com/nestjs/terminus/commit/262c808f193591bbff8d6efb82c891acd25d33c7)), closes [#2328](https://github.com/nestjs/terminus/issues/2328)

## [10.0.1](https://github.com/nestjs/terminus/compare/10.0.0...10.0.1) (2023-06-17)


### Bug Fixes

* **prisma:** add `@Injectable` decorator ([25893bd](https://github.com/nestjs/terminus/commit/25893bde9aa17cda07e39daad211ca5a782535de))

# [10.0.0](https://github.com/nestjs/terminus/compare/9.2.2...10.0.0) (2023-06-17)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.8.14 ([#2262](https://github.com/nestjs/terminus/issues/2262)) ([86152cc](https://github.com/nestjs/terminus/commit/86152cc617d07e3ed902c52c8557247ce0957a77))
* **deps:** update dependency @grpc/grpc-js to v1.8.15 ([#2280](https://github.com/nestjs/terminus/issues/2280)) ([6ecdc1f](https://github.com/nestjs/terminus/commit/6ecdc1f243948f62375d9e0da812fcd85e676e8d))
* **deps:** update dependency @grpc/proto-loader to v0.7.6 ([#2263](https://github.com/nestjs/terminus/issues/2263)) ([638a7cd](https://github.com/nestjs/terminus/commit/638a7cd04ffc504640c00bba1390dca1c659cf2c))
* **deps:** update dependency @grpc/proto-loader to v0.7.7 ([#2268](https://github.com/nestjs/terminus/issues/2268)) ([48d2d8d](https://github.com/nestjs/terminus/commit/48d2d8d168145b47032a67a7456f8636f1f71d40))
* **deps:** update dependency @mikro-orm/nestjs to v5.1.8 ([#2265](https://github.com/nestjs/terminus/issues/2265)) ([4bdb17d](https://github.com/nestjs/terminus/commit/4bdb17d9ab2c151cfe4a91dbc1500d78fb4f9656))
* **deps:** update dependency @nestjs/mongoose to v9.2.2 ([#2267](https://github.com/nestjs/terminus/issues/2267)) ([d97deb7](https://github.com/nestjs/terminus/commit/d97deb762f5ec1035b8366dda64763d92de6562b))
* **deps:** update dependency @nestjs/sequelize to v9.0.2 ([#2269](https://github.com/nestjs/terminus/issues/2269)) ([3de1168](https://github.com/nestjs/terminus/commit/3de11681f4520b8a17498877fb80bdde02256a99))
* **deps:** update dependency ioredis to v5.3.2 ([#2281](https://github.com/nestjs/terminus/issues/2281)) ([3b58611](https://github.com/nestjs/terminus/commit/3b58611d7b1a088b7f76d19f5c4ac6b8ddf3b45c))
* **deps:** update dependency mysql2 to v3.2.4 ([#2216](https://github.com/nestjs/terminus/issues/2216)) ([38c9593](https://github.com/nestjs/terminus/commit/38c95939a46cc73face8e2d3f780658068629395))


* v10.0.0 (#2282) ([291bbed](https://github.com/nestjs/terminus/commit/291bbed71e42e79060e3f04d700c3686d9f4e0d5)), closes [#2282](https://github.com/nestjs/terminus/issues/2282) [#2250](https://github.com/nestjs/terminus/issues/2250)


### BREAKING CHANGES

* drop support for Node v12

* chore: fix prisma sample

* chore: add prisma client as optional peer dependency

* refactor(prisma): rename PrismaORM to PrismaHealthIndicator

* chore(): release v10.0.0-beta.0

* chore: fix format

* chore: add node v18 and v20

* chore: drop support for node v14
* drop support for node v14

* feat: upgrade to nest v10

* feat(deps): upgrade TypeScript to v5

* chore: update dependencies

* feat(disk): prettify type information

* chore(): release v10.0.0-beta.1

* Revert "feat(disk): prettify type information"

This reverts commit e0b13aac8069bb510c508783171aad2a87444fb1.

* chore: use --legacy-peer-deps in ci

Revert once https://github.com/mikro-orm/nestjs/issues/122 is resolved

* chore: remove prisma timeout

* chore: add debian openssl for prisma

# [10.0.0-beta.1](https://github.com/nestjs/terminus/compare/9.2.2...10.0.0-beta.1) (2023-06-16)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.8.14 ([#2262](https://github.com/nestjs/terminus/issues/2262)) ([86152cc](https://github.com/nestjs/terminus/commit/86152cc617d07e3ed902c52c8557247ce0957a77))
* **deps:** update dependency @grpc/grpc-js to v1.8.15 ([#2280](https://github.com/nestjs/terminus/issues/2280)) ([6ecdc1f](https://github.com/nestjs/terminus/commit/6ecdc1f243948f62375d9e0da812fcd85e676e8d))
* **deps:** update dependency @grpc/proto-loader to v0.7.6 ([#2263](https://github.com/nestjs/terminus/issues/2263)) ([638a7cd](https://github.com/nestjs/terminus/commit/638a7cd04ffc504640c00bba1390dca1c659cf2c))
* **deps:** update dependency @grpc/proto-loader to v0.7.7 ([#2268](https://github.com/nestjs/terminus/issues/2268)) ([48d2d8d](https://github.com/nestjs/terminus/commit/48d2d8d168145b47032a67a7456f8636f1f71d40))
* **deps:** update dependency @mikro-orm/nestjs to v5.1.8 ([#2265](https://github.com/nestjs/terminus/issues/2265)) ([4bdb17d](https://github.com/nestjs/terminus/commit/4bdb17d9ab2c151cfe4a91dbc1500d78fb4f9656))
* **deps:** update dependency @nestjs/mongoose to v9.2.2 ([#2267](https://github.com/nestjs/terminus/issues/2267)) ([d97deb7](https://github.com/nestjs/terminus/commit/d97deb762f5ec1035b8366dda64763d92de6562b))
* **deps:** update dependency @nestjs/sequelize to v9.0.2 ([#2269](https://github.com/nestjs/terminus/issues/2269)) ([3de1168](https://github.com/nestjs/terminus/commit/3de11681f4520b8a17498877fb80bdde02256a99))
* **deps:** update dependency ioredis to v5.3.2 ([#2281](https://github.com/nestjs/terminus/issues/2281)) ([3b58611](https://github.com/nestjs/terminus/commit/3b58611d7b1a088b7f76d19f5c4ac6b8ddf3b45c))
* **deps:** update dependency mysql2 to v3.2.4 ([#2216](https://github.com/nestjs/terminus/issues/2216)) ([38c9593](https://github.com/nestjs/terminus/commit/38c95939a46cc73face8e2d3f780658068629395))


### chore

* drop support for Node v12 ([260d170](https://github.com/nestjs/terminus/commit/260d170cb9c45386d7e79e367177297d874d4cc0))
* drop support for node v14 ([5f15d10](https://github.com/nestjs/terminus/commit/5f15d10bbb1559eb151c32c8153b46121bdbbfba))


### Features

* **deps:** upgrade TypeScript to v5 ([1e9d40e](https://github.com/nestjs/terminus/commit/1e9d40e301f9ab1943c69527cff88af23bc7ddaf))
* **disk:** prettify type information ([e0b13aa](https://github.com/nestjs/terminus/commit/e0b13aac8069bb510c508783171aad2a87444fb1))
* health indicator for Prisma ORM ([#2250](https://github.com/nestjs/terminus/issues/2250)) ([6960af6](https://github.com/nestjs/terminus/commit/6960af6eb4780af92a4f2097527df69d6c745bdb))
* upgrade to nest v10 ([b1e4aa2](https://github.com/nestjs/terminus/commit/b1e4aa23134a793428f5f286f46865916d8c6e7a))


### BREAKING CHANGES

* drop support for node v14
* drop support for Node v12

# [10.0.0-beta.0](https://github.com/nestjs/terminus/compare/9.2.2...10.0.0-beta.0) (2023-04-25)


### chore

* drop support for Node v12 ([beee03c](https://github.com/nestjs/terminus/commit/beee03cd9d01f8b936c3cdc2e61406ef1755dd8f))


### Features

* health indicator for Prisma ORM ([#2250](https://github.com/nestjs/terminus/issues/2250)) ([bef105d](https://github.com/nestjs/terminus/commit/bef105d7fb1ccf4be373e854fb918cc5a446687f))


### BREAKING CHANGES

* drop support for Node v12

## [9.2.2](https://github.com/nestjs/terminus/compare/9.2.1...9.2.2) (2023-03-27)


### Bug Fixes

* **deps:** update dependency mongoose to v6.9.2 ([62433b6](https://github.com/nestjs/terminus/commit/62433b6e0cd1b89e902a05fa16881985a8e00089))
* missing setContext when using logger: false ([9274056](https://github.com/nestjs/terminus/commit/92740566d3476694b31c2fda72bdbf6e6e099568))

## [9.2.1](https://github.com/nestjs/terminus/compare/9.2.0...9.2.1) (2023-02-15)


### Bug Fixes

* **deps:** update dependency ioredis to v5.3.0 ([7969397](https://github.com/nestjs/terminus/commit/7969397059b7305906a8450ea582bc8ee24ce590))
* **deps:** update dependency mongoose to v6.9.0 ([d3cc5ba](https://github.com/nestjs/terminus/commit/d3cc5baf30388ec425c4231b1dd014f4b62b9822))
* **deps:** update dependency redis to v4.6.4 ([ef505b9](https://github.com/nestjs/terminus/commit/ef505b908001bc7039735763b21791e15e9717da))
* **deps:** update nest monorepo ([0ebac8a](https://github.com/nestjs/terminus/commit/0ebac8a92138e7657f108663ec615e5bff2ea20c))
* Fix crash when setting logger to false ([3d417fd](https://github.com/nestjs/terminus/commit/3d417fdd1c6fcbf789ccb5db49823efe8436a8e2))

# [9.2.0](https://github.com/nestjs/terminus/compare/9.2.0-beta.0...9.2.0) (2023-02-03)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.8.7 ([aac1ef4](https://github.com/nestjs/terminus/commit/aac1ef43ba70c9397d4188228ec00aa28315135f))
* **deps:** update dependency rimraf to v4.1.2 ([682b17d](https://github.com/nestjs/terminus/commit/682b17d4437b1a76fe3d7e577121b628b40b6ab1))

# [9.2.0-beta.0](https://github.com/nestjs/terminus/compare/9.1.4...9.2.0-beta.0) (2023-01-27)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.8.4 ([3094665](https://github.com/nestjs/terminus/commit/3094665bda238ee97772e82fd65afd297b55a6d7))
* **deps:** update dependency @grpc/proto-loader to v0.7.4 ([ab21f60](https://github.com/nestjs/terminus/commit/ab21f60c07a508adc2b9c477b90558290523305c))
* **deps:** update dependency @nestjs/axios to v1 ([85468a6](https://github.com/nestjs/terminus/commit/85468a6845553f90873321d75cbddf251ebeeb27))
* **deps:** update dependency @nestjs/axios to v1.0.1 ([d57f7f1](https://github.com/nestjs/terminus/commit/d57f7f1fb2a0c89d39430d0ca68cca60f930958e))
* **deps:** update dependency @nestjs/terminus to v9.1.4 ([9d12b73](https://github.com/nestjs/terminus/commit/9d12b73dd86398e723607136aae73472d7d71fa1))
* **deps:** update dependency ioredis to v5.2.5 ([35572d9](https://github.com/nestjs/terminus/commit/35572d9af9609ab014171dfe67ce000a3709d452))
* **deps:** update dependency mongoose to v6.7.4 ([fedafa5](https://github.com/nestjs/terminus/commit/fedafa50b930b56571eaa34199abd8a3024b4d30))
* **deps:** update dependency mongoose to v6.8.3 ([8d6b0c4](https://github.com/nestjs/terminus/commit/8d6b0c421d0a1bdde4dcf9d52811f020c3dfcf13))
* **deps:** update dependency mongoose to v6.8.4 ([a782dd3](https://github.com/nestjs/terminus/commit/a782dd3330e385e47df305c471edfa0716e0b599))
* **deps:** update dependency mysql2 to v3 ([ba0879a](https://github.com/nestjs/terminus/commit/ba0879ade1e6dedf76833364e62dde34771393bd))
* **deps:** update dependency redis to v4.5.1 ([f80cb94](https://github.com/nestjs/terminus/commit/f80cb942855a8b4e8f18dacafa6dda315d7bdfcf))
* **deps:** update dependency rimraf to v4 ([27a7f01](https://github.com/nestjs/terminus/commit/27a7f0170f452b47bbcf46f4a62721b16caa4c0b))
* **deps:** update dependency rxjs to v7.8.0 ([c3c9bf9](https://github.com/nestjs/terminus/commit/c3c9bf95a2f92f1c6fc3cdb665d5b4ba64c7aa83))
* **deps:** update dependency sequelize to v6.28.0 ([5e3b96f](https://github.com/nestjs/terminus/commit/5e3b96f24ad49eb9442cecf96b492dcb8c0cb4ab))
* **deps:** update dependency typeorm to v0.3.11 ([b6cb0d5](https://github.com/nestjs/terminus/commit/b6cb0d5ac494b842917961d2c33c84f22f969e39))
* **deps:** update nest monorepo to v9.2.1 ([85b8a94](https://github.com/nestjs/terminus/commit/85b8a94481f4c4837e88bc7729b67faedc0fa93d))


### Features

* add custom logger ([e88c1e2](https://github.com/nestjs/terminus/commit/e88c1e2e9be0d8c6fb473ba6f814a31e2f84d3be)), closes [#2068](https://github.com/nestjs/terminus/issues/2068)

## [9.1.4](https://github.com/nestjs/terminus/compare/9.1.3...9.1.4) (2022-11-24)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.7.3 ([afb87eb](https://github.com/nestjs/terminus/commit/afb87eba0389bb43cc8c4b3c88dfdfea908f024d))
* **deps:** update dependency @grpc/proto-loader to v0.7.3 ([f52eafb](https://github.com/nestjs/terminus/commit/f52eafb0521cc7fbc28b1e2d29b8f0ac67083ee8))
* **deps:** update dependency @nestjs/mongoose to v9.2.1 ([a1ca732](https://github.com/nestjs/terminus/commit/a1ca732215a36bb09576bfa3d07f8ffdc0a33320))
* **deps:** update dependency ioredis to v5.2.4 ([314e647](https://github.com/nestjs/terminus/commit/314e6472556ed0c72ece9183e8c509f718bac898))
* **deps:** update dependency mongoose to v6.7.3 ([3568a9e](https://github.com/nestjs/terminus/commit/3568a9ea05835f06eda531af5dab281ed9240ba3))
* **deps:** update dependency redis to v4.5.0 ([4f02bcd](https://github.com/nestjs/terminus/commit/4f02bcd14e90edb3fe33d69896f307f1802fd748))
* **deps:** update dependency sequelize to v6.25.8 ([49bee09](https://github.com/nestjs/terminus/commit/49bee09c84f8dd8a4b543e428333c5d538b72aab))
* **deps:** update dependency sequelize-typescript to v2.1.5 ([eadedbe](https://github.com/nestjs/terminus/commit/eadedbef43dbb7568295fd74e199f25c1605abbc))
* HealthCheckError in sync HealthIndicatorFunction ([ee06275](https://github.com/nestjs/terminus/commit/ee062758e74513fa7b058b6c680c5df592e9ba0f)), closes [#2083](https://github.com/nestjs/terminus/issues/2083)

## [9.1.3](https://github.com/nestjs/terminus/compare/9.1.3-beta.0...9.1.3) (2022-11-17)

## [9.1.3-beta.0](https://github.com/nestjs/terminus/compare/9.1.2...9.1.3-beta.0) (2022-11-17)


### Bug Fixes

* **deps:** update dependency rxjs to v7.5.7 ([a87a096](https://github.com/nestjs/terminus/commit/a87a096fd4e519b085cbc58f31a7edc26f90d241))
* **deps:** update nest monorepo ([61b272e](https://github.com/nestjs/terminus/commit/61b272eb638815df13beeb43a65842440db8e133))

## [9.1.2](https://github.com/nestjs/terminus/compare/9.1.1...9.1.2) (2022-09-26)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.6.10 ([71fdb51](https://github.com/nestjs/terminus/commit/71fdb51ad3b8c394aaf12d879841e90d341f6f6c))
* **deps:** update dependency @grpc/grpc-js to v1.7.1 ([8bb7c2b](https://github.com/nestjs/terminus/commit/8bb7c2b03314b708000feb8d7b1919d9608a9308))
* **deps:** update dependency @grpc/proto-loader to v0.7.2 ([331c666](https://github.com/nestjs/terminus/commit/331c66617b5e7c32946be74a7883644f84f2f5c9))
* **deps:** update dependency @nestjs/typeorm to v9.0.1 ([d7adb5f](https://github.com/nestjs/terminus/commit/d7adb5f0b96ec1b70eb9c62f0fd7c17b7f6191dd))
* **deps:** update dependency ioredis to v5.2.3 ([1c077ae](https://github.com/nestjs/terminus/commit/1c077aea92d5a6d21aaafa1695d44c08de9f1bda))
* **deps:** update dependency mongoose to v6.6.1 ([0f5c99f](https://github.com/nestjs/terminus/commit/0f5c99f0e1f3193cc437fbff97317be1d0ae5ddf))
* **deps:** update dependency redis to v4.3.1 ([8c85460](https://github.com/nestjs/terminus/commit/8c85460a9d77a9aaccc288964724f16b2437c865))
* **deps:** update dependency sequelize to v6.21.4 ([c494563](https://github.com/nestjs/terminus/commit/c494563f2f4d40e66016813080902417352ce4cc))
* **deps:** update dependency sequelize to v6.23.1 ([03565c5](https://github.com/nestjs/terminus/commit/03565c545ebe809fe0be9cc8712f06900702e6ad))
* **deps:** update dependency typeorm to v0.3.10 ([d00b555](https://github.com/nestjs/terminus/commit/d00b555b6025aa9a0c6ac17dc3c691f54e12a12f))
* **deps:** update nest monorepo ([670d4ce](https://github.com/nestjs/terminus/commit/670d4ce41543ba1c7fce0a6b6af7cc38e6e3a9aa))
* **deps:** update nest monorepo to v9.0.11 ([b8ed5fe](https://github.com/nestjs/terminus/commit/b8ed5fef044a6abf4eb03c31f28795569d48cb1f))
* flush logs on package resolution failure ([56cb4b5](https://github.com/nestjs/terminus/commit/56cb4b5f09d53eb8a9b6ad82be7cd28c06479d11))

## [9.1.1](https://github.com/nestjs/terminus/compare/9.1.0...9.1.1) (2022-08-10)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.6.9 ([24d9d45](https://github.com/nestjs/terminus/commit/24d9d45b3da86c7f3ab7f2f0b6ca3f9c489be118))
* **deps:** update dependency @nestjs/terminus to v9.1.0 ([53b323f](https://github.com/nestjs/terminus/commit/53b323fa530d8d07e6c76d6e8a96b6bca1066a9a))
* **deps:** update dependency mongoose to v6.5.2 ([33fc690](https://github.com/nestjs/terminus/commit/33fc69091f2e06ce8c0142300643b6d3375ed688))
* **deps:** update nest monorepo to v9.0.8 ([97ff06c](https://github.com/nestjs/terminus/commit/97ff06cde6f847a36bed4978f7459d28013eec9e))
* **http:** remove axios reference in type defs ([d449820](https://github.com/nestjs/terminus/commit/d44982076824c80900fcffaa33db383db4e9a36c)), closes [#1991](https://github.com/nestjs/terminus/issues/1991)

# [9.1.0](https://github.com/nestjs/terminus/compare/9.0.0...9.1.0) (2022-07-30)


### Bug Fixes

* **deps:** pin dependency ioredis to 5.2.0 ([#1940](https://github.com/nestjs/terminus/issues/1940)) ([0947d43](https://github.com/nestjs/terminus/commit/0947d439723181da285fea3d524349a332bce4ac))
* **deps:** update dependency @grpc/grpc-js to v1.6.8 ([#1959](https://github.com/nestjs/terminus/issues/1959)) ([9dc1530](https://github.com/nestjs/terminus/commit/9dc15309ae1d580e72a88a3e49ad18a2dd9a5e03))
* **deps:** update dependency @grpc/proto-loader to v0.7.0 ([#1943](https://github.com/nestjs/terminus/issues/1943)) ([0371fd0](https://github.com/nestjs/terminus/commit/0371fd06d9bc57f5bb9a45fbb531495a028eb4e4))
* **deps:** update dependency @nestjs/axios to v0.1.0 ([8df8a6f](https://github.com/nestjs/terminus/commit/8df8a6fe47280a7a4ce89a36087d6c2df2ec0c5f))
* **deps:** update dependency @nestjs/sequelize to v9 ([a77d589](https://github.com/nestjs/terminus/commit/a77d5895cc9f97cdf0bc83e71d75341be4b89e0b))
* **deps:** update dependency @nestjs/terminus to v8.1.1 ([7f27da6](https://github.com/nestjs/terminus/commit/7f27da6c35e5e018b4f67391061bcf09db9c4b33))
* **deps:** update dependency @nestjs/terminus to v9 ([1d4dfbd](https://github.com/nestjs/terminus/commit/1d4dfbd290174a69624cd0d44609cb777467548d))
* **deps:** update dependency @nestjs/typeorm to v9 ([eb6a75c](https://github.com/nestjs/terminus/commit/eb6a75c62c3df9f5a1b8b37891df685490c742ed))
* **deps:** update dependency check-disk-space to v3.3.1 ([701f9ed](https://github.com/nestjs/terminus/commit/701f9ed523dbb3f6566b0ef069b582e3b9bf8da6))
* **deps:** update dependency ioredis to v5.2.2 ([a821e5c](https://github.com/nestjs/terminus/commit/a821e5c82ca5fc3403bbc12df472f6183137c5e7))
* **deps:** update dependency mongoose to v6.5.0 ([#1953](https://github.com/nestjs/terminus/issues/1953)) ([c5fb4b3](https://github.com/nestjs/terminus/commit/c5fb4b3cd3e0e56741fc592cbdf3ebaf0c917408))
* **deps:** update dependency redis to v4.2.0 ([041e81f](https://github.com/nestjs/terminus/commit/041e81f3c37d12f0fa550c4eaa2c19983db572bc))
* **deps:** update dependency rxjs to v7.5.6 ([74d7953](https://github.com/nestjs/terminus/commit/74d79535370145148092920029bf48e51c70b60b))
* **deps:** update dependency sequelize to v6.21.3 ([1539e8a](https://github.com/nestjs/terminus/commit/1539e8acb5aa828fcb6791720fa13dcc327a9ee5))
* **deps:** update nest monorepo to v9 ([b0e7db0](https://github.com/nestjs/terminus/commit/b0e7db0e31789355b0198e3db9173603662b3e41))
* **deps:** update nest monorepo to v9.0.3 ([0051eff](https://github.com/nestjs/terminus/commit/0051eff20efb02cb3bb85d5c3affb94791c19ba6))
* **deps:** update nest monorepo to v9.0.5 ([9d7adc2](https://github.com/nestjs/terminus/commit/9d7adc24f27a24455ff1e095fe186683e20c9250))
* **deps:** update nest monorepo to v9.0.7 ([4574960](https://github.com/nestjs/terminus/commit/45749601cd63b8b04f1a9f4bc5927361f9e3731d))
* **http:** responseCheck callback does not get executed on unhealthy http res ([d9147d8](https://github.com/nestjs/terminus/commit/d9147d8af1048506bb907e71976790a55c75c482)), closes [#1944](https://github.com/nestjs/terminus/issues/1944)


### Features

* add pretty error print log ([818504e](https://github.com/nestjs/terminus/commit/818504e6ec93f8bfaf67a5714cd1cbf9d2ff2a56)), closes [#1925](https://github.com/nestjs/terminus/issues/1925)


### Performance Improvements

* run health indicators in parallel ([b791916](https://github.com/nestjs/terminus/commit/b79191616c22795fa5b308688f6ae24b29cb63da)), closes [#1965](https://github.com/nestjs/terminus/issues/1965)

# [9.0.0](https://github.com/nestjs/terminus/compare/8.1.1...9.0.0) (2022-07-10)


### Bug Fixes

* **deps:** update dependency @nestjs/mongoose to v9.2.0 ([41774ea](https://github.com/nestjs/terminus/commit/41774ea8d74aa0ea221b41f5a501725c81bd1c3d))
* **deps:** update dependency mongoose to v6.4.4 ([2ade1c3](https://github.com/nestjs/terminus/commit/2ade1c39d2aa1074ab7d14fd2f81f1d861f5ec33))
* **deps:** update dependency sequelize to v6.21.2 ([9502239](https://github.com/nestjs/terminus/commit/950223903fe6761c6b1e4a0d45e57c19650fdb0d))
* **deps:** update dependency typeorm to v0.3.7 ([819f0ad](https://github.com/nestjs/terminus/commit/819f0adf96aa080a7188c004f730277e41931eee))


### Features

* upgrade to NestJS v9 ([2809388](https://github.com/nestjs/terminus/commit/28093885a68ec04fc89c636428fff7e736c558a9))


### BREAKING CHANGES

* - Upgrade to NestJS v9
- Replace `redis` with `ioredis` package
- Drop Node v10 support

## [8.1.1](https://github.com/nestjs/terminus/compare/8.1.0...8.1.1) (2022-07-05)


### Bug Fixes

* **deps:** update dependency @grpc/proto-loader to v0.6.13 ([b8e4254](https://github.com/nestjs/terminus/commit/b8e425465023bacad9e8511bc56fa3f53ec072de))
* **deps:** update dependency @nestjs/axios to v0.0.8 ([370a265](https://github.com/nestjs/terminus/commit/370a26575512856508b19488bda51b14aa3363a2))
* **deps:** update dependency @nestjs/sequelize to v8.0.1 ([7cdabbc](https://github.com/nestjs/terminus/commit/7cdabbca07277e4a69612ae5ce090983b3fe77fb))
* **deps:** update dependency mongoose to v6.4.1 ([8b3cefd](https://github.com/nestjs/terminus/commit/8b3cefdd6b8bc15c42fe25d758d0e268b92b127e))
* **deps:** update nest monorepo to v8.4.7 ([2b92488](https://github.com/nestjs/terminus/commit/2b92488e8fbfa8f1281ce68b03c30d1b7d21ad97))
* **microservice:** RMQ health check always healthy even when down ([da3f6c0](https://github.com/nestjs/terminus/commit/da3f6c0164f07d2e1e65ddd1937ce425f6d5bd0f)), closes [#1891](https://github.com/nestjs/terminus/issues/1891)
* **typo:** fix another typo ([c3fc5f3](https://github.com/nestjs/terminus/commit/c3fc5f386f24149a490277b7ff79113a6b66b9d6))
* **typo:** fix bunch of typos ([139ca49](https://github.com/nestjs/terminus/commit/139ca49aa289671809aaefc3e4ef5542dd798c53))

# [8.1.0](https://github.com/nestjs/terminus/compare/8.0.8...8.1.0) (2022-06-27)


### Bug Fixes

* **deps:** update dependency @nestjs/terminus to v8.0.8 ([ca9362c](https://github.com/nestjs/terminus/commit/ca9362ce696d4eb559edc247d53014169150e1eb))


### Features

* **health-indicator:** add mikro-orm health indicator ([e67d939](https://github.com/nestjs/terminus/commit/e67d939c2ed1d67ee427a9213417be80414f5ff3)), closes [#1877](https://github.com/nestjs/terminus/issues/1877)

# [8.1.0-beta.1](https://github.com/nestjs/terminus/compare/8.1.0-beta.0...8.1.0-beta.1) (2022-06-26)

# [8.1.0-beta.0](https://github.com/nestjs/terminus/compare/8.0.8...8.1.0-beta.0) (2022-06-26)


### Features

* **health-indicator:** add mikro-orm health indicator ([aecbe4b](https://github.com/nestjs/terminus/commit/aecbe4b66881b74542279b1ed7a9864db1e0831e))

## [8.0.8](https://github.com/nestjs/terminus/compare/8.0.7...8.0.8) (2022-06-19)

## [8.0.6](https://github.com/nestjs/terminus/compare/8.0.5...8.0.6) (2022-03-16)

## [8.0.4](https://github.com/nestjs/terminus/compare/8.0.3...8.0.4) (2022-01-16)

### Bug Fixes
* **deps:** update dependency @grpc/grpc-js to v1.5.0 ([95e99fe](https://github.com/nestjs/terminus/commit/95e99fedfa03847b2b98da5725e266c83db5074a))
* **deps:** update dependency @grpc/proto-loader to v0.6.9 ([861f27d](https://github.com/nestjs/terminus/commit/861f27d66d4fcf8aa09c9dd7f52baf0ddcb5dc3b))
* **deps:** update dependency @nestjs/terminus to v8.0.3 ([ac5c53c](https://github.com/nestjs/terminus/commit/ac5c53c7dab2537344469666d7c42254734ab761))
* **deps:** update dependency check-disk-space to v3.1.0 ([7c01f58](https://github.com/nestjs/terminus/commit/7c01f583df5d59e5823588d6ac607f158921bbab))
* **deps:** update dependency class-transformer to v0.5.1 ([103d9d9](https://github.com/nestjs/terminus/commit/103d9d97e6d5815a1868b09270b45720dc7b7cb6))
* **deps:** update dependency class-validator to v0.13.2 ([2f2dd2a](https://github.com/nestjs/terminus/commit/2f2dd2ab448ff4f9348424c217f1996944403cbf))
* **deps:** update dependency redis to v4 ([7e0bea8](https://github.com/nestjs/terminus/commit/7e0bea89f1b20ae0c8710ccadb27fb558548e036))
* **deps:** update dependency sequelize to v6.12.5 ([a98ac07](https://github.com/nestjs/terminus/commit/a98ac070379d012f5f9fcb3e909f1e50348956fc))
* **deps:** update dependency sequelize-typescript to v2.1.2 ([912d3ba](https://github.com/nestjs/terminus/commit/912d3ba7f45be793749ba059431086d63c61740c))
* **deps:** update nest monorepo to v8.2.4 ([619033c](https://github.com/nestjs/terminus/commit/619033ce857a32249a79da3f48aafded6868fdb6))
* **http:** URL interface gets imported ([ac1f3a3](https://github.com/nestjs/terminus/commit/ac1f3a39c2b53f22d45b195a2e5b09914e4dcf1b)), closes [#1623](https://github.com/nestjs/terminus/issues/1623)

## [8.0.3](https://github.com/nestjs/terminus/compare/8.0.2...8.0.3) (2021-12-03)


### Bug Fixes

* **deps:** update dependency @grpc/proto-loader to v0.6.7 ([4fb84b3](https://github.com/nestjs/terminus/commit/4fb84b335d1a6a33497e0d8ac89820056baf535d))
* **deps:** update dependency sequelize to v6.11.0 ([f3bf368](https://github.com/nestjs/terminus/commit/f3bf368b44b47482548a2a19d5e983db48a32e7e))
* **deps:** update nest monorepo to v8.2.3 ([fc2d73f](https://github.com/nestjs/terminus/commit/fc2d73ff9ee4c201d34869206cec0c6ce5b8bf5e))

## [8.0.2](https://github.com/nestjs/terminus/compare/8.0.1...8.0.2) (2021-11-26)


### Bug Fixes

* **deps:** update dependency @grpc/grpc-js to v1.4.4 ([da75499](https://github.com/nestjs/terminus/commit/da75499dda29896ab710f05d84bdf0acc1ff572d))
* **deps:** update dependency @nestjs/axios to v0.0.3 ([ac84369](https://github.com/nestjs/terminus/commit/ac843699ba109f940ff86a4d3af7de127a7b0176))
* **deps:** update dependency @nestjs/mongoose to v9 ([99f01b5](https://github.com/nestjs/terminus/commit/99f01b50f34d6d9fb79865a8462ccd4d4bf30be8))
* **deps:** update dependency @nestjs/terminus to v8.0.1 ([6795db7](https://github.com/nestjs/terminus/commit/6795db72a9abf0e778b76480f1613170a732b6ed))
* **deps:** update dependency class-transformer to v0.4.1 ([91451e5](https://github.com/nestjs/terminus/commit/91451e521f6d4811e77aaa31652117f561e2a6ca))
* **deps:** update dependency mysql2 to v2.3.3 ([7c07a8a](https://github.com/nestjs/terminus/commit/7c07a8a156e29af0aa187903c8c2cb4ced8f61f4))
* **deps:** update dependency rxjs to v7.4.0 ([29662ef](https://github.com/nestjs/terminus/commit/29662efebcc085c2960e4e9faa646b43891b6766))
* **deps:** update dependency sequelize-typescript to v2.1.1 ([e2a8ab6](https://github.com/nestjs/terminus/commit/e2a8ab6fd92dfd5e60036536f0463c61b9d7e48c))
* **deps:** update dependency typeorm to v0.2.40 ([6f22672](https://github.com/nestjs/terminus/commit/6f22672560861f42b5c150e8c50da8f112ec001d))
* **deps:** update dependency typeorm to v0.2.41 ([8298293](https://github.com/nestjs/terminus/commit/8298293d333fa951d9bfca79d3780df1989cd077))
* **deps:** update nest monorepo to v8.0.7 ([655c763](https://github.com/nestjs/terminus/commit/655c763c70ed1a17efafe84fc7645dcd3f5da80a))
* **deps:** update nest monorepo to v8.0.8 ([7a51ff4](https://github.com/nestjs/terminus/commit/7a51ff46fbc397fd2b42913ad3c84897c9cab5aa))
* **deps:** update nest monorepo to v8.0.9 ([b4df97c](https://github.com/nestjs/terminus/commit/b4df97c97aaa05bce550fcbdef8ef6e55000bd23))
* **deps:** update nest monorepo to v8.2.1 ([b4a1dc7](https://github.com/nestjs/terminus/commit/b4a1dc7057630ac0e66418cd4c21554d6640679e))
* **deps:** update nest monorepo to v8.2.2 ([b174e32](https://github.com/nestjs/terminus/commit/b174e3269aa0edbb4a5a0ca9bf6a22169a9cdefe))
* **health:** open handle when using `pingCheck` in jest environment ([0d7b48e](https://github.com/nestjs/terminus/commit/0d7b48e454bd3d2623bc9167305915dcbaa7cbcd)), closes [#1466](https://github.com/nestjs/terminus/issues/1466)

## [8.0.1](https://github.com/nestjs/terminus/compare/8.0.0...8.0.1) (2021-09-22)


### Bug Fixes

* **deps:** update dependency @grpc/proto-loader to v0.6.4 ([16f59e0](https://github.com/nestjs/terminus/commit/16f59e033627743b60d6a51d7d0b3e867a3b96cf))
* **deps:** update dependency @grpc/proto-loader to v0.6.5 ([2df4f25](https://github.com/nestjs/terminus/commit/2df4f25358363f30aa4e0a993bd3192e4bb9a655))
* **deps:** update dependency @nestjs/mongoose to v8.0.1 ([eb4dcd0](https://github.com/nestjs/terminus/commit/eb4dcd0cb7d5a205ee47bd446d88367e42ba3198))
* **deps:** update dependency @nestjs/typeorm to v8 ([49453f3](https://github.com/nestjs/terminus/commit/49453f3a82d23d06dd2a24ac307c58046997d010))
* **deps:** update dependency grpc to v1.24.11 ([173441f](https://github.com/nestjs/terminus/commit/173441fe6ba1dd762ad3666c5eab97d60a0849c1))
* **deps:** update dependency mysql2 to v2.3.0 ([a75173a](https://github.com/nestjs/terminus/commit/a75173a7abcd8edba1f540303866449410f7e28c))
* **deps:** update dependency rxjs to v7 ([5d47b11](https://github.com/nestjs/terminus/commit/5d47b11031f41f257ea0f8ba7340e21aebe7495f))
* **deps:** update dependency sequelize to v6.6.5 ([023e35e](https://github.com/nestjs/terminus/commit/023e35e5015874da3e403d0295e0aff5386ea33f))
* **deps:** update dependency sequelize-typescript to v2 ([5ce8dba](https://github.com/nestjs/terminus/commit/5ce8dbaa107586b3601924ce54037a4e9d7d60b2))
* **deps:** update dependency typeorm to v0.2.37 ([9116a29](https://github.com/nestjs/terminus/commit/9116a29d17fc093499b79d0478814013d700a4d3))
* **deps:** update nest monorepo to v8.0.6 ([392d378](https://github.com/nestjs/terminus/commit/392d378d2a016bd3e453c9746814f9714b0512f8))
* remove dependencies grpc [#1425](https://github.com/nestjs/terminus/issues/1425)) ([432300f](https://github.com/nestjs/terminus/commit/432300f05283901f329e1681c940256435c2680d))

# [8.0.0](https://github.com/nestjs/terminus/compare/7.2.0...8.0.0) (2021-08-27)


### Bug Fixes

* **deps:** update dependency @godaddy/terminus to v4.7.2 ([ba7c3a1](https://github.com/nestjs/terminus/commit/ba7c3a1320ff64a8de25340a768a97141cb65bf2))
* **deps:** update dependency @godaddy/terminus to v4.8.0 ([878939b](https://github.com/nestjs/terminus/commit/878939bfd0a9b62ea165a039ede88f9e419947be))
* **deps:** update dependency @godaddy/terminus to v4.9.0 ([66867f8](https://github.com/nestjs/terminus/commit/66867f8a4dadee439eb9375016ff41b9576faad9))
* **deps:** update dependency @grpc/proto-loader to v0.6.2 ([821fb9e](https://github.com/nestjs/terminus/commit/821fb9e3398b782c90405e87579ba58444713ee0))
* **deps:** update dependency @nestjs/terminus to v7.2.0 ([65f6cf7](https://github.com/nestjs/terminus/commit/65f6cf74d16c23a8f23a81baec36c4807bc6061f))
* **deps:** update dependency fastify to v3.15.1 ([e5dea7a](https://github.com/nestjs/terminus/commit/e5dea7af982a425378d3d98b5b2b3af3fe8b625f))
* **deps:** update dependency fastify to v3.17.0 ([1a744b1](https://github.com/nestjs/terminus/commit/1a744b1763ee9c9745737e4dd1a7849398d019ac))
* **deps:** update dependency grpc to v1.24.10 ([83a39fc](https://github.com/nestjs/terminus/commit/83a39fcc6006e37ffca1ac4fc4e2a324cdf56202))
* **deps:** update dependency grpc to v1.24.9 ([044b47c](https://github.com/nestjs/terminus/commit/044b47c3b14ee5b9e17df03dc7e79756fc61ea8c))
* **deps:** update dependency typeorm to v0.2.34 ([99b1e44](https://github.com/nestjs/terminus/commit/99b1e449f8316e5781408f33b937bad945f0e3d7))
* **deps:** update nest monorepo to v7.6.17 ([85c28c4](https://github.com/nestjs/terminus/commit/85c28c4eae62d1d638fbd81d9ba4ecd2fa348906))


### Code Refactoring

* remove deprecated Terminus API ([70e36ed](https://github.com/nestjs/terminus/commit/70e36edd61e23e3511d280fb73346b7348213d72))


### Features

* upgrade to v8 ([ba6eee0](https://github.com/nestjs/terminus/commit/ba6eee07b5878cf652aaf2a0d975dc2d73a63af6))


### BREAKING CHANGES

* TerminusModule.forRoot{Async) has been removed

In order to migrate, check out: https://docs.nestjs.com/migration-guide#terminus
* Upgrade to NestJS v8.x.x

# [7.2.0](https://github.com/nestjs/terminus/compare/7.1.2...7.2.0) (2021-05-13)


### Bug Fixes

* **deps:** update dependency @grpc/proto-loader to v0.6.0 ([e00f738](https://github.com/nestjs/terminus/commit/e00f73887e48300f6d4c0f7b6ebb4f94160bbcf5))
* **deps:** update dependency @grpc/proto-loader to v0.6.1 ([19165b9](https://github.com/nestjs/terminus/commit/19165b9db2e7a6b76c4f0774477f26a13c43b22a))
* **deps:** update dependency @nestjs/mongoose to v7.2.4 ([1d0d26e](https://github.com/nestjs/terminus/commit/1d0d26e6cc0a8dc24466ffcf799158b65a7f049e))
* **deps:** update dependency @nestjs/terminus to v7.1.2 ([9d0c98e](https://github.com/nestjs/terminus/commit/9d0c98e47eef066a55a1a35c6c1fda60a359364f))
* **deps:** update dependency fastify to v3.14.2 ([710055b](https://github.com/nestjs/terminus/commit/710055bbc40e996dce9fe9cf34523b6186184239))
* **deps:** update dependency fastify to v3.15.0 ([61940eb](https://github.com/nestjs/terminus/commit/61940eb72be4d14ff34b023cd2c2084b5b098243))
* **deps:** update dependency redis to v3.1.0 ([7e83581](https://github.com/nestjs/terminus/commit/7e835818edcbebb3f5813ee110d0ecdb776906d5))
* **deps:** update dependency redis to v3.1.2 ([7ca9ed9](https://github.com/nestjs/terminus/commit/7ca9ed94c2b1ef9551e10ea882f09683ba1f3c2a))
* **deps:** update dependency rxjs to v6.6.7 ([de4da85](https://github.com/nestjs/terminus/commit/de4da85b31f152eb198e7933772a8829984b3bb3))


### Features

* **health:** add `httpClient` as option ([74d98db](https://github.com/nestjs/terminus/commit/74d98dbda5fc098e4a5d9d695257bcd71a9bab6d)), closes [#1151](https://github.com/nestjs/terminus/issues/1151)

# [7.2.0-next.0](https://github.com/nestjs/terminus/compare/7.1.2...7.2.0-next.0) (2021-04-11)


### Bug Fixes

* **deps:** update dependency @grpc/proto-loader to v0.6.0 ([e00f738](https://github.com/nestjs/terminus/commit/e00f73887e48300f6d4c0f7b6ebb4f94160bbcf5))
* **deps:** update dependency @nestjs/mongoose to v7.2.4 ([1d0d26e](https://github.com/nestjs/terminus/commit/1d0d26e6cc0a8dc24466ffcf799158b65a7f049e))
* **deps:** update dependency @nestjs/terminus to v7.1.2 ([9d0c98e](https://github.com/nestjs/terminus/commit/9d0c98e47eef066a55a1a35c6c1fda60a359364f))
* **deps:** update dependency fastify to v3.14.2 ([710055b](https://github.com/nestjs/terminus/commit/710055bbc40e996dce9fe9cf34523b6186184239))
* **deps:** update dependency redis to v3.1.0 ([7e83581](https://github.com/nestjs/terminus/commit/7e835818edcbebb3f5813ee110d0ecdb776906d5))
* **deps:** update dependency rxjs to v6.6.7 ([de4da85](https://github.com/nestjs/terminus/commit/de4da85b31f152eb198e7933772a8829984b3bb3))


### Features

* **health:** add `httpClient` as option ([25a428c](https://github.com/nestjs/terminus/commit/25a428c80c9f2b521f7d528fe6d7685be3ea0b1d)), closes [#1151](https://github.com/nestjs/terminus/issues/1151)

## [7.1.2](https://github.com/nestjs/terminus/compare/7.1.1...7.1.2) (2021-04-04)


### Bug Fixes

* TypeORM Health Check for SAP HANA ([dc32733](https://github.com/nestjs/terminus/commit/dc32733399a8cd1e902b675e1a273ba31eb88a85))
* **deps:** update dependency @godaddy/terminus to v4.7.1 ([88751c4](https://github.com/nestjs/terminus/commit/88751c46b7b15f230ba11e3caee5d9efbb11107f))
* **deps:** update dependency @nestjs/terminus to v7.1.1 ([58d337f](https://github.com/nestjs/terminus/commit/58d337fd71551a648dc5d84e5f53a5cd8cae0192))
* **deps:** update dependency sequelize to v5.22.4 ([560aaca](https://github.com/nestjs/terminus/commit/560aaca4e9eb54de75effa3dca365eab100e0370))
* **deps:** update dependency typeorm to v0.2.32 ([eb55a75](https://github.com/nestjs/terminus/commit/eb55a752aa7557f2aa81fe2658b8e209cceb7a00))
* **deps:** update nest monorepo to v7.6.15 ([c6e8464](https://github.com/nestjs/terminus/commit/c6e8464a07d557f8239e575039dd42e8d911827c))

## [7.1.1](https://github.com/nestjs/terminus/compare/7.1.0...7.1.1) (2021-03-27)


### Bug Fixes

* **deps:** update dependency @nestjs/sequelize to v0.2.0 ([6e16abc](https://github.com/nestjs/terminus/commit/6e16abce549d4661816e796c8e4014ac6056a3d3))
* **deps:** update dependency class-transformer to v0.4.0 ([4a0f17a](https://github.com/nestjs/terminus/commit/4a0f17ab4d426daecb9914b91f49132fa93ab758))
* **deps:** update dependency class-validator to v0.13.1 ([79df750](https://github.com/nestjs/terminus/commit/79df750137ccaefd3507d9a00db8e5694322f524))
* **deps:** update dependency fastify to v3.13.0 ([b3d5425](https://github.com/nestjs/terminus/commit/b3d5425fd026cb311c340052d55574acd8dde161))
* **deps:** update dependency grpc to v1.24.5 ([1b968f2](https://github.com/nestjs/terminus/commit/1b968f202b9ba09218f44868f88242e6d06af97f))
* **deps:** update dependency lodash to v4.17.21 ([94df5e4](https://github.com/nestjs/terminus/commit/94df5e4b3ec298b2388633452096283c604c75c9))
* **deps:** update dependency rxjs to v6.6.6 ([dee27f0](https://github.com/nestjs/terminus/commit/dee27f0b4a48d76f99e54fe8de22fe5592a1a868))
* **deps:** update dependency sequelize to v5.22.3 ([157ee24](https://github.com/nestjs/terminus/commit/157ee245d8fcb0196557002b3c83c7664e19fa12))
* **deps:** update dependency typeorm to v0.2.31 ([ad1db55](https://github.com/nestjs/terminus/commit/ad1db5520f5c11bf749568d6aaa344eb79ce1622))
* **deps:** update nest monorepo to v7.6.12 ([9c77d92](https://github.com/nestjs/terminus/commit/9c77d928daddf8cdddadde363e7422b0f342ea0e))
* **deps:** update nest monorepo to v7.6.13 ([2af6a93](https://github.com/nestjs/terminus/commit/2af6a938321dac8c28248dc7cb0c5a3b54b0a48d))

# [7.1.0](https://github.com/nestjs/terminus/compare/7.1.0-next.1...7.1.0) (2021-01-31)

# [7.1.0-next.1](https://github.com/nestjs/terminus/compare/7.1.0-next.0...7.1.0-next.1) (2021-01-31)

# [7.1.0-next.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2021-01-31)


### Bug Fixes

* **deps:** pin dependencies ([b938a67](https://github.com/nestjs/terminus/commit/b938a67004b398c1267563c35d80b2d14d6b70d3))
* **deps:** update dependency @godaddy/terminus to v4.4.1 ([756b8ff](https://github.com/nestjs/terminus/commit/756b8ffeca84ef5eb1e9714782bfbb73a72c9bfd))
* **deps:** update dependency @godaddy/terminus to v4.5.0 ([f9ca250](https://github.com/nestjs/terminus/commit/f9ca2501c287e83eb918b61a5335207f00c4f474))
* **deps:** update dependency @godaddy/terminus to v4.6.0 ([d1094c8](https://github.com/nestjs/terminus/commit/d1094c8e9c4f6117ae8e1b54a9bd52ae1e7f7d0c))
* **deps:** update dependency @grpc/proto-loader to v0.5.5 ([b49d76c](https://github.com/nestjs/terminus/commit/b49d76ca51b19b0820fe500768af9a2009e8fb7b))
* **deps:** update dependency @grpc/proto-loader to v0.5.6 ([e3e421c](https://github.com/nestjs/terminus/commit/e3e421cf1aaff638b7c827b1309bb2775cb4f032))
* **deps:** update dependency @nestjs/mongoose to v7 ([dec67b0](https://github.com/nestjs/terminus/commit/dec67b05463a455123a6a4b115af9ef0a3803591))
* **deps:** update dependency @nestjs/mongoose to v7.0.2 ([a0ab83d](https://github.com/nestjs/terminus/commit/a0ab83dbf7e32367f1a133d1148d55b5a7293e2b))
* **deps:** update dependency class-transformer to v0.3.1 [security] ([cd823d9](https://github.com/nestjs/terminus/commit/cd823d968a7a80e3e3497a418facb325e10a3558))
* **deps:** update dependency class-transformer to v0.3.2 ([57af43a](https://github.com/nestjs/terminus/commit/57af43a9c9d6c7f7adbd613acc9ba02350473082))
* **deps:** update dependency class-validator to v0.12.2 ([7b79b9f](https://github.com/nestjs/terminus/commit/7b79b9ff1e50b0f92213a61b4bf9a0f0344a821c))
* **deps:** update dependency fastify to v2.13.1 ([1966c6b](https://github.com/nestjs/terminus/commit/1966c6be02bc3bd2a52dbaea16e981b6567645a3))
* **deps:** update dependency fastify to v2.14.0 ([548880c](https://github.com/nestjs/terminus/commit/548880c39d2c19101697c4a638900307b89b25e0))
* **deps:** update dependency fastify to v2.14.1 ([776c671](https://github.com/nestjs/terminus/commit/776c6717f6b5ac62071537f06948e54224b44364))
* **deps:** update dependency fastify to v2.15.0 ([32ab6f2](https://github.com/nestjs/terminus/commit/32ab6f2514b882352d45e0bd1a88d5c727fd523e))
* **deps:** update dependency fastify to v2.15.2 ([966ba8e](https://github.com/nestjs/terminus/commit/966ba8ed018ec2dfac73ecbc26fff57dbc7215bd))
* **deps:** update dependency fastify to v3 ([fc41920](https://github.com/nestjs/terminus/commit/fc41920a7759570ec37bf78dbbe1cf865663554f))
* **deps:** update dependency fastify to v3.11.0 ([78f56ef](https://github.com/nestjs/terminus/commit/78f56efb0cd08624c2b30e7a04ae83a43d596755))
* **deps:** update dependency fastify to v3.5.0 ([49a0fe9](https://github.com/nestjs/terminus/commit/49a0fe9d1a2825b6c9c5b0a1317a7f4f8d6293d8))
* **deps:** update dependency fastify to v3.7.0 ([de6aeae](https://github.com/nestjs/terminus/commit/de6aeae1bf759f22741b1bf122a45087dd8ad379))
* **deps:** update dependency fastify to v3.9.2 ([9267df9](https://github.com/nestjs/terminus/commit/9267df9a0da3ecf0c570e5205437c7c63afb5165))
* **deps:** update dependency grpc to v1.24.3 ([6dc4d09](https://github.com/nestjs/terminus/commit/6dc4d09cbd76984616b35305dcbbf98f76c7422c))
* **deps:** update dependency grpc to v1.24.4 ([caf08fe](https://github.com/nestjs/terminus/commit/caf08fe4b3b42ce2edc9b149708d9939b6daf501))
* **deps:** update dependency lodash to v4.17.19 ([f9c746b](https://github.com/nestjs/terminus/commit/f9c746b9cfec21aeb1db593778ee4ffe122ad173))
* **deps:** update dependency lodash to v4.17.20 ([3e71bf2](https://github.com/nestjs/terminus/commit/3e71bf253666f6ee121127c2dfae5c116561a21a))
* **deps:** update dependency rxjs to v6.6.0 ([61780e9](https://github.com/nestjs/terminus/commit/61780e934718baa58b7ec9fa747e712fad3db7f4))
* **deps:** update dependency typeorm to v0.2.29 ([47e92b1](https://github.com/nestjs/terminus/commit/47e92b1d0a274ce3ca6f199bb9c1e60d986aff19))
* **deps:** update dependency typeorm to v0.2.30 ([5ed2a1b](https://github.com/nestjs/terminus/commit/5ed2a1bdff1e1a5bc71b3805761655670ac932b3))
* **deps:** update dependency typescript to v3.9.3 ([edade2d](https://github.com/nestjs/terminus/commit/edade2d7a999df32b6ca9e790d849ac5f891cf2f))
* **deps:** update dependency typescript to v3.9.5 ([f5118bb](https://github.com/nestjs/terminus/commit/f5118bb9bd369d569f285ec39373f2d46f157142))
* **deps:** update dependency typescript to v3.9.6 ([4e7d5e4](https://github.com/nestjs/terminus/commit/4e7d5e42801d19bf57062b5dcb64cfe345fb8f0f))
* **deps:** update dependency typescript to v3.9.7 ([2f01942](https://github.com/nestjs/terminus/commit/2f019422a5d765a5653a41f28b0e358f02f1c977))
* **deps:** update nest monorepo to v7.0.9 ([b7e26af](https://github.com/nestjs/terminus/commit/b7e26af29432f94dda29fc9b90a161b388c59c6d))
* **deps:** update nest monorepo to v7.1.0 ([e919248](https://github.com/nestjs/terminus/commit/e91924814833908b045b82e68b1e8563bc483340))
* **deps:** update nest monorepo to v7.1.2 ([824bf3f](https://github.com/nestjs/terminus/commit/824bf3fc8f5b28c1be31bbcdddc885b39bd4f0f1))
* **deps:** update nest monorepo to v7.1.3 ([30a6479](https://github.com/nestjs/terminus/commit/30a64794b372d9d7e39d4725f80f5bbf0c958461))
* **deps:** update nest monorepo to v7.2.0 ([cbaf79d](https://github.com/nestjs/terminus/commit/cbaf79d5d226d78bcc8831275dfa4a6def90bc97))
* **deps:** update nest monorepo to v7.3.0 ([9842678](https://github.com/nestjs/terminus/commit/98426783aa9acb848253b0dab2c8dc32480c0f48))
* **deps:** update nest monorepo to v7.3.2 ([8f9c47a](https://github.com/nestjs/terminus/commit/8f9c47adef41c4a43ab7cb4fec6ce9dcb14ce0a2))
* **deps:** update nest monorepo to v7.5.4 ([8795f24](https://github.com/nestjs/terminus/commit/8795f24e347fbd7a18f7cef3397ed01e57b10418))


### Features

* **dns:** add responseCheck function ([e31a9e3](https://github.com/nestjs/terminus/commit/e31a9e35bc528210ffdf5c8b656df9b6480c0cd2))
* **sequelize:** add sequelize health indicator ([9b2e262](https://github.com/nestjs/terminus/commit/9b2e262ddc39f1fee28add77b39ea30b6e95f71e))



## [7.0.1](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2020-04-10)


### Bug Fixes

* export HealthCheckResult to public interface ([5deda3b](https://github.com/nestjs/terminus/commit/5deda3b8291b4ed92a89fb85bda86dd7b6a43353)), closes [#639](https://github.com/nestjs/terminus/issues/639)
* **deps:** update nest monorepo to v7.0.7 ([ed9d0c0](https://github.com/nestjs/terminus/commit/ed9d0c0d0e9202475f903c6f8666ad702d8459f1))



# [7.0.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2020-04-05)


### Bug Fixes

* grpc proto files not copied ([900383a](https://github.com/nestjs/terminus/commit/900383a51132bc3ad18d4bdd02e36a5ebc751b26))



# [7.0.0-pre.5](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2020-04-05)


### Bug Fixes

* grpc health indicator test to use HealthCheckError from package ([39f1927](https://github.com/nestjs/terminus/commit/39f19272ee2de5442769289ebbd611eb97bcd091))
* import of optional modules in dec files ([7ac4307](https://github.com/nestjs/terminus/commit/7ac4307b3803f83fac67b6aea032c9e2203b25c4))
* import path of HealthCheckError ([874224c](https://github.com/nestjs/terminus/commit/874224c244b2e7b4a5e715a53c93370e5ec945a8))
* minor code style changes ([b5beeaf](https://github.com/nestjs/terminus/commit/b5beeaf680069437fc4118de2493fbdf7343a788))
* remove mandatory @godaddy/terminus imports ([dceb5db](https://github.com/nestjs/terminus/commit/dceb5dbafef098f6901859bb0b1d7c9968cb1276))
* type imports for optional packages ([22d3d8c](https://github.com/nestjs/terminus/commit/22d3d8c4bdd71f0013cbf90a7992987838162d50))
* type only imports ([491df9a](https://github.com/nestjs/terminus/commit/491df9ad9171ca4c09bc70bdb5d2f0bb26c198c5))
* **deps:** update dependency @nestjs/mongoose to v6.2.1 ([aa32a7c](https://github.com/nestjs/terminus/commit/aa32a7cbd58ff9ac76d24e9d25a36eb2e52bfef7))
* **deps:** update dependency @nestjs/mongoose to v6.3.1 ([0b063fc](https://github.com/nestjs/terminus/commit/0b063fc2f13645c4c6d9be6c58568f10f318a5dc))
* **deps:** update dependency @nestjs/mongoose to v6.4.0 ([f135f04](https://github.com/nestjs/terminus/commit/f135f0477fc1e0479de0a13dd57028a17d2e314a))
* **deps:** update dependency fastify to v2.12.0 ([236b594](https://github.com/nestjs/terminus/commit/236b5942fed892821924fb4236801707963b63d9))
* **deps:** update dependency fastify to v2.12.1 ([ca814e7](https://github.com/nestjs/terminus/commit/ca814e7855f1d2dd47efe7edb031ef366b2911f7))
* **deps:** update dependency redis to v3 ([951056d](https://github.com/nestjs/terminus/commit/951056dfd4719b74c16cbe7fbbba58d6c564bab6))
* **deps:** update dependency rimraf to v3.0.1 ([0c18801](https://github.com/nestjs/terminus/commit/0c188014ff9521059d15dc03137c91d78c7a4bf9))
* **deps:** update dependency rimraf to v3.0.2 ([41f6e05](https://github.com/nestjs/terminus/commit/41f6e052299d95c88d12f9827b494a83f8020647))
* **deps:** update dependency rxjs to v6.5.4 ([5f81d55](https://github.com/nestjs/terminus/commit/5f81d55850e713062dd4967d4802902561cc9652))
* **deps:** update dependency typescript to v3.7.4 ([a9e103e](https://github.com/nestjs/terminus/commit/a9e103ee69334f965e2ef3b041a10491a1b1140e))
* **deps:** update dependency typescript to v3.7.5 ([b2b124c](https://github.com/nestjs/terminus/commit/b2b124c56c342a041d66d203d5fbc8c6838d7a9d))
* **deps:** update dependency typescript to v3.8.2 ([6b8ed94](https://github.com/nestjs/terminus/commit/6b8ed941d8d6b4a2ec040d72bfb5ab241e7a8d98))
* **deps:** update dependency typescript to v3.8.3 ([4aa6992](https://github.com/nestjs/terminus/commit/4aa69923bf1482bd271b21a803e51a64c4af8305))
* **deps:** update nest monorepo to v6.10.13 ([890f9f5](https://github.com/nestjs/terminus/commit/890f9f588c038a0c98d7f080640cf8b376956c95))
* **deps:** update nest monorepo to v6.10.14 ([5daabc1](https://github.com/nestjs/terminus/commit/5daabc189a305058991b48b210f6eb58175c3cba))
* **deps:** update nest monorepo to v6.11.5 ([193a95c](https://github.com/nestjs/terminus/commit/193a95ca711623aea8ee81da0f90c72a0429fefa))
* **deps:** update nest monorepo to v6.11.6 ([f59f109](https://github.com/nestjs/terminus/commit/f59f109682752100f9f0dbda373327d012054191))
* **deps:** update nest monorepo to v6.11.7 ([7a15fb1](https://github.com/nestjs/terminus/commit/7a15fb164597c60a1dcde608c03ae9070baea279))
* **deps:** update nest monorepo to v6.11.8 ([3e3c750](https://github.com/nestjs/terminus/commit/3e3c750436baeb78144a737abe5a1455328419fb))
* **typeorm:** Connection parameter error ([969ce67](https://github.com/nestjs/terminus/commit/969ce670e6d99c666a4bd31520fed9f1c57799fd)), closes [#545](https://github.com/nestjs/terminus/issues/545)
* **typeorm:** MongoDB connection always returns connected ([da11398](https://github.com/nestjs/terminus/commit/da11398d5a6cbfd37d39975946b45edd34dce324)), closes [#547](https://github.com/nestjs/terminus/issues/547)


### Features

* add swagger integration ([a3502f0](https://github.com/nestjs/terminus/commit/a3502f0d4d170edebe1364b634791f5ea3f1e658)), closes [#32](https://github.com/nestjs/terminus/issues/32)
* create HealthService ([42dc720](https://github.com/nestjs/terminus/commit/42dc720e6d9fdcc3ae90af5047e3b691153ddd6d))
* ignore deprecations warnings via env variable ([9fa88f8](https://github.com/nestjs/terminus/commit/9fa88f8003e703bb48ab78073989407dae4f090f))
* log health check errors in health check service ([d6687d9](https://github.com/nestjs/terminus/commit/d6687d9f81f2eb30690e02357ec4f4cb92dd1127))
* update to the latest release (7.0.0) ([1384a28](https://github.com/nestjs/terminus/commit/1384a28aa865e084f8bc01ba2e18790176fb6667))



## [6.5.5](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-12-16)


### Bug Fixes

* Do not bootstrap terminus in case no http server ([aaea244](https://github.com/nestjs/terminus/commit/aaea24432614e48b47f41b292d64eeaae4280a36)), closes [#461](https://github.com/nestjs/terminus/issues/461)
* **deps:** update dependency @godaddy/terminus to v4.3.1 ([bc5d033](https://github.com/nestjs/terminus/commit/bc5d0334de861c10906f275cc368712bec02f8b2))
* **deps:** update dependency fastify to v2.11.0 ([d954613](https://github.com/nestjs/terminus/commit/d95461316ac1a729f13e541c4c6aff45ce2afa0f))
* **deps:** update dependency typescript to v3.7.3 ([729a83a](https://github.com/nestjs/terminus/commit/729a83a44a73f3e0e5123a12332c2be25030bd4f))
* **deps:** update nest monorepo to v6.10.11 ([6340975](https://github.com/nestjs/terminus/commit/6340975bfef22fca85ceb5fee1d85e438d526bbe))
* **deps:** update nest monorepo to v6.10.2 ([7725f68](https://github.com/nestjs/terminus/commit/7725f6861b5b92dd78407e409dff819eed186297))



## [6.5.3](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-11-17)


### Bug Fixes

* **deps:** update dependency @godaddy/terminus to v4.2.1 ([6fd9b26](https://github.com/nestjs/terminus/commit/6fd9b26944ede865d2de7feddd2e48aea1211dfd))
* **deps:** update dependency @grpc/proto-loader to v0.5.3 ([520231a](https://github.com/nestjs/terminus/commit/520231a863f3816c90257c1fe95f1c1cf5ca75f6))
* **deps:** update dependency class-validator to v0.10.1 ([a454a10](https://github.com/nestjs/terminus/commit/a454a10d28ef31dfed21deac708e04ff5783ede2))
* **deps:** update dependency class-validator to v0.10.2 ([af5c4f8](https://github.com/nestjs/terminus/commit/af5c4f85c5e2f4ee26bb775e33f1af95220f2226))
* **deps:** update dependency class-validator to v0.11.0 ([fe58f40](https://github.com/nestjs/terminus/commit/fe58f40423a17afec9dbe54fdf7610bea09feb9f))
* **deps:** update dependency fastify to v2.10.0 ([2f3ebdf](https://github.com/nestjs/terminus/commit/2f3ebdfc4951c994153cc72ed6e26351cccadb9e))
* **deps:** update dependency fastify to v2.9.0 ([b17d598](https://github.com/nestjs/terminus/commit/b17d598412f6eb1e08cf2813108e1a09df355d75))
* **deps:** update dependency grpc to v1.24.0 ([c9317e9](https://github.com/nestjs/terminus/commit/c9317e95d437dfe178c0f597b58b09fa5f497b7d))
* **deps:** update dependency grpc to v1.24.1 ([b478457](https://github.com/nestjs/terminus/commit/b478457d3f7d23040a29bbe9f3ad36317fa87cc7))
* **deps:** update dependency grpc to v1.24.2 ([1ca98e3](https://github.com/nestjs/terminus/commit/1ca98e3edd346bd062d351619a553d1e9c79d2be))
* **deps:** update dependency typescript to v3.6.4 ([3d6ce15](https://github.com/nestjs/terminus/commit/3d6ce15e0ad9e950350d082e1a8d0fec53db8be3))
* **deps:** update dependency typescript to v3.7.2 ([a4973b1](https://github.com/nestjs/terminus/commit/a4973b125114621f04cedaab71980df7b4ad4b28))
* **deps:** update nest monorepo to v6.10.1 ([304feb0](https://github.com/nestjs/terminus/commit/304feb085595c4eb912e66b37bc9f5eeda4f70d5))
* **deps:** update nest monorepo to v6.8.0 ([bdfebbf](https://github.com/nestjs/terminus/commit/bdfebbf9fe24f5cfded8b9ba2c2513ef282cdacc))
* **deps:** update nest monorepo to v6.8.2 ([9862969](https://github.com/nestjs/terminus/commit/98629692e20ff4896452546b334c6fe24d8aa1d0))
* **deps:** update nest monorepo to v6.8.3 ([0630810](https://github.com/nestjs/terminus/commit/0630810e70da01d28d1ddf8e5688993b9572fb79))
* **deps:** update nest monorepo to v6.8.5 ([35f165e](https://github.com/nestjs/terminus/commit/35f165e5f8203c589fe717a26c03b3ebc99ca7f1))



## [6.5.2](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-09-23)


### Bug Fixes

* **deps:** update dependency @godaddy/terminus to v4.2.0 ([da8a630](https://github.com/nestjs/terminus/commit/da8a6304c1909350c53a02712fb2163b448dbab4))
* **deps:** update dependency @grpc/proto-loader to v0.5.2 ([a343c2b](https://github.com/nestjs/terminus/commit/a343c2b5335d97fb487e9d008091893136fa7b57))
* **deps:** update dependency fastify to v2.8.0 ([1c4f27c](https://github.com/nestjs/terminus/commit/1c4f27ca60f268d503c2040c4cca39bf5c4f9e85))
* **deps:** update dependency grpc to v1.23.2 ([8e137eb](https://github.com/nestjs/terminus/commit/8e137eb467246c01d14a76e1597cfca302cb5bdc))
* **deps:** update dependency grpc to v1.23.3 ([bd420cf](https://github.com/nestjs/terminus/commit/bd420cfacbf2b04b843ea87e4be2e24dc704700f))
* **deps:** update dependency rxjs to v6.5.3 ([3b85165](https://github.com/nestjs/terminus/commit/3b85165249b768e0ef40b82f1cf921c7c8cb2371))
* **deps:** update dependency typescript to v3.6.2 ([e1d61d7](https://github.com/nestjs/terminus/commit/e1d61d79dc92dfc6aa022c9638b3fae5cec3ffd3))
* **deps:** update dependency typescript to v3.6.3 ([3c27173](https://github.com/nestjs/terminus/commit/3c27173ef3f7e937d9b7cfc9554a1beb840d1bcd))
* **deps:** update nest monorepo to v6.6.0 ([c01fa12](https://github.com/nestjs/terminus/commit/c01fa129c45a380920896f8d9cb9af3c57bbdfde))
* **deps:** update nest monorepo to v6.6.1 ([ef8e089](https://github.com/nestjs/terminus/commit/ef8e089944f5c93a2dfa2883e78c789b63794f8d))
* **deps:** update nest monorepo to v6.6.2 ([ee617d1](https://github.com/nestjs/terminus/commit/ee617d1a3f9e0147e2c406bbbc77304b6739d4b0))
* **deps:** update nest monorepo to v6.6.7 ([c392277](https://github.com/nestjs/terminus/commit/c392277779c911e4f9540dcfe80df327e28bc563))
* **deps:** update nest monorepo to v6.7.1 ([5cf25ec](https://github.com/nestjs/terminus/commit/5cf25ecf765fd1bd67d73f2cd5a52b57e1715d3e))
* **deps:** update nest monorepo to v6.7.2 ([b9a7dc6](https://github.com/nestjs/terminus/commit/b9a7dc6d71a69ada17cbddb6ba2c5b6349f6fc7f))
* do not fix peerDependencies ([bfbaf58](https://github.com/nestjs/terminus/commit/bfbaf58bb0fdeb99e3cea4b95269586f18011e7d)), closes [#363](https://github.com/nestjs/terminus/issues/363)



## [6.5.1](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-08-20)


### Bug Fixes

* **deps:** update dependency class-validator to v0.10.0 ([6098ede](https://github.com/nestjs/terminus/commit/6098ede9af97d92e882315290892f751135cf1de))
* **deps:** update dependency fastify to v2.7.0 ([04c8dd2](https://github.com/nestjs/terminus/commit/04c8dd2f83e9504de2c4930be9a89eb93cdf6d80))
* **deps:** update dependency fastify to v2.7.1 ([55ed78e](https://github.com/nestjs/terminus/commit/55ed78eb8ce1ce6d6887024081500191a775b3ab))
* **deps:** update dependency grpc to v1.23.1 ([d661034](https://github.com/nestjs/terminus/commit/d661034802167e586f44f56c57567c69ee8d148f))
* **deps:** update dependency lodash to v4.17.15 ([208cf17](https://github.com/nestjs/terminus/commit/208cf17b1107e053aaa3fec5dbc805ed21501ec0))
* **deps:** update dependency rimraf to v2.7.0 ([c3b697f](https://github.com/nestjs/terminus/commit/c3b697fd27c8f5179073d63abde4b69010006d57))
* **deps:** update dependency rimraf to v2.7.1 ([6358551](https://github.com/nestjs/terminus/commit/6358551b142f961128c42cff331c0e1cf655b568))
* **deps:** update dependency rimraf to v3 ([8b60199](https://github.com/nestjs/terminus/commit/8b60199b8836b8dff3c3e69b6f664577ab775fc5))
* **deps:** update nest monorepo to v6.5.3 ([bf7054f](https://github.com/nestjs/terminus/commit/bf7054f464a7d92c10e41b382d4139178386f85a))
* **microservice:** Disconnect MS on ping check ([9b88e2e](https://github.com/nestjs/terminus/commit/9b88e2edf5186feeb035f0884eaede56b2770d4d))



# [6.5.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-07-15)


### Bug Fixes

* **002:** Add skipLibCheck to tsconfig ([cc3d47e](https://github.com/nestjs/terminus/commit/cc3d47e9dbf9092e4d340f2e4b03434c292d1153))
* **deps:** update dependency grpc to v1.22.0 ([966338b](https://github.com/nestjs/terminus/commit/966338b723af2a100030759d7675c93d1fe3b883))
* **deps:** update dependency grpc to v1.22.1 ([a1c2241](https://github.com/nestjs/terminus/commit/a1c2241db78a45f2e87305c61e8133dbf385e9b9))
* **deps:** update dependency grpc to v1.22.2 ([b2d0612](https://github.com/nestjs/terminus/commit/b2d0612e3030a2c798bc50abf2889aae1f3b540d))
* **deps:** update dependency lodash to v4.17.14 ([28aab23](https://github.com/nestjs/terminus/commit/28aab23cd4630846998fba1ce34e4b5361481572))
* **deps:** update dependency typescript to v3.5.3 ([664f339](https://github.com/nestjs/terminus/commit/664f3392635cffcff760993a6b3a457f09108722))
* **deps:** update nest monorepo to v6.5.2 ([ac65e47](https://github.com/nestjs/terminus/commit/ac65e47041d19a8f6dbb7a887495b5c06f1fbb13))


### Features

* **bootstrap:** Add `useGlobalPrefix` option ([a5f0d2e](https://github.com/nestjs/terminus/commit/a5f0d2e1a1625f89daaea950a820d2ebbc85597b)), closes [#228](https://github.com/nestjs/terminus/issues/228)



## [6.3.4](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-06-28)


### Bug Fixes

* **deps:** update dependency fastify to v2.6.0 ([05158fb](https://github.com/nestjs/terminus/commit/05158fb89f4e694a5c316486ade3e9c08706dfbc))
* **disk:** Use types from check-disk-space package ([40d16cf](https://github.com/nestjs/terminus/commit/40d16cff06723566ef75ae1b1c07d2b820398243))
* Accept any in TerminusLogger as error ([3e20535](https://github.com/nestjs/terminus/commit/3e2053507b95bce7d0a598f06b27c0aa292b21e0))
* Update tests to @godaddy/terminus@4.1.2 ([b68f57b](https://github.com/nestjs/terminus/commit/b68f57bcdbfdc11920a6085359296fc894885cce))
* Use Mongoose full import to prevent having to install typings ([5550029](https://github.com/nestjs/terminus/commit/5550029222ab2ad8d0e5da8bb058e7162b904b3c))
* **deps:** update dependency @godaddy/terminus to v4.1.2 ([d1a5d8d](https://github.com/nestjs/terminus/commit/d1a5d8d38ee134e900abcfefb592327d1309cce6))
* **deps:** update dependency fastify to v2.5.0 ([47a078a](https://github.com/nestjs/terminus/commit/47a078a6d674339b92011169e39b6b18df6211cd))
* **deps:** update dependency typescript to v3.5.2 ([a3ea1d0](https://github.com/nestjs/terminus/commit/a3ea1d0f64540a421c3aa3130d91bc6dc87403c1))


### Features

* **typeorm:** Add mongo support ([83e930f](https://github.com/nestjs/terminus/commit/83e930f8d4f4ae0c778a0f9f9b304c65b45a1b23)), closes [#222](https://github.com/nestjs/terminus/issues/222)



## [6.3.3](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-06-11)


### Bug Fixes

* **build:** Allow empty dist folder for clean task ([d922e21](https://github.com/nestjs/terminus/commit/d922e215135ef47a32129a102b7ab82bd8333085))
* **grpc:** Do not use as any to convert GRPCClient ([1899588](https://github.com/nestjs/terminus/commit/1899588b701991243108358c0c595be7edc8bc39))
* **grpc:** Use Transport only when microservices module is installed ([488164f](https://github.com/nestjs/terminus/commit/488164f91187a7ed2cd79a56139bdc8c68003952))


### Features

* **sample:** Use @nestjs/terminus import path instead of relative ([c159eb3](https://github.com/nestjs/terminus/commit/c159eb3f12653dc136bb5761cfb74726821f9846))



## [6.3.1](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-06-06)


### Features

* **build:** Use gulp for building the lib ([d68cf97](https://github.com/nestjs/terminus/commit/d68cf976f113b7af2341c19b2445507bf1d04be7))



# [6.3.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-06-05)


### Bug Fixes

* Update module interface to nest 6.3.0 standard ([9748cc4](https://github.com/nestjs/terminus/commit/9748cc49e9b256074b884e401ceb657b7bc32031))
* **deps:** Move unneeded peerDependencies to optionalDependencies ([0bd2978](https://github.com/nestjs/terminus/commit/0bd29787d9e0be08cd59b5cf4383cc1f76140575)), closes [#186](https://github.com/nestjs/terminus/issues/186)
* **deps:** pin dependencies ([0b10b02](https://github.com/nestjs/terminus/commit/0b10b02eabea8268c68c0f1ca15ceee40d4375c6))
* **deps:** pin dependency @nestjs/platform-express to 6.2.4 ([01904a8](https://github.com/nestjs/terminus/commit/01904a82cdbb3fe9b98fb8bbeab844150c2c7b22))
* **deps:** update dependency check-disk-space to v2 ([bf684ff](https://github.com/nestjs/terminus/commit/bf684ff0087d71d23a8b5cf9239a87c2969cb494))
* **deps:** update dependency class-transformer to v0.2.3 ([068de37](https://github.com/nestjs/terminus/commit/068de37f32ccc417d33cf9afe12b5efe9c32e65f))
* **deps:** update dependency grpc to v1.21.1 ([9551044](https://github.com/nestjs/terminus/commit/9551044d190ee6f1efd59bf6b93752ba272bb531))
* **deps:** update dependency mongoose to v5.5.10 ([7c449d4](https://github.com/nestjs/terminus/commit/7c449d40ce814380b8aa513b1c6e2533fbaedd08))
* **deps:** update dependency mongoose to v5.5.12 ([7365e90](https://github.com/nestjs/terminus/commit/7365e902137590f1fee2b9115d3bd1f3b2da6e9a))
* **deps:** update dependency mongoose to v5.5.9 ([0b5e980](https://github.com/nestjs/terminus/commit/0b5e9800aac32491bbe62b0dd9eb76a94f0d4c07))
* **deps:** update dependency typescript to v3.5.1 ([74e1250](https://github.com/nestjs/terminus/commit/74e1250e6fe9dbc0ed7a16ce99bedf8b09896497))
* **deps:** update nest monorepo to v6.2.4 ([64e0116](https://github.com/nestjs/terminus/commit/64e01160940a499681376f59e4dc38fc52400241))
* **mongoose:** Use connection.readyState to check DB ([63b97c1](https://github.com/nestjs/terminus/commit/63b97c128ad91c5753d33838c986df03274b59a2)), closes [#207](https://github.com/nestjs/terminus/issues/207)


### Features

* **@nestjs/terminus:** Add grpc health indicator ([179db8b](https://github.com/nestjs/terminus/commit/179db8b4bb272d18c58d9dfc2c71bca4b59325a4))
* **build:** Build samples in pipeline ([4f1fdfb](https://github.com/nestjs/terminus/commit/4f1fdfba1eb3f0519bcf1bf83af84cd37c186b30))



## [6.1.5](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-05-15)


### Bug Fixes

* **deps:** update dependency class-transformer to v0.2.2 ([81b8d69](https://github.com/nestjs/terminus/commit/81b8d69706b65a4c00a9f00c3334ad7e3f455330))
* **deps:** update dependency rxjs to v6.5.2 ([486f2ac](https://github.com/nestjs/terminus/commit/486f2acb5c47b515bd94922febc2967465851eaa))
* **deps:** update nest monorepo to v6.2.0 ([37ee4f1](https://github.com/nestjs/terminus/commit/37ee4f1eeed564bccfac168c3ea0c7c5e1124aaa))
* **doc:** Improve TSDoc comments for compodoc ([008be95](https://github.com/nestjs/terminus/commit/008be951378c1665d2c6e909107984e159bb6ae2))


### Features

* **terminus:** Add and export providers to terminus module ([596b1e2](https://github.com/nestjs/terminus/commit/596b1e2208b6a3da8ef92ddfddbc1cd64fb8a947))



## [6.1.4](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-04-28)


### Bug Fixes

* **typeorm:** change pingDb from public to private ([6bb1933](https://github.com/nestjs/terminus/commit/6bb1933f263e1717bb1498892a44f7d429a15227))



## [6.1.3](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-04-27)


### Bug Fixes

* **bootstrap:** Fix multiple error result surpression ([9899422](https://github.com/nestjs/terminus/commit/9899422cbc79c940fa803cbc79632b5df25c9a3a))
* **deps:** update dependency mongoose to v5.5.4 ([a115305](https://github.com/nestjs/terminus/commit/a115305add25df097081868b6c80a353a079e581))
* **deps:** update dependency rxjs to v6.5.1 ([9007360](https://github.com/nestjs/terminus/commit/900736043cc4c4ba5fb16a22b59c316d179f2fb7))
* **deps:** update dependency typescript to v3.4.5 ([a303ac1](https://github.com/nestjs/terminus/commit/a303ac18a4a24fbfb84ee54a1595d18e7f083257))



## [6.1.2](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-04-22)


### Bug Fixes

* **mongoose:** Fix default connection string ([4d188b4](https://github.com/nestjs/terminus/commit/4d188b4eb21288c57e1034f3ade2751be1d3a119))



## [6.1.1](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-04-22)


### Bug Fixes

* **@nestjs/terminus:** Deprecated comment ([c30db01](https://github.com/nestjs/terminus/commit/c30db014c37d61630f8a02be9bb4fefbe0577160))
* **database:** Fix searching for default connection ([8e43ce2](https://github.com/nestjs/terminus/commit/8e43ce2cb77a4ac0e8e3d890a6db730bde2d8730))
* **database:** Load database packages lazily ([5974b76](https://github.com/nestjs/terminus/commit/5974b7645a6a2e75e8b52675c684d7de341a4776)), closes [#119](https://github.com/nestjs/terminus/issues/119)
* **deps:** update dependency @nestjs/mongoose to v6.1.2 ([1032509](https://github.com/nestjs/terminus/commit/10325094176782859d112731e6de4c9b0ba9babf))
* **deps:** update dependency fastify to v2.3.0 ([936adda](https://github.com/nestjs/terminus/commit/936adda6ac831370f9dfdb401bd1418251193261))
* **deps:** update dependency mongoose to v5.5.2 ([ea94a1b](https://github.com/nestjs/terminus/commit/ea94a1b68e32e3a7aaef4c48e8199cd23f27127d))
* **deps:** update dependency typescript to v3.4.3 ([4fe6e00](https://github.com/nestjs/terminus/commit/4fe6e002d71dcf4c42374365be29d97e3c2b0c48))
* **deps:** update dependency typescript to v3.4.4 ([712a066](https://github.com/nestjs/terminus/commit/712a06608b7056e7b0c1737bcd89674e5e6f1476))
* **deps:** update nest monorepo to v6.1.0 ([2541824](https://github.com/nestjs/terminus/commit/25418247c5e2b55112c28dc32dae731bfa85c162))
* **deps:** update nest monorepo to v6.1.1 ([224ba74](https://github.com/nestjs/terminus/commit/224ba74636d8c4e2a49646b96163a798d27dc209))
* **microservice:** Load @nestjs/microservices package lazily ([da72a2e](https://github.com/nestjs/terminus/commit/da72a2e0ecd1bae92df3e7b67fd47839bf07ff87)), closes [#119](https://github.com/nestjs/terminus/issues/119)



# [6.1.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-04-07)


### Bug Fixes

* **deps:** pin dependency lodash to 4.17.11 ([b0bf078](https://github.com/nestjs/terminus/commit/b0bf07893a559e96d42e1e6300ed73a5f0bf009d))
* **deps:** update dependency @nestjs/mongoose to v6 ([fce59b0](https://github.com/nestjs/terminus/commit/fce59b01c350cfa61af14fd91be740efe84320bf))
* **deps:** update dependency class-transformer to v0.2.0 ([e03f010](https://github.com/nestjs/terminus/commit/e03f0101d45ede6df647de4e55523dff5d01649e))
* **deps:** update dependency class-validator to v0.9.1 ([f185176](https://github.com/nestjs/terminus/commit/f185176b4a03cd8957b1c8cc3f107a0c85adb9d5))
* **deps:** update dependency fastify to v1.14.4 ([1313840](https://github.com/nestjs/terminus/commit/131384017cf5514e8308c85c7b5fc4faca73b7ec))
* **deps:** update dependency fastify to v2.1.0 ([1f31f0d](https://github.com/nestjs/terminus/commit/1f31f0ddd48df266389dd39ec154502736f3ae13))
* **deps:** update dependency fastify to v2.2.0 ([fefed02](https://github.com/nestjs/terminus/commit/fefed027634fdeaa99a75f41ee5eae11f2ede6cb))
* **deps:** update dependency lodash to v4.17.11 [security] ([daaaa90](https://github.com/nestjs/terminus/commit/daaaa901f44abdcf581decbff63c17e4b5839a41))
* **deps:** update dependency mongoose to v5.4.22 ([4940d6d](https://github.com/nestjs/terminus/commit/4940d6d598d1a71ed8fb2cdfe0adfabb0fbd6c45))
* **deps:** update dependency reflect-metadata to v0.1.13 ([5533183](https://github.com/nestjs/terminus/commit/553318399df88fe00b2acfe625c224a3992e31d0))
* **deps:** update dependency rxjs to v6.4.0 ([d3febb6](https://github.com/nestjs/terminus/commit/d3febb65d5ca29c862716b9e3bd5aafc6d23fcf1))
* **deps:** update dependency typescript to v3.3.3333 ([9e81fec](https://github.com/nestjs/terminus/commit/9e81fec9f52780d0490737f90be80eaa79fc7ba0))
* **deps:** update dependency typescript to v3.3.4000 ([917aec3](https://github.com/nestjs/terminus/commit/917aec3764fed2de7d75354b633371acbb0a51cf))
* **deps:** update dependency typescript to v3.4.2 ([05b61a9](https://github.com/nestjs/terminus/commit/05b61a9298f00f0266cdb9698c6acd48877895a5))
* **deps:** update nest monorepo to v6 ([271b2fa](https://github.com/nestjs/terminus/commit/271b2faa33d63a26663088b833f46f30267d4fe8))
* **disk:** Fix jsdoc comment on check method ([bac4854](https://github.com/nestjs/terminus/commit/bac4854ad24baba59cf11a870b5152d96dbfe489))


### Features

* **@nestjs/terminus:** Add disk health indicator ([42387ca](https://github.com/nestjs/terminus/commit/42387ca8c697b95aee6960dac72908bdb495a38b))
* **@nestjs/terminus:** Add MemoryHealthIndicator ([2666161](https://github.com/nestjs/terminus/commit/266616192228183eef0c8538e60b5bdf69a09dea))



# [6.0.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-03-17)


### Bug Fixes

* **terminus:** Change AppRefHost to HttpAdapterHost ([52e212b](https://github.com/nestjs/terminus/commit/52e212b3d5fe34b169aaa1f226856f6fd6c5fdb4))



# [5.6.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2019-03-10)


### Bug Fixes

* **bootsrap:** Use appRef.instance.server for fastify httpServer ([c3cf3b9](https://github.com/nestjs/terminus/commit/c3cf3b9977e8c2144cdda0dae58c84704db6d8b2)), closes [#24](https://github.com/nestjs/terminus/issues/24)
* **build:** Move mysql to devdependencies ([c5431f2](https://github.com/nestjs/terminus/commit/c5431f22f6f4d8e3d044a7d8e31a9c37fd9a0e6f))
* **test:** unneeded imports ([235ea30](https://github.com/nestjs/terminus/commit/235ea30cbebb3d574ad1a1c5f005ac0f3d485c62))


### Features

* **@nestjs/terminus:** Add microservice health indicator ([7cc931a](https://github.com/nestjs/terminus/commit/7cc931ab308055931b8d6260528c64793977a5cb))



## [5.5.1](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2018-12-17)


### Features

* **core:** Add ability to configure logger ([0dcb432](https://github.com/nestjs/terminus/commit/0dcb43207929ce993ae2c9045f6d9af2d32209a7)), closes [#19](https://github.com/nestjs/terminus/issues/19)
* **health:** Log health check status on error ([64dca1e](https://github.com/nestjs/terminus/commit/64dca1e36995854f2666bf0d6f54deb5a7615a84))
* **health:** Log healthcheck  registration to nest logger ([ccd77fb](https://github.com/nestjs/terminus/commit/ccd77fb2d30e26c9ef7509a1398e505c5da64cf0))



# [5.4.0-rc.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2018-11-07)


### Bug Fixes

* **npm:** Make npm package smaller ([5ecbaec](https://github.com/nestjs/terminus/commit/5ecbaec8401087ecdf921c6e01eb2b1913ae6589))
* **package:** typeorm package dependencies ([bf7a4d9](https://github.com/nestjs/terminus/commit/bf7a4d90d10746a1c0ebf4b1ca5f6d5949dae735))
* **package:** typeorm package dependencies ([0f292bb](https://github.com/nestjs/terminus/commit/0f292bbd8ab8a4c1011cdbf1ebf8ad5e0017004e))
* **test:** docker compose configuration ([a069b19](https://github.com/nestjs/terminus/commit/a069b196a3c278adde9caba72d336bb1efb0d0df))
* **test:** Fix ts error on compilation ([8e69f05](https://github.com/nestjs/terminus/commit/8e69f058ccf7ff7604de20a04170bd452164871b))
* **travis:** Fix mysql database connection in e2e tests ([0420caa](https://github.com/nestjs/terminus/commit/0420caa0699219fc78738eb1f32c2960c6ae1a59))
* **travis:** Fix mysql database connection in e2e tests ([6c26dc8](https://github.com/nestjs/terminus/commit/6c26dc8839ee7f094654b88939ccf77710b75ec7))


### Features

* **database:** Add database health indicator ([bdd4652](https://github.com/nestjs/terminus/commit/bdd46526a18784151ea0ce8ab9bef6e61d14993b))
* **health:** Add dns ping check ([cdaa695](https://github.com/nestjs/terminus/commit/cdaa695fa858e71c37427b2c155b2bb7642ebd0f))



# [5.3.0](https://github.com/nestjs/terminus/compare/7.0.1...7.1.0-next.0) (2018-09-03)