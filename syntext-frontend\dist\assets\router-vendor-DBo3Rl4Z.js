import{r as e}from"./redux-vendor-bhcDk3Gk.js";
/**
 * react-router v7.7.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */var t="popstate";function n(e={}){return function(e,n,a,u={}){let{window:s=document.defaultView,v5Compat:c=!1}=u,h=s.history,p="POP",d=null,m=f();null==m&&(m=0,h.replaceState({...h.state,idx:m},""));function f(){return(h.state||{idx:null}).idx}function v(){p="POP";let e=f(),t=null==e?null:e-m;m=e,d&&d({action:p,location:b.location,delta:t})}function y(e,t){p="PUSH";let n=i(b.location,e,t);m=f()+1;let r=o(n,m),a=b.createHref(n);try{h.pushState(r,"",a)}catch(l){if(l instanceof DOMException&&"DataCloneError"===l.name)throw l;s.location.assign(a)}c&&d&&d({action:p,location:b.location,delta:1})}function g(e,t){p="REPLACE";let n=i(b.location,e,t);m=f();let r=o(n,m),a=b.createHref(n);h.replaceState(r,"",a),c&&d&&d({action:p,location:b.location,delta:0})}function w(e){return function(e,t=!1){let n="http://localhost";"undefined"!=typeof window&&(n="null"!==window.location.origin?window.location.origin:window.location.href);r(n,"No window.location.(origin|href) available to create URL");let a="string"==typeof e?e:l(e);a=a.replace(/ $/,"%20"),!t&&a.startsWith("//")&&(a=n+a);return new URL(a,n)}(e)}let b={get action(){return p},get location(){return e(s,h)},listen(e){if(d)throw new Error("A history only accepts one active listener");return s.addEventListener(t,v),d=e,()=>{s.removeEventListener(t,v),d=null}},createHref:e=>n(s,e),createURL:w,encodeLocation(e){let t=w(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:y,replace:g,go:e=>h.go(e)};return b}(function(e,t){let{pathname:n,search:r,hash:a}=e.location;return i("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){return"string"==typeof t?t:l(t)},0,e)}function r(e,t){if(!1===e||null==e)throw new Error(t)}function a(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function o(e,t){return{usr:e.state,key:e.key,idx:t}}function i(e,t,n=null,r){return{pathname:"string"==typeof e?e:e.pathname,search:"",hash:"",..."string"==typeof t?u(t):t,state:n,key:t&&t.key||r||Math.random().toString(36).substring(2,10)}}function l({pathname:e="/",search:t="",hash:n=""}){return t&&"?"!==t&&(e+="?"===t.charAt(0)?t:"?"+t),n&&"#"!==n&&(e+="#"===n.charAt(0)?n:"#"+n),e}function u(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substring(n),e=e.substring(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substring(r),e=e.substring(0,r)),e&&(t.pathname=e)}return t}function s(e,t,n="/"){return function(e,t,n,r){let a="string"==typeof t?u(t):t,o=E(a.pathname||"/",n);if(null==o)return null;let i=c(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(i);let l=null;for(let u=0;null==l&&u<i.length;++u){let e=R(o);l=b(i[u],e,r)}return l}(e,t,n,!1)}function c(e,t=[],n=[],a=""){let o=(e,o,i)=>{let l={relativePath:void 0===i?e.path||"":i,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};l.relativePath.startsWith("/")&&(r(l.relativePath.startsWith(a),`Absolute route path "${l.relativePath}" nested under path "${a}" is not valid. An absolute child route path must start with the combined path of all its parent routes.`),l.relativePath=l.relativePath.slice(a.length));let u=L([a,l.relativePath]),s=n.concat(l);e.children&&e.children.length>0&&(r(!0!==e.index,`Index routes must not have child routes. Please remove all child routes from route path "${u}".`),c(e.children,t,s,u)),(null!=e.path||e.index)&&t.push({path:u,score:w(u,e.index),routesMeta:s})};return e.forEach((e,t)=>{if(""!==e.path&&e.path?.includes("?"))for(let n of h(e.path))o(e,t,n);else o(e,t)}),t}function h(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=h(r.join("/")),l=[];return l.push(...i.map(e=>""===e?o:[o,e].join("/"))),a&&l.push(...i),l.map(t=>e.startsWith("/")&&""===t?"/":t)}var p=/^:[\w-]+$/,d=3,m=2,f=1,v=10,y=-2,g=e=>"*"===e;function w(e,t){let n=e.split("/"),r=n.length;return n.some(g)&&(r+=y),t&&(r+=m),n.filter(e=>!g(e)).reduce((e,t)=>e+(p.test(t)?d:""===t?f:v),r)}function b(e,t,n=!1){let{routesMeta:r}=e,a={},o="/",i=[];for(let l=0;l<r.length;++l){let e=r[l],u=l===r.length-1,s="/"===o?t:t.slice(o.length)||"/",c=x({path:e.relativePath,caseSensitive:e.caseSensitive,end:u},s),h=e.route;if(!c&&u&&n&&!r[r.length-1].route.index&&(c=x({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},s)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:L([o,c.pathname]),pathnameBase:k(L([o,c.pathnameBase])),route:h}),"/"!==c.pathnameBase&&(o=L([o,c.pathnameBase]))}return i}function x(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t=!1,n=!0){a("*"===e||!e.endsWith("*")||e.endsWith("/*"),`Route path "${e}" will be treated as if it were "${e.replace(/\*$/,"/*")}" because the \`*\` character must always follow a \`/\` in the pattern. To get rid of this warning, please change the route path to "${e.replace(/\*$/,"/*")}".`);let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))");let i=new RegExp(o,t?void 0:"i");return[i,r]}(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let i=o[0],l=i.replace(/(.)\/+$/,"$1"),u=o.slice(1);return{params:r.reduce((e,{paramName:t,isOptional:n},r)=>{if("*"===t){let e=u[r]||"";l=i.slice(0,i.length-e.length).replace(/(.)\/+$/,"$1")}const a=u[r];return e[t]=n&&!a?void 0:(a||"").replace(/%2F/g,"/"),e},{}),pathname:i,pathnameBase:l,pattern:e}}function R(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return a(!1,`The URL path "${e}" could not be decoded because it is a malformed URL segment. This is probably due to a bad percent encoding (${t}).`),e}}function E(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function C(e,t,n,r){return`Cannot include a '${e}' character in a manually specified \`to.${t}\` field [${JSON.stringify(r)}].  Please separate it out to the \`to.${n}\` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.`}function S(e){let t=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t.map((e,n)=>n===t.length-1?e.pathname:e.pathnameBase)}function $(e,t,n,a=!1){let o;"string"==typeof e?o=u(e):(o={...e},r(!o.pathname||!o.pathname.includes("?"),C("?","pathname","search",o)),r(!o.pathname||!o.pathname.includes("#"),C("#","pathname","hash",o)),r(!o.search||!o.search.includes("#"),C("#","search","hash",o)));let i,l=""===e||""===o.pathname,s=l?"/":o.pathname;if(null==s)i=n;else{let e=t.length-1;if(!a&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}i=e>=0?t[e]:"/"}let c=function(e,t="/"){let{pathname:n,search:r="",hash:a=""}="string"==typeof e?u(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:P(r),hash:T(a)}}(o,i),h=s&&"/"!==s&&s.endsWith("/"),p=(l||"."===s)&&n.endsWith("/");return c.pathname.endsWith("/")||!h&&!p||(c.pathname+="/"),c}var L=e=>e.join("/").replace(/\/\/+/g,"/"),k=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),P=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",T=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";var N=["POST","PUT","PATCH","DELETE"];new Set(N);var F=["GET",...N];new Set(F);var M=e.createContext(null);M.displayName="DataRouter";var A=e.createContext(null);A.displayName="DataRouterState",e.createContext(!1);var O=e.createContext({isTransitioning:!1});O.displayName="ViewTransition",e.createContext(new Map).displayName="Fetchers",e.createContext(null).displayName="Await";var D=e.createContext(null);D.displayName="Navigation";var W=e.createContext(null);W.displayName="Location";var B=e.createContext({outlet:null,matches:[],isDataRoute:!1});B.displayName="Route";var U=e.createContext(null);function j(){return null!=e.useContext(W)}function _(){return r(j(),"useLocation() may be used only in the context of a <Router> component."),e.useContext(W).location}U.displayName="RouteError";var H="You should call navigate() in a React.useEffect(), not when your component is first rendered.";function I(t){e.useContext(D).static||e.useLayoutEffect(t)}function z(){let{isDataRoute:t}=e.useContext(B);return t?function(){let{router:t}=function(t){let n=e.useContext(M);return r(n,X(t)),n}("useNavigate"),n=Q("useNavigate"),o=e.useRef(!1);return I(()=>{o.current=!0}),e.useCallback(async(e,r={})=>{a(o.current,H),o.current&&("number"==typeof e?t.navigate(e):await t.navigate(e,{fromRouteId:n,...r}))},[t,n])}():function(){r(j(),"useNavigate() may be used only in the context of a <Router> component.");let t=e.useContext(M),{basename:n,navigator:o}=e.useContext(D),{matches:i}=e.useContext(B),{pathname:l}=_(),u=JSON.stringify(S(i)),s=e.useRef(!1);return I(()=>{s.current=!0}),e.useCallback((e,r={})=>{if(a(s.current,H),!s.current)return;if("number"==typeof e)return void o.go(e);let i=$(e,JSON.parse(u),l,"path"===r.relative);null==t&&"/"!==n&&(i.pathname="/"===i.pathname?n:L([n,i.pathname])),(r.replace?o.replace:o.push)(i,r.state,r)},[n,o,u,l,t])}()}function J(t,{relative:n}={}){let{matches:r}=e.useContext(B),{pathname:a}=_(),o=JSON.stringify(S(r));return e.useMemo(()=>$(t,JSON.parse(o),a,"path"===n),[t,o,a,n])}function Y(t,n,o,i){r(j(),"useRoutes() may be used only in the context of a <Router> component.");let{navigator:l}=e.useContext(D),{matches:c}=e.useContext(B),h=c[c.length-1],p=h?h.params:{},d=h?h.pathname:"/",m=h?h.pathnameBase:"/",f=h&&h.route;{let e=f&&f.path||"";ee(d,!f||e.endsWith("*")||e.endsWith("*?"),`You rendered descendant <Routes> (or called \`useRoutes()\`) at "${d}" (under <Route path="${e}">) but the parent route path has no trailing "*". This means if you navigate deeper, the parent won't match anymore and therefore the child routes will never render.\n\nPlease change the parent <Route path="${e}"> to <Route path="${"/"===e?"*":`${e}/*`}">.`)}let v,y=_();if(n){let e="string"==typeof n?u(n):n;r("/"===m||e.pathname?.startsWith(m),`When overriding the location using \`<Routes location>\` or \`useRoutes(routes, location)\`, the location pathname must begin with the portion of the URL pathname that was matched by all parent routes. The current pathname base is "${m}" but pathname "${e.pathname}" was given in the \`location\` prop.`),v=e}else v=y;let g=v.pathname||"/",w=g;if("/"!==m){let e=m.replace(/^\//,"").split("/");w="/"+g.replace(/^\//,"").split("/").slice(e.length).join("/")}let b=s(t,{pathname:w});a(f||null!=b,`No routes matched location "${v.pathname}${v.search}${v.hash}" `),a(null==b||void 0!==b[b.length-1].route.element||void 0!==b[b.length-1].route.Component||void 0!==b[b.length-1].route.lazy,`Matched leaf route at location "${v.pathname}${v.search}${v.hash}" does not have an element or Component. This means it will render an <Outlet /> with a null value by default resulting in an "empty" page.`);let x=function(t,n=[],a=null){if(null==t){if(!a)return null;if(a.errors)t=a.matches;else{if(0!==n.length||a.initialized||!(a.matches.length>0))return null;t=a.matches}}let o=t,i=a?.errors;if(null!=i){let e=o.findIndex(e=>e.route.id&&void 0!==i?.[e.route.id]);r(e>=0,`Could not find a matching route for errors on route IDs: ${Object.keys(i).join(",")}`),o=o.slice(0,Math.min(o.length,e+1))}let l=!1,u=-1;if(a)for(let e=0;e<o.length;e++){let t=o[e];if((t.route.HydrateFallback||t.route.hydrateFallbackElement)&&(u=e),t.route.id){let{loaderData:e,errors:n}=a,r=t.route.loader&&!e.hasOwnProperty(t.route.id)&&(!n||void 0===n[t.route.id]);if(t.route.lazy||r){l=!0,o=u>=0?o.slice(0,u+1):[o[0]];break}}}return o.reduceRight((t,r,s)=>{let c,h=!1,p=null,d=null;a&&(c=i&&r.route.id?i[r.route.id]:void 0,p=r.route.errorElement||V,l&&(u<0&&0===s?(ee("route-fallback",!1,"No `HydrateFallback` element provided to render during initial hydration"),h=!0,d=null):u===s&&(h=!0,d=r.route.hydrateFallbackElement||null)));let m=n.concat(o.slice(0,s+1)),f=()=>{let n;return n=c?p:h?d:r.route.Component?e.createElement(r.route.Component,null):r.route.element?r.route.element:t,e.createElement(G,{match:r,routeContext:{outlet:t,matches:m,isDataRoute:null!=a},children:n})};return a&&(r.route.ErrorBoundary||r.route.errorElement||0===s)?e.createElement(q,{location:a.location,revalidation:a.revalidation,component:p,error:c,children:f(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):f()},null)}(b&&b.map(e=>Object.assign({},e,{params:Object.assign({},p,e.params),pathname:L([m,l.encodeLocation?l.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?m:L([m,l.encodeLocation?l.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),c,o,i);return n&&x?e.createElement(W.Provider,{value:{location:{pathname:"/",search:"",hash:"",state:null,key:"default",...v},navigationType:"POP"}},x):x}function K(){let t=function(){let t=e.useContext(U),n=function(t){let n=e.useContext(A);return r(n,X(t)),n}("useRouteError"),a=Q("useRouteError");if(void 0!==t)return t;return n.errors?.[a]}(),n=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(t)?`${t.status} ${t.statusText}`:t instanceof Error?t.message:JSON.stringify(t),a=t instanceof Error?t.stack:null,o="rgba(200,200,200, 0.5)",i={padding:"0.5rem",backgroundColor:o},l={padding:"2px 4px",backgroundColor:o},u=null;return console.error("Error handled by React Router default ErrorBoundary:",t),u=e.createElement(e.Fragment,null,e.createElement("p",null,"💿 Hey developer 👋"),e.createElement("p",null,"You can provide a way better UX than this when your app throws errors by providing your own ",e.createElement("code",{style:l},"ErrorBoundary")," or"," ",e.createElement("code",{style:l},"errorElement")," prop on your route.")),e.createElement(e.Fragment,null,e.createElement("h2",null,"Unexpected Application Error!"),e.createElement("h3",{style:{fontStyle:"italic"}},n),a?e.createElement("pre",{style:i},a):null,u)}e.createContext(null);var V=e.createElement(K,null),q=class extends e.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?e.createElement(B.Provider,{value:this.props.routeContext},e.createElement(U.Provider,{value:this.state.error,children:this.props.component})):this.props.children}};function G({routeContext:t,match:n,children:r}){let a=e.useContext(M);return a&&a.static&&a.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=n.route.id),e.createElement(B.Provider,{value:t},r)}function X(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}function Q(t){let n=function(t){let n=e.useContext(B);return r(n,X(t)),n}(t),a=n.matches[n.matches.length-1];return r(a.route.id,`${t} can only be used on routes that contain a unique "id"`),a.route.id}var Z={};function ee(e,t,n){t||Z[e]||(Z[e]=!0,a(!1,n))}function te({to:t,replace:n,state:o,relative:i}){r(j(),"<Navigate> may be used only in the context of a <Router> component.");let{static:l}=e.useContext(D);a(!l,"<Navigate> must not be used on the initial render in a <StaticRouter>. This is a no-op, but you should modify your code so the <Navigate> is only ever rendered in response to some user interaction or state change.");let{matches:u}=e.useContext(B),{pathname:s}=_(),c=z(),h=$(t,S(u),s,"path"===i),p=JSON.stringify(h);return e.useEffect(()=>{c(JSON.parse(p),{replace:n,state:o,relative:i})},[c,p,i,n,o]),null}function ne(e){r(!1,"A <Route> is only ever to be used as the child of <Routes> element, never rendered directly. Please wrap your <Route> in a <Routes>.")}function re({basename:t="/",children:n=null,location:o,navigationType:i="POP",navigator:l,static:s=!1}){r(!j(),"You cannot render a <Router> inside another <Router>. You should never have more than one in your app.");let c=t.replace(/^\/*/,"/"),h=e.useMemo(()=>({basename:c,navigator:l,static:s,future:{}}),[c,l,s]);"string"==typeof o&&(o=u(o));let{pathname:p="/",search:d="",hash:m="",state:f=null,key:v="default"}=o,y=e.useMemo(()=>{let e=E(p,c);return null==e?null:{location:{pathname:e,search:d,hash:m,state:f,key:v},navigationType:i}},[c,p,d,m,f,v,i]);return a(null!=y,`<Router basename="${c}"> is not able to match the URL "${p}${d}${m}" because it does not start with the basename, so the <Router> won't render anything.`),null==y?null:e.createElement(D.Provider,{value:h},e.createElement(W.Provider,{children:n,value:y}))}function ae({children:e,location:t}){return Y(oe(e),t)}function oe(t,n=[]){let a=[];return e.Children.forEach(t,(t,o)=>{if(!e.isValidElement(t))return;let i=[...n,o];if(t.type===e.Fragment)return void a.push.apply(a,oe(t.props.children,i));r(t.type===ne,`[${"string"==typeof t.type?t.type:t.type.name}] is not a <Route> component. All component children of <Routes> must be a <Route> or <React.Fragment>`),r(!t.props.index||!t.props.children,"An index route cannot have child routes.");let l={id:t.props.id||i.join("-"),caseSensitive:t.props.caseSensitive,element:t.props.element,Component:t.props.Component,index:t.props.index,path:t.props.path,loader:t.props.loader,action:t.props.action,hydrateFallbackElement:t.props.hydrateFallbackElement,HydrateFallback:t.props.HydrateFallback,errorElement:t.props.errorElement,ErrorBoundary:t.props.ErrorBoundary,hasErrorBoundary:!0===t.props.hasErrorBoundary||null!=t.props.ErrorBoundary||null!=t.props.errorElement,shouldRevalidate:t.props.shouldRevalidate,handle:t.props.handle,lazy:t.props.lazy};t.props.children&&(l.children=oe(t.props.children,i)),a.push(l)}),a}e.memo(function({routes:e,future:t,state:n}){return Y(e,void 0,n,t)});var ie="get",le="application/x-www-form-urlencoded";function ue(e){return null!=e&&"string"==typeof e.tagName}var se=null;var ce=new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);function he(e){return null==e||ce.has(e)?e:(a(!1,`"${e}" is not a valid \`encType\` for \`<Form>\`/\`<fetcher.Form>\` and will default to "${le}"`),null)}function pe(e,t){let n,r,a,o,i;if(ue(l=e)&&"form"===l.tagName.toLowerCase()){let i=e.getAttribute("action");r=i?E(i,t):null,n=e.getAttribute("method")||ie,a=he(e.getAttribute("enctype"))||le,o=new FormData(e)}else if(function(e){return ue(e)&&"button"===e.tagName.toLowerCase()}(e)||function(e){return ue(e)&&"input"===e.tagName.toLowerCase()}(e)&&("submit"===e.type||"image"===e.type)){let i=e.form;if(null==i)throw new Error('Cannot submit a <button> or <input type="submit"> without a <form>');let l=e.getAttribute("formaction")||i.getAttribute("action");if(r=l?E(l,t):null,n=e.getAttribute("formmethod")||i.getAttribute("method")||ie,a=he(e.getAttribute("formenctype"))||he(i.getAttribute("enctype"))||le,o=new FormData(i,e),!function(){if(null===se)try{new FormData(document.createElement("form"),0),se=!1}catch(e){se=!0}return se}()){let{name:t,type:n,value:r}=e;if("image"===n){let e=t?`${t}.`:"";o.append(`${e}x`,"0"),o.append(`${e}y`,"0")}else t&&o.append(t,r)}}else{if(ue(e))throw new Error('Cannot submit element that is not <form>, <button>, or <input type="submit|image">');n=ie,r=null,a=le,i=e}var l;return o&&"text/plain"===a&&(i=o,o=void 0),{action:r,method:n.toLowerCase(),encType:a,formData:o,body:i}}function de(e,t){if(!1===e||null==e)throw new Error(t)}function me(e){return null!=e&&(null==e.href?"preload"===e.rel&&"string"==typeof e.imageSrcSet&&"string"==typeof e.imageSizes:"string"==typeof e.rel&&"string"==typeof e.href)}async function fe(e,t,n){return function(e,t){let n=new Set;return new Set(t),e.reduce((e,t)=>{let r=JSON.stringify(function(e){let t={},n=Object.keys(e).sort();for(let r of n)t[r]=e[r];return t}(t));return n.has(r)||(n.add(r),e.push({key:r,link:t})),e},[])}((await Promise.all(e.map(async e=>{let r=t.routes[e.route.id];if(r){let e=await async function(e,t){if(e.id in t)return t[e.id];try{let n=await import(e.module);return t[e.id]=n,n}catch(n){return console.error(`Error loading route module \`${e.module}\`, reloading page...`),console.error(n),window.__reactRouterContext&&window.__reactRouterContext.isSpaMode,window.location.reload(),new Promise(()=>{})}}(r,n);return e.links?e.links():[]}return[]}))).flat(1).filter(me).filter(e=>"stylesheet"===e.rel||"preload"===e.rel).map(e=>"stylesheet"===e.rel?{...e,rel:"prefetch",as:"style"}:{...e,rel:"prefetch"}))}function ve(e,t,n,r,a,o){let i=(e,t)=>!n[t]||e.route.id!==n[t].route.id,l=(e,t)=>n[t].pathname!==e.pathname||n[t].route.path?.endsWith("*")&&n[t].params["*"]!==e.params["*"];return"assets"===o?t.filter((e,t)=>i(e,t)||l(e,t)):"data"===o?t.filter((t,o)=>{let u=r.routes[t.route.id];if(!u||!u.hasLoader)return!1;if(i(t,o)||l(t,o))return!0;if(t.route.shouldRevalidate){let r=t.route.shouldRevalidate({currentUrl:new URL(a.pathname+a.search+a.hash,window.origin),currentParams:n[0]?.params||{},nextUrl:new URL(e,window.origin),nextParams:t.params,defaultShouldRevalidate:!0});if("boolean"==typeof r)return r}return!0}):[]}function ye(e,t,{includeHydrateFallback:n}={}){return r=e.map(e=>{let r=t.routes[e.route.id];if(!r)return[];let a=[r.module];return r.clientActionModule&&(a=a.concat(r.clientActionModule)),r.clientLoaderModule&&(a=a.concat(r.clientLoaderModule)),n&&r.hydrateFallbackModule&&(a=a.concat(r.hydrateFallbackModule)),r.imports&&(a=a.concat(r.imports)),a}).flat(1),[...new Set(r)];var r}function ge(){let t=e.useContext(M);return de(t,"You must render this element inside a <DataRouterContext.Provider> element"),t}Object.getOwnPropertyNames(Object.prototype).sort().join("\0");var we=e.createContext(void 0);function be(){let t=e.useContext(we);return de(t,"You must render this element inside a <HydratedRouter> element"),t}function xe(e,t){return n=>{e&&e(n),n.defaultPrevented||t(n)}}function Re({page:t,...n}){let{router:r}=ge(),a=e.useMemo(()=>s(r.routes,t,r.basename),[r.routes,t,r.basename]);return a?e.createElement(Ee,{page:t,matches:a,...n}):null}function Ee({page:t,matches:n,...r}){let a=_(),{manifest:o,routeModules:i}=be(),{basename:l}=ge(),{loaderData:u,matches:s}=function(){let t=e.useContext(A);return de(t,"You must render this element inside a <DataRouterStateContext.Provider> element"),t}(),c=e.useMemo(()=>ve(t,n,s,o,a,"data"),[t,n,s,o,a]),h=e.useMemo(()=>ve(t,n,s,o,a,"assets"),[t,n,s,o,a]),p=e.useMemo(()=>{if(t===a.pathname+a.search+a.hash)return[];let e=new Set,r=!1;if(n.forEach(t=>{let n=o.routes[t.route.id];n&&n.hasLoader&&(!c.some(e=>e.route.id===t.route.id)&&t.route.id in u&&i[t.route.id]?.shouldRevalidate||n.hasClientLoader?r=!0:e.add(t.route.id))}),0===e.size)return[];let s=function(e,t,n){let r="string"==typeof e?new URL(e,"undefined"==typeof window?"server://singlefetch/":window.location.origin):e;return"/"===r.pathname?r.pathname=`_root.${n}`:t&&"/"===E(r.pathname,t)?r.pathname=`${t.replace(/\/$/,"")}/_root.${n}`:r.pathname=`${r.pathname.replace(/\/$/,"")}.${n}`,r}(t,l,"data");return r&&e.size>0&&s.searchParams.set("_routes",n.filter(t=>e.has(t.route.id)).map(e=>e.route.id).join(",")),[s.pathname+s.search]},[l,u,a,o,c,n,t,i]),d=e.useMemo(()=>ye(h,o),[h,o]),m=function(t){let{manifest:n,routeModules:r}=be(),[a,o]=e.useState([]);return e.useEffect(()=>{let e=!1;return fe(t,n,r).then(t=>{e||o(t)}),()=>{e=!0}},[t,n,r]),a}(h);return e.createElement(e.Fragment,null,p.map(t=>e.createElement("link",{key:t,rel:"prefetch",as:"fetch",href:t,...r})),d.map(t=>e.createElement("link",{key:t,rel:"modulepreload",href:t,...r})),m.map(({key:t,link:n})=>e.createElement("link",{key:t,...n})))}function Ce(...e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}we.displayName="FrameworkContext";var Se="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement;try{Se&&(window.__reactRouterVersion="7.7.1")}catch(Fe){}function $e({basename:t,children:r,window:a}){let o=e.useRef();null==o.current&&(o.current=n({window:a,v5Compat:!0}));let i=o.current,[l,u]=e.useState({action:i.action,location:i.location}),s=e.useCallback(t=>{e.startTransition(()=>u(t))},[u]);return e.useLayoutEffect(()=>i.listen(s),[i,s]),e.createElement(re,{basename:t,children:r,location:l.location,navigationType:l.action,navigator:i})}var Le=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ke=e.forwardRef(function({onClick:t,discover:n="render",prefetch:o="none",relative:i,reloadDocument:u,replace:s,state:c,target:h,to:p,preventScrollReset:d,viewTransition:m,...f},v){let y,{basename:g}=e.useContext(D),w="string"==typeof p&&Le.test(p),b=!1;if("string"==typeof p&&w&&(y=p,Se))try{let e=new URL(window.location.href),t=p.startsWith("//")?new URL(e.protocol+p):new URL(p),n=E(t.pathname,g);t.origin===e.origin&&null!=n?p=n+t.search+t.hash:b=!0}catch(Fe){a(!1,`<Link to="${p}"> contains an invalid URL which will probably break when clicked - please update to a valid URL path.`)}let x=function(t,{relative:n}={}){r(j(),"useHref() may be used only in the context of a <Router> component.");let{basename:a,navigator:o}=e.useContext(D),{hash:i,pathname:l,search:u}=J(t,{relative:n}),s=l;return"/"!==a&&(s="/"===l?a:L([a,l])),o.createHref({pathname:s,search:u,hash:i})}(p,{relative:i}),[R,C,S]=function(t,n){let r=e.useContext(we),[a,o]=e.useState(!1),[i,l]=e.useState(!1),{onFocus:u,onBlur:s,onMouseEnter:c,onMouseLeave:h,onTouchStart:p}=n,d=e.useRef(null);e.useEffect(()=>{if("render"===t&&l(!0),"viewport"===t){let e=new IntersectionObserver(e=>{e.forEach(e=>{l(e.isIntersecting)})},{threshold:.5});return d.current&&e.observe(d.current),()=>{e.disconnect()}}},[t]),e.useEffect(()=>{if(a){let e=setTimeout(()=>{l(!0)},100);return()=>{clearTimeout(e)}}},[a]);let m=()=>{o(!0)},f=()=>{o(!1),l(!1)};return r?"intent"!==t?[i,d,{}]:[i,d,{onFocus:xe(u,m),onBlur:xe(s,f),onMouseEnter:xe(c,m),onMouseLeave:xe(h,f),onTouchStart:xe(p,m)}]:[!1,d,{}]}(o,f),$=function(t,{target:n,replace:r,state:a,preventScrollReset:o,relative:i,viewTransition:u}={}){let s=z(),c=_(),h=J(t,{relative:i});return e.useCallback(e=>{if(function(e,t){return!(0!==e.button||t&&"_self"!==t||function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e))}(e,n)){e.preventDefault();let n=void 0!==r?r:l(c)===l(h);s(t,{replace:n,state:a,preventScrollReset:o,relative:i,viewTransition:u})}},[c,s,h,r,a,n,t,o,i,u])}(p,{replace:s,state:c,target:h,preventScrollReset:d,relative:i,viewTransition:m});let k=e.createElement("a",{...f,...S,href:y||x,onClick:b||u?t:function(e){t&&t(e),e.defaultPrevented||$(e)},ref:Ce(v,C),target:h,"data-discover":w||"render"!==n?void 0:"true"});return R&&!w?e.createElement(e.Fragment,null,k,e.createElement(Re,{page:x})):k});function Pe(t){let n=e.useContext(M);return r(n,function(e){return`${e} must be used within a data router.  See https://reactrouter.com/en/main/routers/picking-a-router.`}(t)),n}ke.displayName="Link",e.forwardRef(function({"aria-current":t="page",caseSensitive:n=!1,className:a="",end:o=!1,style:i,to:l,viewTransition:u,children:s,...c},h){let p=J(l,{relative:c.relative}),d=_(),m=e.useContext(A),{navigator:f,basename:v}=e.useContext(D),y=null!=m&&function(t,{relative:n}={}){let a=e.useContext(O);r(null!=a,"`useViewTransitionState` must be used within `react-router-dom`'s `RouterProvider`.  Did you accidentally import `RouterProvider` from `react-router`?");let{basename:o}=Pe("useViewTransitionState"),i=J(t,{relative:n});if(!a.isTransitioning)return!1;let l=E(a.currentLocation.pathname,o)||a.currentLocation.pathname,u=E(a.nextLocation.pathname,o)||a.nextLocation.pathname;return null!=x(i.pathname,u)||null!=x(i.pathname,l)}(p)&&!0===u,g=f.encodeLocation?f.encodeLocation(p).pathname:p.pathname,w=d.pathname,b=m&&m.navigation&&m.navigation.location?m.navigation.location.pathname:null;n||(w=w.toLowerCase(),b=b?b.toLowerCase():null,g=g.toLowerCase()),b&&v&&(b=E(b,v)||b);const R="/"!==g&&g.endsWith("/")?g.length-1:g.length;let C,S=w===g||!o&&w.startsWith(g)&&"/"===w.charAt(R),$=null!=b&&(b===g||!o&&b.startsWith(g)&&"/"===b.charAt(g.length)),L={isActive:S,isPending:$,isTransitioning:y},k=S?t:void 0;C="function"==typeof a?a(L):[a,S?"active":null,$?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let P="function"==typeof i?i(L):i;return e.createElement(ke,{...c,"aria-current":k,className:C,ref:h,style:P,to:l,viewTransition:u},"function"==typeof s?s(L):s)}).displayName="NavLink",e.forwardRef(({discover:t="render",fetcherKey:n,navigate:a,reloadDocument:o,replace:i,state:u,method:s=ie,action:c,onSubmit:h,relative:p,preventScrollReset:d,viewTransition:m,...f},v)=>{let y=function(){let{router:t}=Pe("useSubmit"),{basename:n}=e.useContext(D),r=Q("useRouteId");return e.useCallback(async(e,a={})=>{let{action:o,method:i,encType:l,formData:u,body:s}=pe(e,n);if(!1===a.navigate){let e=a.fetcherKey||Ne();await t.fetch(e,r,a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||i,formEncType:a.encType||l,flushSync:a.flushSync})}else await t.navigate(a.action||o,{preventScrollReset:a.preventScrollReset,formData:u,body:s,formMethod:a.method||i,formEncType:a.encType||l,replace:a.replace,state:a.state,fromRouteId:r,flushSync:a.flushSync,viewTransition:a.viewTransition})},[t,n,r])}(),g=function(t,{relative:n}={}){let{basename:a}=e.useContext(D),o=e.useContext(B);r(o,"useFormAction must be used inside a RouteContext");let[i]=o.matches.slice(-1),u={...J(t||".",{relative:n})},s=_();if(null==t){u.search=s.search;let e=new URLSearchParams(u.search),t=e.getAll("index");if(t.some(e=>""===e)){e.delete("index"),t.filter(e=>e).forEach(t=>e.append("index",t));let n=e.toString();u.search=n?`?${n}`:""}}t&&"."!==t||!i.route.index||(u.search=u.search?u.search.replace(/^\?/,"?index&"):"?index");"/"!==a&&(u.pathname="/"===u.pathname?a:L([a,u.pathname]));return l(u)}(c,{relative:p}),w="get"===s.toLowerCase()?"get":"post",b="string"==typeof c&&Le.test(c);return e.createElement("form",{ref:v,method:w,action:g,onSubmit:o?h:e=>{if(h&&h(e),e.defaultPrevented)return;e.preventDefault();let t=e.nativeEvent.submitter,r=t?.getAttribute("formmethod")||s;y(t||e.currentTarget,{fetcherKey:n,method:r,navigate:a,replace:i,state:u,relative:p,preventScrollReset:d,viewTransition:m})},...f,"data-discover":b||"render"!==t?void 0:"true"})}).displayName="Form";var Te=0,Ne=()=>`__${String(++Te)}__`;export{$e as B,ke as L,te as N,ae as R,ne as a,z as u};
//# sourceMappingURL=router-vendor-DBo3Rl4Z.js.map
