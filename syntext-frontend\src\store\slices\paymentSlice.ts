import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface Payment {
  paymentId: string;
  transactionId: string;
  amount: number;
  currency: string;
  method: 'credit_card' | 'alipay' | 'wechat_pay' | 'paypal' | 'bank_transfer';
  paymentUrl?: string;
  qrCode?: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  createdAt?: string;
}

export interface CouponValidation {
  valid: boolean;
  couponId?: string;
  couponName?: string;
  discountAmount?: number;
  finalAmount?: number;
  type?: 'percentage' | 'fixed_amount';
  value?: number;
  error?: string;
}

interface PaymentState {
  currentPayment: {
    data: Payment | null;
    loading: boolean;
    error: string | null;
  };
  coupon: {
    code: string;
    validation: CouponValidation | null;
    loading: boolean;
    error: string | null;
  };
  paymentHistory: {
    list: Payment[];
    loading: boolean;
    error: string | null;
  };
}

const initialState: PaymentState = {
  currentPayment: {
    data: null,
    loading: false,
    error: null,
  },
  coupon: {
    code: '',
    validation: null,
    loading: false,
    error: null,
  },
  paymentHistory: {
    list: [],
    loading: false,
    error: null,
  },
};

// Async thunks
export const createPayment = createAsyncThunk(
  'payment/create',
  async (data: {
    subscriptionId: string;
    amount: number;
    currency: string;
    method: string;
    description: string;
    couponId?: string;
    returnUrl?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await api.post('/payment/create', data);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建支付失败');
    }
  }
);

export const validateCoupon = createAsyncThunk(
  'payment/validateCoupon',
  async (data: {
    code: string;
    originalAmount: number;
    planType?: string;
  }, { rejectWithValue }) => {
    try {
      const response = await api.post('/coupon/validate', data);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '验证优惠码失败');
    }
  }
);

export const checkPaymentStatus = createAsyncThunk(
  'payment/checkStatus',
  async (paymentId: string, { rejectWithValue }) => {
    try {
      const response = await api.get(`/payment/${paymentId}/status`);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '查询支付状态失败');
    }
  }
);

export const fetchPaymentHistory = createAsyncThunk(
  'payment/fetchHistory',
  async (params: { limit?: number; offset?: number } = {}, { rejectWithValue }) => {
    try {
      const response = await api.get('/payment', { params });
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取支付历史失败');
    }
  }
);

const paymentSlice = createSlice({
  name: 'payment',
  initialState,
  reducers: {
    setCouponCode: (state, action: PayloadAction<string>) => {
      state.coupon.code = action.payload;
      if (!action.payload) {
        state.coupon.validation = null;
        state.coupon.error = null;
      }
    },
    clearCouponValidation: (state) => {
      state.coupon.validation = null;
      state.coupon.error = null;
    },
    clearPaymentError: (state) => {
      state.currentPayment.error = null;
    },
    clearCurrentPayment: (state) => {
      state.currentPayment.data = null;
      state.currentPayment.error = null;
    },
    updatePaymentStatus: (state, action: PayloadAction<{ paymentId: string; status: string }>) => {
      if (state.currentPayment.data?.paymentId === action.payload.paymentId) {
        state.currentPayment.data.status = action.payload.status as any;
      }
    },
  },
  extraReducers: (builder) => {
    // Create payment
    builder
      .addCase(createPayment.pending, (state) => {
        state.currentPayment.loading = true;
        state.currentPayment.error = null;
      })
      .addCase(createPayment.fulfilled, (state, action) => {
        state.currentPayment.loading = false;
        state.currentPayment.data = action.payload;
      })
      .addCase(createPayment.rejected, (state, action) => {
        state.currentPayment.loading = false;
        state.currentPayment.error = action.payload as string;
      });

    // Validate coupon
    builder
      .addCase(validateCoupon.pending, (state) => {
        state.coupon.loading = true;
        state.coupon.error = null;
      })
      .addCase(validateCoupon.fulfilled, (state, action) => {
        state.coupon.loading = false;
        state.coupon.validation = action.payload;
      })
      .addCase(validateCoupon.rejected, (state, action) => {
        state.coupon.loading = false;
        state.coupon.error = action.payload as string;
        state.coupon.validation = { valid: false, error: action.payload as string };
      });

    // Check payment status
    builder
      .addCase(checkPaymentStatus.fulfilled, (state, action) => {
        if (state.currentPayment.data) {
          state.currentPayment.data.status = action.payload.status;
        }
      });

    // Fetch payment history
    builder
      .addCase(fetchPaymentHistory.pending, (state) => {
        state.paymentHistory.loading = true;
        state.paymentHistory.error = null;
      })
      .addCase(fetchPaymentHistory.fulfilled, (state, action) => {
        state.paymentHistory.loading = false;
        state.paymentHistory.list = action.payload;
      })
      .addCase(fetchPaymentHistory.rejected, (state, action) => {
        state.paymentHistory.loading = false;
        state.paymentHistory.error = action.payload as string;
      });
  },
});

export const {
  setCouponCode,
  clearCouponValidation,
  clearPaymentError,
  clearCurrentPayment,
  updatePaymentStatus,
} = paymentSlice.actions;

export default paymentSlice.reducer;