import { MigrationInterface, QueryRunner, Table, TableIndex, TableForeignKey } from 'typeorm';

export class CreateTranslationTable1691000001000 implements MigrationInterface {
  name = 'CreateTranslationTable1691000001000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // 创建翻译状态枚举类型
    await queryRunner.query(`
      CREATE TYPE "translation_status" AS ENUM('pending', 'processing', 'completed', 'failed')
    `);

    // 创建翻译表
    await queryRunner.createTable(
      new Table({
        name: 'translations',
        columns: [
          {
            name: 'id',
            type: 'uuid',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid_generate_v4()',
          },
          {
            name: 'userId',
            type: 'uuid',
            isNullable: false,
          },
          {
            name: 'sourceText',
            type: 'text',
            isNullable: false,
          },
          {
            name: 'translatedText',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'sourceLanguage',
            type: 'varchar',
            length: '10',
            isNullable: false,
          },
          {
            name: 'targetLanguage',
            type: 'varchar',
            length: '10',
            isNullable: false,
          },
          {
            name: 'status',
            type: 'enum',
            enum: ['pending', 'processing', 'completed', 'failed'],
            default: "'pending'",
          },
          {
            name: 'characterCount',
            type: 'int',
            default: 0,
          },
          {
            name: 'processingTime',
            type: 'int',
            isNullable: true,
            comment: 'Processing time in milliseconds',
          },
          {
            name: 'errorMessage',
            type: 'text',
            isNullable: true,
          },
          {
            name: 'metadata',
            type: 'jsonb',
            default: "'{}'::jsonb",
          },
          {
            name: 'createdAt',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updatedAt',
            type: 'timestamp with time zone',
            default: 'CURRENT_TIMESTAMP',
          },
        ],
      }),
      true,
    );

    // 创建外键约束
    await queryRunner.createForeignKey(
      'translations',
      new TableForeignKey({
        columnNames: ['userId'],
        referencedColumnNames: ['id'],
        referencedTableName: 'users',
        onDelete: 'CASCADE',
      }),
    );

    // 创建索引
    await queryRunner.createIndex(
      'translations',
      new TableIndex({ name: 'IDX_translations_user_id', columnNames: ['userId'] }),
    );
    await queryRunner.createIndex(
      'translations',
      new TableIndex({ name: 'IDX_translations_status', columnNames: ['status'] }),
    );
    await queryRunner.createIndex(
      'translations',
      new TableIndex({ name: 'IDX_translations_created_at', columnNames: ['createdAt'] }),
    );
    await queryRunner.createIndex(
      'translations',
      new TableIndex({ name: 'IDX_translations_language_pair', columnNames: ['sourceLanguage', 'targetLanguage'] }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('translations');
    await queryRunner.query(`DROP TYPE "translation_status"`);
  }
}
