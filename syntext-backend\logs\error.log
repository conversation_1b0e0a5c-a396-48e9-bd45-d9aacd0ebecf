{"context":"ExceptionFilter","level":"error","message":"GET /health","stack":["NotFoundException: Cannot GET /health\n    at callback (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\@nestjs\\core\\router\\routes-resolver.js:77:19)\n    at E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\@nestjs\\core\\router\\router-proxy.js:9:23\n    at Layer.handleRequest (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:342:13)\n    at E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:297:9\n    at processParams (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:582:12)\n    at next (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:342:13)"],"timestamp":"2025-08-03T20:00:55.517Z"}
{"context":"ExceptionFilter","level":"error","message":"GET /favicon.ico","stack":["NotFoundException: Cannot GET /favicon.ico\n    at callback (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\@nestjs\\core\\router\\routes-resolver.js:77:19)\n    at E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\@nestjs\\core\\router\\router-proxy.js:9:23\n    at Layer.handleRequest (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:342:13)\n    at E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:297:9\n    at processParams (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:582:12)\n    at next (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:342:13)"],"timestamp":"2025-08-03T20:02:26.203Z"}
