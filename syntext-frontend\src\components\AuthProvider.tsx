import { useEffect } from 'react';
import { useAppDispatch } from '../hooks';
import { refreshToken, resetAuth } from '../store/slices/authSlice';
import api from '../services/api';

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider = ({ children }: AuthProviderProps) => {
  const dispatch = useAppDispatch();
  // Auth state is managed by Redux store

  useEffect(() => {
    const initializeAuth = async () => {
      const storedToken = localStorage.getItem('token');
      const storedRefreshToken = localStorage.getItem('refreshToken');

      if (storedToken && storedRefreshToken) {
        try {
          // 验证当前用户状态
          await api.post('/auth/me');
        } catch (_error) {
          // Token无效，尝试刷新
          try {
            await dispatch(refreshToken()).unwrap();
          } catch (_refreshError) {
            // 刷新失败，清除认证状态
            dispatch(resetAuth());
          }
        }
      } else if (storedToken || storedRefreshToken) {
        // 部分token存在，清除所有
        dispatch(resetAuth());
      }
    };

    initializeAuth();
  }, [dispatch]);

  return <>{children}</>;
};

export default AuthProvider;