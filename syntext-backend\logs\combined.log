{"context":"WebSocketsController","level":"info","message":"<PERSON><PERSON>ateway subscribed to the \"translation:start\" message","timestamp":"2025-08-03T18:09:27.860Z"}
{"context":"WebSocketsController","level":"info","message":"<PERSON>G<PERSON><PERSON> subscribed to the \"translation:batch\" message","timestamp":"2025-08-03T18:09:27.862Z"}
{"context":"WebSocketsController","level":"info","message":"<PERSON><PERSON>ate<PERSON> subscribed to the \"translation:history\" message","timestamp":"2025-08-03T18:09:27.862Z"}
{"context":"WebSocketsController","level":"info","message":"<PERSON><PERSON>ate<PERSON> subscribed to the \"translation:favorite\" message","timestamp":"2025-08-03T18:09:27.862Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-03T18:09:27.864Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-03T18:09:27.866Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-03T18:09:27.867Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/detailed, GET} route","timestamp":"2025-08-03T18:09:27.867Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/readiness, GET} route","timestamp":"2025-08-03T18:09:27.868Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/liveness, GET} route","timestamp":"2025-08-03T18:09:27.868Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-03T18:09:27.869Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-03T18:09:27.870Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-03T18:09:27.870Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-03T18:09:27.871Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/logout, POST} route","timestamp":"2025-08-03T18:09:27.872Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/me, POST} route","timestamp":"2025-08-03T18:09:27.872Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translation}:","timestamp":"2025-08-03T18:09:27.873Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/translate, POST} route","timestamp":"2025-08-03T18:09:27.874Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/batch, POST} route","timestamp":"2025-08-03T18:09:27.874Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/history, GET} route","timestamp":"2025-08-03T18:09:27.875Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/favorites, GET} route","timestamp":"2025-08-03T18:09:27.875Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/:id/favorite, POST} route","timestamp":"2025-08-03T18:09:27.876Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/languages, GET} route","timestamp":"2025-08-03T18:09:27.876Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscription}:","timestamp":"2025-08-03T18:09:27.876Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/plans, GET} route","timestamp":"2025-08-03T18:09:27.877Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/current, GET} route","timestamp":"2025-08-03T18:09:27.878Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/subscribe, POST} route","timestamp":"2025-08-03T18:09:27.878Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/activate/:subscriptionId, PUT} route","timestamp":"2025-08-03T18:09:27.879Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/cancel, DELETE} route","timestamp":"2025-08-03T18:09:27.879Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/renew, POST} route","timestamp":"2025-08-03T18:09:27.880Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/history, GET} route","timestamp":"2025-08-03T18:09:27.880Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/usage, GET} route","timestamp":"2025-08-03T18:09:27.880Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/usage/history, GET} route","timestamp":"2025-08-03T18:09:27.880Z"}
{"context":"RoutesResolver","level":"info","message":"PaymentController {/api/v1/payment}:","timestamp":"2025-08-03T18:09:27.881Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/create, POST} route","timestamp":"2025-08-03T18:09:27.881Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId, GET} route","timestamp":"2025-08-03T18:09:27.881Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/status, GET} route","timestamp":"2025-08-03T18:09:27.882Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment, GET} route","timestamp":"2025-08-03T18:09:27.882Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/callback, POST} route","timestamp":"2025-08-03T18:09:27.882Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/refund, POST} route","timestamp":"2025-08-03T18:09:27.882Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupon}:","timestamp":"2025-08-03T18:09:27.883Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/validate, POST} route","timestamp":"2025-08-03T18:09:27.883Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/:code, GET} route","timestamp":"2025-08-03T18:09:27.883Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon, GET} route","timestamp":"2025-08-03T18:09:27.884Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/:couponId/stats, GET} route","timestamp":"2025-08-03T18:09:27.884Z"}
{"context":"RoutesResolver","level":"info","message":"BillingController {/api/v1/billing}:","timestamp":"2025-08-03T18:09:27.884Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/summary, GET} route","timestamp":"2025-08-03T18:09:27.884Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/user, GET} route","timestamp":"2025-08-03T18:09:27.885Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/user/:userId, GET} route","timestamp":"2025-08-03T18:09:27.885Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/invoice/:paymentId, GET} route","timestamp":"2025-08-03T18:09:27.885Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/revenue, GET} route","timestamp":"2025-08-03T18:09:27.885Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/customers/top, GET} route","timestamp":"2025-08-03T18:09:27.886Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/payment-methods, GET} route","timestamp":"2025-08-03T18:09:27.886Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/export, GET} route","timestamp":"2025-08-03T18:09:27.886Z"}
{"context":"RoutesResolver","level":"info","message":"AdminController {/api/v1/admin}:","timestamp":"2025-08-03T18:09:27.887Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/dashboard, GET} route","timestamp":"2025-08-03T18:09:27.887Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users, GET} route","timestamp":"2025-08-03T18:09:27.887Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id, PUT} route","timestamp":"2025-08-03T18:09:27.887Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id, DELETE} route","timestamp":"2025-08-03T18:09:27.888Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id/toggle-status, PUT} route","timestamp":"2025-08-03T18:09:27.888Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/translations, GET} route","timestamp":"2025-08-03T18:09:27.888Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/coupons, GET} route","timestamp":"2025-08-03T18:09:27.888Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/coupons, POST} route","timestamp":"2025-08-03T18:09:27.888Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-03T18:09:27.893Z"}
{"level":"info","message":"Application is running on: http://localhost:3000/api/v1","timestamp":"2025-08-03T18:09:27.898Z"}
{"level":"info","message":"Swagger documentation is available at: http://localhost:3000/api-docs","timestamp":"2025-08-03T18:09:27.898Z"}
{"context":"EmailService","level":"info","message":"Email transporter is ready to send messages","timestamp":"2025-08-03T18:09:28.642Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:start\" message","timestamp":"2025-08-03T18:22:50.046Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:batch\" message","timestamp":"2025-08-03T18:22:50.047Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:history\" message","timestamp":"2025-08-03T18:22:50.047Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:favorite\" message","timestamp":"2025-08-03T18:22:50.048Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-03T18:22:50.049Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-03T18:22:50.053Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-03T18:22:50.055Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/detailed, GET} route","timestamp":"2025-08-03T18:22:50.056Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/readiness, GET} route","timestamp":"2025-08-03T18:22:50.058Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/liveness, GET} route","timestamp":"2025-08-03T18:22:50.059Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-03T18:22:50.059Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-03T18:22:50.060Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-03T18:22:50.061Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-03T18:22:50.062Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/logout, POST} route","timestamp":"2025-08-03T18:22:50.063Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/me, POST} route","timestamp":"2025-08-03T18:22:50.064Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translation}:","timestamp":"2025-08-03T18:22:50.064Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/translate, POST} route","timestamp":"2025-08-03T18:22:50.065Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/batch, POST} route","timestamp":"2025-08-03T18:22:50.065Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/history, GET} route","timestamp":"2025-08-03T18:22:50.066Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/favorites, GET} route","timestamp":"2025-08-03T18:22:50.066Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/:id/favorite, POST} route","timestamp":"2025-08-03T18:22:50.067Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/languages, GET} route","timestamp":"2025-08-03T18:22:50.068Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscription}:","timestamp":"2025-08-03T18:22:50.068Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/plans, GET} route","timestamp":"2025-08-03T18:22:50.069Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/current, GET} route","timestamp":"2025-08-03T18:22:50.070Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/subscribe, POST} route","timestamp":"2025-08-03T18:22:50.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/activate/:subscriptionId, PUT} route","timestamp":"2025-08-03T18:22:50.071Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/cancel, DELETE} route","timestamp":"2025-08-03T18:22:50.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/renew, POST} route","timestamp":"2025-08-03T18:22:50.072Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/history, GET} route","timestamp":"2025-08-03T18:22:50.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/usage, GET} route","timestamp":"2025-08-03T18:22:50.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/usage/history, GET} route","timestamp":"2025-08-03T18:22:50.073Z"}
{"context":"RoutesResolver","level":"info","message":"PaymentController {/api/v1/payment}:","timestamp":"2025-08-03T18:22:50.073Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/create, POST} route","timestamp":"2025-08-03T18:22:50.074Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId, GET} route","timestamp":"2025-08-03T18:22:50.074Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/status, GET} route","timestamp":"2025-08-03T18:22:50.074Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment, GET} route","timestamp":"2025-08-03T18:22:50.075Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/callback, POST} route","timestamp":"2025-08-03T18:22:50.075Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/refund, POST} route","timestamp":"2025-08-03T18:22:50.075Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupon}:","timestamp":"2025-08-03T18:22:50.076Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/validate, POST} route","timestamp":"2025-08-03T18:22:50.077Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/:code, GET} route","timestamp":"2025-08-03T18:22:50.078Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon, GET} route","timestamp":"2025-08-03T18:22:50.079Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/:couponId/stats, GET} route","timestamp":"2025-08-03T18:22:50.080Z"}
{"context":"RoutesResolver","level":"info","message":"BillingController {/api/v1/billing}:","timestamp":"2025-08-03T18:22:50.080Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/summary, GET} route","timestamp":"2025-08-03T18:22:50.081Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/user, GET} route","timestamp":"2025-08-03T18:22:50.081Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/user/:userId, GET} route","timestamp":"2025-08-03T18:22:50.081Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/invoice/:paymentId, GET} route","timestamp":"2025-08-03T18:22:50.082Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/revenue, GET} route","timestamp":"2025-08-03T18:22:50.082Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/customers/top, GET} route","timestamp":"2025-08-03T18:22:50.083Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/payment-methods, GET} route","timestamp":"2025-08-03T18:22:50.083Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/export, GET} route","timestamp":"2025-08-03T18:22:50.083Z"}
{"context":"RoutesResolver","level":"info","message":"AdminController {/api/v1/admin}:","timestamp":"2025-08-03T18:22:50.084Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/dashboard, GET} route","timestamp":"2025-08-03T18:22:50.084Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users, GET} route","timestamp":"2025-08-03T18:22:50.084Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id, PUT} route","timestamp":"2025-08-03T18:22:50.084Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id, DELETE} route","timestamp":"2025-08-03T18:22:50.085Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id/toggle-status, PUT} route","timestamp":"2025-08-03T18:22:50.085Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/translations, GET} route","timestamp":"2025-08-03T18:22:50.086Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/coupons, GET} route","timestamp":"2025-08-03T18:22:50.086Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/coupons, POST} route","timestamp":"2025-08-03T18:22:50.086Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-03T18:22:50.092Z"}
{"level":"info","message":"Application is running on: http://localhost:3000/api/v1","timestamp":"2025-08-03T18:22:50.096Z"}
{"level":"info","message":"Swagger documentation is available at: http://localhost:3000/api-docs","timestamp":"2025-08-03T18:22:50.097Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:start\" message","timestamp":"2025-08-03T20:00:28.338Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:batch\" message","timestamp":"2025-08-03T20:00:28.338Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:history\" message","timestamp":"2025-08-03T20:00:28.339Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:favorite\" message","timestamp":"2025-08-03T20:00:28.339Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-03T20:00:28.340Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-03T20:00:28.342Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-03T20:00:28.342Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/detailed, GET} route","timestamp":"2025-08-03T20:00:28.343Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/readiness, GET} route","timestamp":"2025-08-03T20:00:28.343Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/liveness, GET} route","timestamp":"2025-08-03T20:00:28.344Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-03T20:00:28.344Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-03T20:00:28.344Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-03T20:00:28.345Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-03T20:00:28.345Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/logout, POST} route","timestamp":"2025-08-03T20:00:28.346Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/me, POST} route","timestamp":"2025-08-03T20:00:28.346Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translation}:","timestamp":"2025-08-03T20:00:28.346Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/translate, POST} route","timestamp":"2025-08-03T20:00:28.347Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/batch, POST} route","timestamp":"2025-08-03T20:00:28.348Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/history, GET} route","timestamp":"2025-08-03T20:00:28.348Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/favorites, GET} route","timestamp":"2025-08-03T20:00:28.349Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/:id/favorite, POST} route","timestamp":"2025-08-03T20:00:28.349Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/languages, GET} route","timestamp":"2025-08-03T20:00:28.350Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscription}:","timestamp":"2025-08-03T20:00:28.350Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/plans, GET} route","timestamp":"2025-08-03T20:00:28.350Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/current, GET} route","timestamp":"2025-08-03T20:00:28.351Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/subscribe, POST} route","timestamp":"2025-08-03T20:00:28.351Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/activate/:subscriptionId, PUT} route","timestamp":"2025-08-03T20:00:28.352Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/cancel, DELETE} route","timestamp":"2025-08-03T20:00:28.352Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/renew, POST} route","timestamp":"2025-08-03T20:00:28.352Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/history, GET} route","timestamp":"2025-08-03T20:00:28.353Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/usage, GET} route","timestamp":"2025-08-03T20:00:28.353Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/usage/history, GET} route","timestamp":"2025-08-03T20:00:28.354Z"}
{"context":"RoutesResolver","level":"info","message":"PaymentController {/api/v1/payment}:","timestamp":"2025-08-03T20:00:28.354Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/create, POST} route","timestamp":"2025-08-03T20:00:28.354Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId, GET} route","timestamp":"2025-08-03T20:00:28.354Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/status, GET} route","timestamp":"2025-08-03T20:00:28.355Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment, GET} route","timestamp":"2025-08-03T20:00:28.355Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/callback, POST} route","timestamp":"2025-08-03T20:00:28.356Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/refund, POST} route","timestamp":"2025-08-03T20:00:28.356Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupon}:","timestamp":"2025-08-03T20:00:28.356Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/validate, POST} route","timestamp":"2025-08-03T20:00:28.357Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/:code, GET} route","timestamp":"2025-08-03T20:00:28.357Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon, GET} route","timestamp":"2025-08-03T20:00:28.357Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/:couponId/stats, GET} route","timestamp":"2025-08-03T20:00:28.358Z"}
{"context":"RoutesResolver","level":"info","message":"BillingController {/api/v1/billing}:","timestamp":"2025-08-03T20:00:28.358Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/summary, GET} route","timestamp":"2025-08-03T20:00:28.358Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/user, GET} route","timestamp":"2025-08-03T20:00:28.358Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/user/:userId, GET} route","timestamp":"2025-08-03T20:00:28.359Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/invoice/:paymentId, GET} route","timestamp":"2025-08-03T20:00:28.359Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/revenue, GET} route","timestamp":"2025-08-03T20:00:28.359Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/customers/top, GET} route","timestamp":"2025-08-03T20:00:28.360Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/payment-methods, GET} route","timestamp":"2025-08-03T20:00:28.360Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/export, GET} route","timestamp":"2025-08-03T20:00:28.360Z"}
{"context":"RoutesResolver","level":"info","message":"AdminController {/api/v1/admin}:","timestamp":"2025-08-03T20:00:28.360Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/dashboard, GET} route","timestamp":"2025-08-03T20:00:28.360Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users, GET} route","timestamp":"2025-08-03T20:00:28.361Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id, PUT} route","timestamp":"2025-08-03T20:00:28.361Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id, DELETE} route","timestamp":"2025-08-03T20:00:28.361Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id/toggle-status, PUT} route","timestamp":"2025-08-03T20:00:28.362Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/translations, GET} route","timestamp":"2025-08-03T20:00:28.362Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/coupons, GET} route","timestamp":"2025-08-03T20:00:28.362Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/coupons, POST} route","timestamp":"2025-08-03T20:00:28.362Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-03T20:00:28.365Z"}
{"level":"info","message":"Application is running on: http://localhost:3000/api/v1","timestamp":"2025-08-03T20:00:28.368Z"}
{"level":"info","message":"Swagger documentation is available at: http://localhost:3000/api-docs","timestamp":"2025-08-03T20:00:28.368Z"}
{"context":"EmailService","level":"info","message":"Email transporter is ready to send messages","timestamp":"2025-08-03T20:00:28.696Z"}
{"context":"ExceptionFilter","level":"error","message":"GET /health","stack":["NotFoundException: Cannot GET /health\n    at callback (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\@nestjs\\core\\router\\routes-resolver.js:77:19)\n    at E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\@nestjs\\core\\router\\router-proxy.js:9:23\n    at Layer.handleRequest (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:342:13)\n    at E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:297:9\n    at processParams (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:582:12)\n    at next (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:342:13)"],"timestamp":"2025-08-03T20:00:55.517Z"}
{"context":"EmailService","level":"info","message":"=== 验证码发送 ===","timestamp":"2025-08-03T20:01:54.404Z"}
{"context":"EmailService","level":"info","message":"收件人: <EMAIL>","timestamp":"2025-08-03T20:01:54.404Z"}
{"context":"EmailService","level":"info","message":"验证码: 568607","timestamp":"2025-08-03T20:01:54.405Z"}
{"context":"EmailService","level":"info","message":"有效期: 5分钟","timestamp":"2025-08-03T20:01:54.405Z"}
{"context":"EmailService","level":"info","message":"==================","timestamp":"2025-08-03T20:01:54.405Z"}
{"context":"EmailService","level":"info","message":"✅ 验证码邮件已成功发送到 <EMAIL>","timestamp":"2025-08-03T20:01:55.159Z"}
{"context":"VerificationCodeService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-03T20:01:55.170Z"}
{"context":"VerificationCodeService","level":"info","message":"Rate limit <NAME_EMAIL>","timestamp":"2025-08-03T20:01:55.170Z"}
{"context":"AuthService","level":"info","message":"Verification code <NAME_EMAIL>","timestamp":"2025-08-03T20:01:55.170Z"}
{"context":"ExceptionFilter","level":"error","message":"GET /favicon.ico","stack":["NotFoundException: Cannot GET /favicon.ico\n    at callback (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\@nestjs\\core\\router\\routes-resolver.js:77:19)\n    at E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\@nestjs\\core\\router\\router-proxy.js:9:23\n    at Layer.handleRequest (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:342:13)\n    at E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:297:9\n    at processParams (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:582:12)\n    at next (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:291:5)\n    at urlencodedParser (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\body-parser\\lib\\types\\urlencoded.js:68:7)\n    at Layer.handleRequest (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\lib\\layer.js:152:17)\n    at trimPrefix (E:\\Syn-All\\backup-cc1\\syntext-backend\\node_modules\\router\\index.js:342:13)"],"timestamp":"2025-08-03T20:02:26.203Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:start\" message","timestamp":"2025-08-03T20:19:23.857Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:batch\" message","timestamp":"2025-08-03T20:19:23.858Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:history\" message","timestamp":"2025-08-03T20:19:23.859Z"}
{"context":"WebSocketsController","level":"info","message":"TranslationGateway subscribed to the \"translation:favorite\" message","timestamp":"2025-08-03T20:19:23.859Z"}
{"context":"RoutesResolver","level":"info","message":"AppController {/api/v1}:","timestamp":"2025-08-03T20:19:23.861Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1, GET} route","timestamp":"2025-08-03T20:19:23.864Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health, GET} route","timestamp":"2025-08-03T20:19:23.865Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/detailed, GET} route","timestamp":"2025-08-03T20:19:23.865Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/readiness, GET} route","timestamp":"2025-08-03T20:19:23.866Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/health/liveness, GET} route","timestamp":"2025-08-03T20:19:23.867Z"}
{"context":"RoutesResolver","level":"info","message":"AuthController {/api/v1/auth}:","timestamp":"2025-08-03T20:19:23.868Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/send-code, POST} route","timestamp":"2025-08-03T20:19:23.870Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/verify-code, POST} route","timestamp":"2025-08-03T20:19:23.870Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/refresh, POST} route","timestamp":"2025-08-03T20:19:23.871Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/logout, POST} route","timestamp":"2025-08-03T20:19:23.872Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/auth/me, POST} route","timestamp":"2025-08-03T20:19:23.872Z"}
{"context":"RoutesResolver","level":"info","message":"TranslationController {/api/v1/translation}:","timestamp":"2025-08-03T20:19:23.873Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/translate, POST} route","timestamp":"2025-08-03T20:19:23.873Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/batch, POST} route","timestamp":"2025-08-03T20:19:23.881Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/history, GET} route","timestamp":"2025-08-03T20:19:23.882Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/favorites, GET} route","timestamp":"2025-08-03T20:19:23.882Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/:id/favorite, POST} route","timestamp":"2025-08-03T20:19:23.883Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/translation/languages, GET} route","timestamp":"2025-08-03T20:19:23.883Z"}
{"context":"RoutesResolver","level":"info","message":"SubscriptionController {/api/v1/subscription}:","timestamp":"2025-08-03T20:19:23.884Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/plans, GET} route","timestamp":"2025-08-03T20:19:23.884Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/current, GET} route","timestamp":"2025-08-03T20:19:23.885Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/subscribe, POST} route","timestamp":"2025-08-03T20:19:23.885Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/activate/:subscriptionId, PUT} route","timestamp":"2025-08-03T20:19:23.886Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/cancel, DELETE} route","timestamp":"2025-08-03T20:19:23.887Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/renew, POST} route","timestamp":"2025-08-03T20:19:23.887Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/history, GET} route","timestamp":"2025-08-03T20:19:23.887Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/usage, GET} route","timestamp":"2025-08-03T20:19:23.888Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/subscription/usage/history, GET} route","timestamp":"2025-08-03T20:19:23.888Z"}
{"context":"RoutesResolver","level":"info","message":"PaymentController {/api/v1/payment}:","timestamp":"2025-08-03T20:19:23.888Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/create, POST} route","timestamp":"2025-08-03T20:19:23.889Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId, GET} route","timestamp":"2025-08-03T20:19:23.889Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/status, GET} route","timestamp":"2025-08-03T20:19:23.890Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment, GET} route","timestamp":"2025-08-03T20:19:23.890Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/callback, POST} route","timestamp":"2025-08-03T20:19:23.891Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/payment/:paymentId/refund, POST} route","timestamp":"2025-08-03T20:19:23.892Z"}
{"context":"RoutesResolver","level":"info","message":"CouponController {/api/v1/coupon}:","timestamp":"2025-08-03T20:19:23.892Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/validate, POST} route","timestamp":"2025-08-03T20:19:23.893Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/:code, GET} route","timestamp":"2025-08-03T20:19:23.893Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon, GET} route","timestamp":"2025-08-03T20:19:23.894Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/coupon/:couponId/stats, GET} route","timestamp":"2025-08-03T20:19:23.895Z"}
{"context":"RoutesResolver","level":"info","message":"BillingController {/api/v1/billing}:","timestamp":"2025-08-03T20:19:23.895Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/summary, GET} route","timestamp":"2025-08-03T20:19:23.896Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/user, GET} route","timestamp":"2025-08-03T20:19:23.897Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/user/:userId, GET} route","timestamp":"2025-08-03T20:19:23.897Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/invoice/:paymentId, GET} route","timestamp":"2025-08-03T20:19:23.898Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/revenue, GET} route","timestamp":"2025-08-03T20:19:23.898Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/customers/top, GET} route","timestamp":"2025-08-03T20:19:23.899Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/analytics/payment-methods, GET} route","timestamp":"2025-08-03T20:19:23.899Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/billing/export, GET} route","timestamp":"2025-08-03T20:19:23.899Z"}
{"context":"RoutesResolver","level":"info","message":"AdminController {/api/v1/admin}:","timestamp":"2025-08-03T20:19:23.900Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/dashboard, GET} route","timestamp":"2025-08-03T20:19:23.900Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users, GET} route","timestamp":"2025-08-03T20:19:23.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id, PUT} route","timestamp":"2025-08-03T20:19:23.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id, DELETE} route","timestamp":"2025-08-03T20:19:23.901Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/users/:id/toggle-status, PUT} route","timestamp":"2025-08-03T20:19:23.902Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/translations, GET} route","timestamp":"2025-08-03T20:19:23.903Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/coupons, GET} route","timestamp":"2025-08-03T20:19:23.903Z"}
{"context":"RouterExplorer","level":"info","message":"Mapped {/api/v1/admin/coupons, POST} route","timestamp":"2025-08-03T20:19:23.903Z"}
{"context":"NestApplication","level":"info","message":"Nest application successfully started","timestamp":"2025-08-03T20:19:23.906Z"}
{"level":"info","message":"Application is running on: http://localhost:3000/api/v1","timestamp":"2025-08-03T20:19:23.912Z"}
{"level":"info","message":"Swagger documentation is available at: http://localhost:3000/api-docs","timestamp":"2025-08-03T20:19:23.913Z"}
{"context":"EmailService","level":"info","message":"Email transporter is ready to send messages","timestamp":"2025-08-03T20:19:24.161Z"}
