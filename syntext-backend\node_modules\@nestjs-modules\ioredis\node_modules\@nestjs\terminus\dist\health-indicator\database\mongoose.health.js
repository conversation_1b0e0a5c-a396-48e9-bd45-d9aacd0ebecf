"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MongooseHealthIndicator = void 0;
const common_1 = require("@nestjs/common");
const core_1 = require("@nestjs/core");
const __1 = require("../..");
const health_check_error_1 = require("../../health-check/health-check.error");
const utils_1 = require("../../utils");
const health_indicator_1 = require("../health-indicator");
/**
 * The MongooseHealthIndicator contains health indicators
 * which are used for health checks related to Mongoose
 *
 * @publicApi
 * @module TerminusModule
 */
let MongooseHealthIndicator = class MongooseHealthIndicator extends health_indicator_1.HealthIndicator {
    /**
     * Initializes the MongooseHealthIndicator
     *
     * @param {ModuleRef} moduleRef The NestJS module reference
     */
    constructor(moduleRef) {
        super();
        this.moduleRef = moduleRef;
        this.checkDependantPackages();
    }
    /**
     * Checks if the dependant packages are present
     */
    checkDependantPackages() {
        (0, utils_1.checkPackages)(['@nestjs/mongoose', 'mongoose'], this.constructor.name);
    }
    /**
     * Returns the connection of the current DI context
     */
    getContextConnection() {
        const { getConnectionToken } = 
        // eslint-disable-next-line @typescript-eslint/no-var-requires
        require('@nestjs/mongoose');
        try {
            return this.moduleRef.get(getConnectionToken('DatabaseConnection'), {
                strict: false,
            });
        }
        catch (err) {
            return null;
        }
    }
    /**
     * Pings a mongoose connection
     * @param connection The connection which the ping should get executed
     * @param timeout The timeout how long the ping should maximum take
     *
     */
    pingDb(connection, timeout) {
        return __awaiter(this, void 0, void 0, function* () {
            const promise = connection.readyState === 1 ? Promise.resolve() : Promise.reject();
            return yield (0, utils_1.promiseTimeout)(timeout, promise);
        });
    }
    /**
     * Checks if the MongoDB responds in (default) 1000ms and
     * returns a result object corresponding to the result
     *
     * @param key The key which will be used for the result object
     * @param options The options for the ping
     * @example
     * mongooseHealthIndicator.pingCheck('mongodb', { timeout: 1500 });
     */
    pingCheck(key, options = {}) {
        return __awaiter(this, void 0, void 0, function* () {
            let isHealthy = false;
            this.checkDependantPackages();
            const connection = options.connection || this.getContextConnection();
            const timeout = options.timeout || 1000;
            if (!connection) {
                throw new __1.ConnectionNotFoundError(this.getStatus(key, isHealthy, {
                    message: 'Connection provider not found in application context',
                }));
            }
            try {
                yield this.pingDb(connection, timeout);
                isHealthy = true;
            }
            catch (err) {
                if (err instanceof utils_1.TimeoutError) {
                    throw new __1.TimeoutError(timeout, this.getStatus(key, isHealthy, {
                        message: `timeout of ${timeout}ms exceeded`,
                    }));
                }
            }
            if (isHealthy) {
                return this.getStatus(key, isHealthy);
            }
            else {
                throw new health_check_error_1.HealthCheckError(`${key} is not available`, this.getStatus(key, isHealthy));
            }
        });
    }
};
exports.MongooseHealthIndicator = MongooseHealthIndicator;
exports.MongooseHealthIndicator = MongooseHealthIndicator = __decorate([
    (0, common_1.Injectable)({ scope: common_1.Scope.TRANSIENT }),
    __metadata("design:paramtypes", [core_1.ModuleRef])
], MongooseHealthIndicator);
//# sourceMappingURL=mongoose.health.js.map