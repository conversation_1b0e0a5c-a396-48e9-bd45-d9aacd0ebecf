version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: syntext-postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: syntext
    ports:
      - "6432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - syntext-network

  redis:
    image: redis:7-alpine
    container_name: syntext-redis
    ports:
      - "6379:6379"
    networks:
      - syntext-network

  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: syntext-backend
    ports:
      - "6000:3000"
    env_file:
      - .env
    depends_on:
      - postgres
      - redis
    networks:
      - syntext-network
    volumes:
      - ./logs:/app/logs

volumes:
  postgres_data:

networks:
  syntext-network:
    driver: bridge