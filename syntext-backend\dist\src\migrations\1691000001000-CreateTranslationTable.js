"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTranslationTable1691000001000 = void 0;
const typeorm_1 = require("typeorm");
class CreateTranslationTable1691000001000 {
    name = 'CreateTranslationTable1691000001000';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TYPE "translation_status" AS ENUM('pending', 'processing', 'completed', 'failed')
    `);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'translations',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'userId',
                    type: 'uuid',
                    isNullable: false,
                },
                {
                    name: 'sourceText',
                    type: 'text',
                    isNullable: false,
                },
                {
                    name: 'translatedText',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'sourceLanguage',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                },
                {
                    name: 'targetLanguage',
                    type: 'varchar',
                    length: '10',
                    isNullable: false,
                },
                {
                    name: 'status',
                    type: 'enum',
                    enum: ['pending', 'processing', 'completed', 'failed'],
                    default: "'pending'",
                },
                {
                    name: 'characterCount',
                    type: 'int',
                    default: 0,
                },
                {
                    name: 'processingTime',
                    type: 'int',
                    isNullable: true,
                    comment: 'Processing time in milliseconds',
                },
                {
                    name: 'errorMessage',
                    type: 'text',
                    isNullable: true,
                },
                {
                    name: 'metadata',
                    type: 'jsonb',
                    default: "'{}'::jsonb",
                },
                {
                    name: 'createdAt',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createForeignKey('translations', new typeorm_1.TableForeignKey({
            columnNames: ['userId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'CASCADE',
        }));
        await queryRunner.createIndex('translations', new typeorm_1.TableIndex({ name: 'IDX_translations_user_id', columnNames: ['userId'] }));
        await queryRunner.createIndex('translations', new typeorm_1.TableIndex({ name: 'IDX_translations_status', columnNames: ['status'] }));
        await queryRunner.createIndex('translations', new typeorm_1.TableIndex({ name: 'IDX_translations_created_at', columnNames: ['createdAt'] }));
        await queryRunner.createIndex('translations', new typeorm_1.TableIndex({ name: 'IDX_translations_language_pair', columnNames: ['sourceLanguage', 'targetLanguage'] }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('translations');
        await queryRunner.query(`DROP TYPE "translation_status"`);
    }
}
exports.CreateTranslationTable1691000001000 = CreateTranslationTable1691000001000;
//# sourceMappingURL=1691000001000-CreateTranslationTable.js.map