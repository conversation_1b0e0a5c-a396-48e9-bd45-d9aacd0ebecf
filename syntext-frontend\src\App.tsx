import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom';
import { Suspense, useEffect } from 'react';
import { useAppSelector } from './hooks';
import ErrorBoundary from './components/ErrorBoundary';
import { setupGlobalErrorHandling } from './utils/errorHandling';

// 直接导入页面组件，简化懒加载
import LoginPage from './pages/LoginPage';
import HomePage from './pages/HomePage';
import TranslatePage from './pages/TranslatePage';
import HistoryPage from './pages/HistoryPage';
import ProfilePage from './pages/ProfilePage';
import SettingsPage from './pages/SettingsPage';
import Subscription from './pages/Subscription';
import HelpPage from './pages/HelpPage';
import AdminDashboard from './pages/AdminDashboard';
import AdminUsers from './pages/AdminUsers';
import AdminTranslations from './pages/AdminTranslations';
import AdminCoupons from './pages/AdminCoupons';
import NotFoundPage from './pages/NotFoundPage';

// 简化的加载指示器
const LoadingFallback = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p className="text-gray-600">加载中...</p>
    </div>
  </div>
);

function App() {
  const { isAuthenticated } = useAppSelector((state) => state.auth);

  // 设置全局错误处理
  useEffect(() => {
    setupGlobalErrorHandling();
  }, []);

  return (
    <ErrorBoundary>
      <BrowserRouter>
        <Suspense fallback={<LoadingFallback />}>
          <Routes>
            <Route path="/login" element={<LoginPage />} />
            <Route
              path="/"
              element={
                isAuthenticated ? <HomePage /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/translate"
              element={
                isAuthenticated ? <TranslatePage /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/history"
              element={
                isAuthenticated ? <HistoryPage /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/profile"
              element={
                isAuthenticated ? <ProfilePage /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/settings"
              element={
                isAuthenticated ? <SettingsPage /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/subscription"
              element={
                isAuthenticated ? <Subscription /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/help"
              element={<HelpPage />}
            />

            {/* Admin Routes */}
            <Route
              path="/admin"
              element={
                isAuthenticated ? <AdminDashboard /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/admin/users"
              element={
                isAuthenticated ? <AdminUsers /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/admin/translations"
              element={
                isAuthenticated ? <AdminTranslations /> : <Navigate to="/login" replace />
              }
            />
            <Route
              path="/admin/coupons"
              element={
                isAuthenticated ? <AdminCoupons /> : <Navigate to="/login" replace />
              }
            />

            <Route path="*" element={<NotFoundPage />} />
          </Routes>
        </Suspense>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;
