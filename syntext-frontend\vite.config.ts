import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    // 代码分割优化
    rollupOptions: {
      output: {
        manualChunks: {
          // 将React相关库分离
          'react-vendor': ['react', 'react-dom'],
          // 将Redux相关库分离
          'redux-vendor': ['@reduxjs/toolkit', 'react-redux'],
          // 将路由库分离
          'router-vendor': ['react-router-dom'],
          // 将工具库分离
          'utils-vendor': ['axios'],
        },
      },
    },
    // 启用源码映射以便调试
    sourcemap: true,
    // 优化构建性能
    target: 'esnext',
    // 压缩配置
    minify: 'terser',
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000,
  },
  // 开发服务器优化
  server: {
    port: 6173,
    // 启用HMR
    hmr: true,
  },
  // 依赖预构建优化
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@reduxjs/toolkit',
      'react-redux',
      'react-router-dom',
      'axios',
    ],
  },
  // 静态资源处理
  assetsInclude: ['**/*.svg', '**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif'],
  // CSS处理优化
  css: {
    // PostCSS配置
    postcss: {},
    // CSS模块
    modules: {
      localsConvention: 'camelCaseOnly',
    },
  },
})
