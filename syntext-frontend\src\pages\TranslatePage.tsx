import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks';
import {
  fetchLanguages,
  translateText,
  setSourceText,
  setSourceLanguage,
  setTargetLanguage,
  clearTranslationError
} from '../store/slices/translationSlice';
import { websocketService } from '../services/websocket';


const TranslatePage = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();

  const {
    currentTranslation,
    languages,
    websocket
  } = useAppSelector((state) => state.translation);

  const [useWebSocket, setUseWebSocket] = useState(true);
  const [showProgress, setShowProgress] = useState(false);

  useEffect(() => {
    // 获取支持的语言列表
    dispatch(fetchLanguages());

    // 连接WebSocket
    if (useWebSocket && !websocket.connected && !websocket.connecting) {
      websocketService.connect();
    }

    // 清除错误状态
    return () => {
      dispatch(clearTranslationError());
    };
  }, [dispatch, useWebSocket, websocket.connected, websocket.connecting]);

  const handleTranslate = async () => {
    if (!currentTranslation.sourceText.trim()) return;

    const translationData = {
      sourceText: currentTranslation.sourceText,
      sourceLanguage: currentTranslation.sourceLanguage,
      targetLanguage: currentTranslation.targetLanguage,
    };

    if (useWebSocket && websocket.connected) {
      // 使用WebSocket进行实时翻译
      setShowProgress(true);
      websocketService.startTranslation(translationData);
    } else {
      // 使用HTTP API进行翻译
      dispatch(translateText(translationData));
    }
  };

  const handleSwapLanguages = () => {
    if (currentTranslation.sourceLanguage === 'auto') return;

    dispatch(setSourceLanguage(currentTranslation.targetLanguage));
    dispatch(setTargetLanguage(currentTranslation.sourceLanguage));
    dispatch(setSourceText(currentTranslation.translatedText));
  };

  const handleClearText = () => {
    dispatch(setSourceText(''));
  };

  const getLanguageOptions = (excludeAuto = false) => {
    const popularLanguages = languages.popular.filter(lang =>
      !excludeAuto || lang.code !== 'auto'
    );
    const otherLanguages = languages.all.filter(lang =>
      !languages.popular.some(popular => popular.code === lang.code) &&
      (!excludeAuto || lang.code !== 'auto')
    );

    return { popular: popularLanguages, others: otherLanguages };
  };

  const { popular: popularLanguages, others: otherLanguages } = getLanguageOptions();
  const { popular: popularTargetLanguages, others: otherTargetLanguages } = getLanguageOptions(true);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <nav className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => navigate('/')}
                className="inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
                返回首页
              </button>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl font-semibold text-gray-900">智能翻译</h1>
            </div>

            {/* WebSocket状态指示器 */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${
                  websocket.connected ? 'bg-green-500' :
                  websocket.connecting ? 'bg-yellow-500' : 'bg-red-500'
                }`}></div>
                <span className="text-sm text-gray-600">
                  {websocket.connected ? '实时连接' :
                   websocket.connecting ? '连接中...' : '离线模式'}
                </span>
              </div>

              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={useWebSocket}
                  onChange={(e) => setUseWebSocket(e.target.checked)}
                  className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                />
                <span className="text-sm text-gray-600">实时翻译</span>
              </label>
            </div>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          {/* Language Selection Bar */}
          <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="flex flex-col">
                  <label className="text-xs font-medium text-gray-500 mb-1">源语言</label>
                  <select
                    value={currentTranslation.sourceLanguage}
                    onChange={(e) => dispatch(setSourceLanguage(e.target.value))}
                    className="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <optgroup label="热门语言">
                      {popularLanguages.map((lang) => (
                        <option key={lang.code} value={lang.code}>
                          {lang.name}
                        </option>
                      ))}
                    </optgroup>
                    {otherLanguages.length > 0 && (
                      <optgroup label="其他语言">
                        {otherLanguages.map((lang) => (
                          <option key={lang.code} value={lang.code}>
                            {lang.name}
                          </option>
                        ))}
                      </optgroup>
                    )}
                  </select>
                </div>

                <button
                  onClick={handleSwapLanguages}
                  disabled={currentTranslation.sourceLanguage === 'auto'}
                  className="p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  title="交换语言"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                  </svg>
                </button>

                <div className="flex flex-col">
                  <label className="text-xs font-medium text-gray-500 mb-1">目标语言</label>
                  <select
                    value={currentTranslation.targetLanguage}
                    onChange={(e) => dispatch(setTargetLanguage(e.target.value))}
                    className="text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <optgroup label="热门语言">
                      {popularTargetLanguages.map((lang) => (
                        <option key={lang.code} value={lang.code}>
                          {lang.name}
                        </option>
                      ))}
                    </optgroup>
                    {otherTargetLanguages.length > 0 && (
                      <optgroup label="其他语言">
                        {otherTargetLanguages.map((lang) => (
                          <option key={lang.code} value={lang.code}>
                            {lang.name}
                          </option>
                        ))}
                      </optgroup>
                    )}
                  </select>
                </div>
              </div>
            </div>
          </div>

          {/* Translation Interface */}
          <div className="grid grid-cols-1 lg:grid-cols-2 divide-y lg:divide-y-0 lg:divide-x divide-gray-200">
            {/* Source Text Panel */}
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">原文</h3>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-500">
                    {currentTranslation.sourceText.length} 字符
                  </span>
                  {currentTranslation.sourceText && (
                    <button
                      onClick={handleClearText}
                      className="text-sm text-gray-400 hover:text-gray-600 transition-colors"
                    >
                      清空
                    </button>
                  )}
                </div>
              </div>

              <textarea
                value={currentTranslation.sourceText}
                onChange={(e) => dispatch(setSourceText(e.target.value))}
                placeholder="请输入要翻译的文本..."
                className="w-full h-80 p-4 text-gray-900 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                maxLength={10000}
              />

              <div className="mt-4 flex items-center justify-between">
                <div className="text-xs text-gray-500">
                  最多支持 10,000 字符
                </div>
                <button
                  onClick={handleTranslate}
                  disabled={!currentTranslation.sourceText.trim() || currentTranslation.isTranslating}
                  className="inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all"
                >
                  {currentTranslation.isTranslating ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      翻译中...
                    </>
                  ) : (
                    <>
                      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                      </svg>
                      开始翻译
                    </>
                  )}
                </button>
              </div>
            </div>

            {/* Translation Result Panel */}
            <div className="p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">译文</h3>
                <div className="flex items-center space-x-2">
                  {currentTranslation.translatedText && (
                    <>
                      <button
                        onClick={() => navigator.clipboard.writeText(currentTranslation.translatedText)}
                        className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
                        title="复制译文"
                      >
                        复制
                      </button>
                      <button
                        className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
                        title="收藏翻译"
                      >
                        收藏
                      </button>
                    </>
                  )}
                </div>
              </div>

              {/* Progress Bar */}
              {(currentTranslation.isTranslating || showProgress) && currentTranslation.progress > 0 && (
                <div className="mb-4">
                  <div className="flex items-center justify-between text-sm text-gray-600 mb-2">
                    <span>翻译进度</span>
                    <span>{currentTranslation.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out"
                      style={{ width: `${currentTranslation.progress}%` }}
                    ></div>
                  </div>
                </div>
              )}

              <textarea
                value={currentTranslation.translatedText}
                readOnly
                placeholder="翻译结果将显示在这里..."
                className="w-full h-80 p-4 text-gray-900 border border-gray-300 rounded-lg resize-none bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />

              {currentTranslation.translatedText && (
                <div className="mt-4 flex items-center justify-between">
                  <div className="text-xs text-gray-500">
                    译文长度: {currentTranslation.translatedText.length} 字符
                  </div>
                  <div className="flex items-center space-x-4">
                    <button
                      onClick={() => dispatch(setSourceText(currentTranslation.translatedText))}
                      className="text-sm text-blue-600 hover:text-blue-800 transition-colors"
                    >
                      继续翻译
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Error Display */}
          {currentTranslation.error && (
            <div className="mx-6 mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">翻译失败</h3>
                  <p className="text-sm text-red-700 mt-1">{currentTranslation.error}</p>
                </div>
                <div className="ml-auto pl-3">
                  <button
                    onClick={() => dispatch(clearTranslationError())}
                    className="text-red-400 hover:text-red-600"
                  >
                    <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default TranslatePage;