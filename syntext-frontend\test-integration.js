#!/usr/bin/env node

/**
 * SynText Frontend Integration Test
 * 
 * This script tests the integration between frontend and backend services
 * to ensure the production-ready system is working correctly.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const BACKEND_URL = process.env.VITE_API_BASE_URL || 'http://localhost:3002';
const FRONTEND_URL = 'http://localhost:6173';

// Test results
const testResults = {
  passed: 0,
  failed: 0,
  tests: []
};

// Utility functions
function log(message, type = 'info') {
  const timestamp = new Date().toISOString();
  const colors = {
    info: '\x1b[36m',
    success: '\x1b[32m',
    error: '\x1b[31m',
    warning: '\x1b[33m',
    reset: '\x1b[0m'
  };
  
  console.log(`${colors[type]}[${timestamp}] ${message}${colors.reset}`);
}

function recordTest(name, passed, error = null) {
  testResults.tests.push({ name, passed, error });
  if (passed) {
    testResults.passed++;
    log(`✅ ${name}`, 'success');
  } else {
    testResults.failed++;
    log(`❌ ${name}: ${error}`, 'error');
  }
}

// Test functions
async function testBackendHealth() {
  try {
    const response = await axios.get(`${BACKEND_URL}/health`, { timeout: 5000 });
    recordTest('Backend Health Check', response.status === 200);
    return true;
  } catch (error) {
    recordTest('Backend Health Check', false, error.message);
    return false;
  }
}

async function testDatabaseConnection() {
  try {
    const response = await axios.get(`${BACKEND_URL}/health/db`, { timeout: 10000 });
    recordTest('Database Connection', response.status === 200);
    return true;
  } catch (error) {
    recordTest('Database Connection', false, error.message);
    return false;
  }
}

async function testRedisConnection() {
  try {
    const response = await axios.get(`${BACKEND_URL}/health/redis`, { timeout: 5000 });
    recordTest('Redis Connection', response.status === 200);
    return true;
  } catch (error) {
    recordTest('Redis Connection', false, error.message);
    return false;
  }
}

async function testAuthEndpoints() {
  try {
    // Test send verification code endpoint
    const response = await axios.post(`${BACKEND_URL}/auth/send-code`, {
      email: '<EMAIL>'
    }, { timeout: 5000 });
    
    recordTest('Auth Endpoints', response.status === 200 || response.status === 201);
    return true;
  } catch (error) {
    // 400 is expected for invalid email, so we consider it a pass
    if (error.response && error.response.status === 400) {
      recordTest('Auth Endpoints', true);
      return true;
    }
    recordTest('Auth Endpoints', false, error.message);
    return false;
  }
}

async function testTranslationEndpoints() {
  try {
    // This will fail without auth, but we're testing if the endpoint exists
    const response = await axios.get(`${BACKEND_URL}/translation/languages`, { timeout: 5000 });
    recordTest('Translation Endpoints', response.status === 200);
    return true;
  } catch (error) {
    // 401 is expected without auth, so we consider it a pass if the endpoint exists
    if (error.response && error.response.status === 401) {
      recordTest('Translation Endpoints', true);
      return true;
    }
    recordTest('Translation Endpoints', false, error.message);
    return false;
  }
}

async function testFrontendBuild() {
  try {
    const buildPath = path.join(__dirname, 'dist');
    const indexPath = path.join(buildPath, 'index.html');
    
    if (fs.existsSync(indexPath)) {
      recordTest('Frontend Build', true);
      return true;
    } else {
      recordTest('Frontend Build', false, 'dist/index.html not found');
      return false;
    }
  } catch (error) {
    recordTest('Frontend Build', false, error.message);
    return false;
  }
}

async function testEnvironmentVariables() {
  try {
    const envPath = path.join(__dirname, '.env');
    let hasRequiredVars = true;
    let missingVars = [];
    
    const requiredVars = [
      'VITE_API_BASE_URL',
      'VITE_WS_URL'
    ];
    
    if (fs.existsSync(envPath)) {
      const envContent = fs.readFileSync(envPath, 'utf8');
      
      requiredVars.forEach(varName => {
        if (!envContent.includes(varName)) {
          hasRequiredVars = false;
          missingVars.push(varName);
        }
      });
    } else {
      hasRequiredVars = false;
      missingVars = requiredVars;
    }
    
    recordTest('Environment Variables', hasRequiredVars, 
      missingVars.length > 0 ? `Missing: ${missingVars.join(', ')}` : null);
    return hasRequiredVars;
  } catch (error) {
    recordTest('Environment Variables', false, error.message);
    return false;
  }
}

async function testPackageIntegrity() {
  try {
    const packagePath = path.join(__dirname, 'package.json');
    const lockPath = path.join(__dirname, 'package-lock.json');
    
    if (!fs.existsSync(packagePath)) {
      recordTest('Package Integrity', false, 'package.json not found');
      return false;
    }
    
    const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
    const hasRequiredDeps = packageJson.dependencies && 
                           packageJson.dependencies.react &&
                           packageJson.dependencies['@reduxjs/toolkit'] &&
                           packageJson.dependencies.axios;
    
    recordTest('Package Integrity', hasRequiredDeps && fs.existsSync(lockPath));
    return hasRequiredDeps;
  } catch (error) {
    recordTest('Package Integrity', false, error.message);
    return false;
  }
}

// Main test runner
async function runIntegrationTests() {
  log('🚀 Starting SynText Frontend Integration Tests', 'info');
  log('='.repeat(50), 'info');
  
  // Test environment and build
  log('Testing Environment...', 'info');
  await testEnvironmentVariables();
  await testPackageIntegrity();
  await testFrontendBuild();
  
  // Test backend connectivity
  log('Testing Backend Connectivity...', 'info');
  const backendHealthy = await testBackendHealth();
  
  if (backendHealthy) {
    await testDatabaseConnection();
    await testRedisConnection();
    await testAuthEndpoints();
    await testTranslationEndpoints();
  } else {
    log('⚠️  Backend is not running, skipping backend tests', 'warning');
  }
  
  // Generate report
  log('='.repeat(50), 'info');
  log('📊 Test Results Summary', 'info');
  log(`✅ Passed: ${testResults.passed}`, 'success');
  log(`❌ Failed: ${testResults.failed}`, testResults.failed > 0 ? 'error' : 'info');
  log(`📈 Success Rate: ${((testResults.passed / (testResults.passed + testResults.failed)) * 100).toFixed(1)}%`, 'info');
  
  // Save detailed report
  const reportPath = path.join(__dirname, 'integration-test-report.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    summary: {
      passed: testResults.passed,
      failed: testResults.failed,
      total: testResults.passed + testResults.failed,
      successRate: (testResults.passed / (testResults.passed + testResults.failed)) * 100
    },
    tests: testResults.tests
  }, null, 2));
  
  log(`📄 Detailed report saved to: ${reportPath}`, 'info');
  
  if (testResults.failed > 0) {
    log('🔧 Some tests failed. Please check the issues above.', 'warning');
    process.exit(1);
  } else {
    log('🎉 All tests passed! System is ready for production.', 'success');
    process.exit(0);
  }
}

// Run tests
if (require.main === module) {
  runIntegrationTests().catch(error => {
    log(`💥 Test runner crashed: ${error.message}`, 'error');
    process.exit(1);
  });
}

module.exports = { runIntegrationTests };
