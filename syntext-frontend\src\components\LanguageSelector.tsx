import React, { useState, useEffect, useRef } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import { fetchLanguages } from '../store/slices/translationSlice';
import type { Language } from '../store/slices/translationSlice';

interface LanguageSelectorProps {
  type: 'source' | 'target';
  value: string;
  onChange: (language: string) => void;
  disabled?: boolean;
}

export const LanguageSelector: React.FC<LanguageSelectorProps> = ({
  type,
  value,
  onChange,
  disabled = false,
}) => {
  const dispatch = useAppDispatch();
  const { languages } = useAppSelector((state) => state.translation);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedLanguage, setSelectedLanguage] = useState<Language | null>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (languages.all.length === 0 && !languages.loading) {
      dispatch(fetchLanguages());
    }
  }, [dispatch, languages.all.length, languages.loading]);

  useEffect(() => {
    const lang = languages.all.find(l => l.code === value) || 
                 languages.popular.find(l => l.code === value);
    setSelectedLanguage(lang || null);
  }, [value, languages.all, languages.popular]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const handleToggle = () => {
    if (disabled) return;
    setIsOpen(!isOpen);
    if (!isOpen) {
      setTimeout(() => searchRef.current?.focus(), 100);
    }
  };

  const handleSelect = (language: Language) => {
    onChange(language.code);
    setIsOpen(false);
    setSearchTerm('');
  };

  const filteredLanguages = [...languages.popular, ...languages.all].filter(language => {
    if (!searchTerm) return true;
    const term = searchTerm.toLowerCase();
    return (
      language.name.toLowerCase().includes(term) ||
      language.nativeName.toLowerCase().includes(term) ||
      language.code.toLowerCase().includes(term)
    );
  });

  const uniqueLanguages = filteredLanguages.reduce((acc, current) => {
    const existing = acc.find(lang => lang.code === current.code);
    if (!existing) {
      acc.push(current);
    }
    return acc;
  }, [] as Language[]);

  const getDisplayText = () => {
    if (type === 'source' && value === 'auto') {
      return '自动检测';
    }
    return selectedLanguage ? selectedLanguage.nativeName : '选择语言';
  };

  const getLanguageIcon = (language: Language) => {
    const regionFlags: { [key: string]: string } = {
      'zh': '🇨🇳',
      'en': '🇺🇸',
      'ja': '🇯🇵',
      'ko': '🇰🇷',
      'fr': '🇫🇷',
      'de': '🇩🇪',
      'es': '🇪🇸',
      'it': '🇮🇹',
      'pt': '🇵🇹',
      'ru': '🇷🇺',
      'ar': '🇸🇦',
      'hi': '🇮🇳',
      'th': '🇹🇭',
      'vi': '🇻🇳',
      'tr': '🇹🇷',
      'pl': '🇵🇱',
      'nl': '🇳🇱',
      'sv': '🇸🇪',
      'da': '🇩🇰',
      'no': '🇳🇴',
      'fi': '🇫🇮',
    };
    return regionFlags[language.code] || '🌐';
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        onClick={handleToggle}
        disabled={disabled}
        className={`
          w-full min-w-[140px] px-3 py-2 text-left bg-white border border-gray-300 rounded-md shadow-sm
          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
          transition-colors duration-200
          ${disabled 
            ? 'bg-gray-100 text-gray-500 cursor-not-allowed' 
            : 'hover:border-gray-400 cursor-pointer'
          }
        `}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {selectedLanguage && (
              <span className="text-lg">{getLanguageIcon(selectedLanguage)}</span>
            )}
            {type === 'source' && value === 'auto' && (
              <span className="text-lg">🔍</span>
            )}
            <span className="text-sm font-medium text-gray-900 truncate">
              {getDisplayText()}
            </span>
          </div>
          <svg
            className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
              isOpen ? 'transform rotate-180' : ''
            }`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </button>

      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-hidden">
          <div className="p-2 border-b border-gray-200">
            <input
              ref={searchRef}
              type="text"
              placeholder="搜索语言..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>

          <div className="overflow-y-auto max-h-48">
            {type === 'source' && (
              <button
                type="button"
                onClick={() => handleSelect({ code: 'auto', name: '自动检测', nativeName: '自动检测', region: '', isPopular: true })}
                className={`
                  w-full px-3 py-2 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100
                  transition-colors duration-150
                  ${value === 'auto' ? 'bg-blue-50 text-blue-700' : 'text-gray-900'}
                `}
              >
                <div className="flex items-center space-x-3">
                  <span className="text-lg">🔍</span>
                  <div>
                    <div className="text-sm font-medium">自动检测</div>
                    <div className="text-xs text-gray-500">Auto Detect</div>
                  </div>
                </div>
              </button>
            )}

            {languages.loading ? (
              <div className="px-3 py-4 text-center text-gray-500">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2"></div>
                <div className="text-sm">加载中...</div>
              </div>
            ) : uniqueLanguages.length === 0 ? (
              <div className="px-3 py-4 text-center text-gray-500">
                <div className="text-sm">未找到匹配的语言</div>
              </div>
            ) : (
              <>
                {languages.popular.length > 0 && !searchTerm && (
                  <>
                    <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gray-50">
                      常用语言
                    </div>
                    {languages.popular.map((language) => (
                      <button
                        key={`popular-${language.code}`}
                        type="button"
                        onClick={() => handleSelect(language)}
                        className={`
                          w-full px-3 py-2 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100
                          transition-colors duration-150
                          ${value === language.code ? 'bg-blue-50 text-blue-700' : 'text-gray-900'}
                        `}
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{getLanguageIcon(language)}</span>
                          <div>
                            <div className="text-sm font-medium">{language.nativeName}</div>
                            <div className="text-xs text-gray-500">{language.name}</div>
                          </div>
                        </div>
                      </button>
                    ))}
                    <div className="border-t border-gray-200 my-1"></div>
                  </>
                )}

                {!searchTerm && (
                  <div className="px-3 py-2 text-xs font-semibold text-gray-500 uppercase tracking-wider bg-gray-50">
                    所有语言
                  </div>
                )}

                {uniqueLanguages
                  .filter(lang => searchTerm || !languages.popular.some(p => p.code === lang.code))
                  .map((language) => (
                    <button
                      key={`all-${language.code}`}
                      type="button"
                      onClick={() => handleSelect(language)}
                      className={`
                        w-full px-3 py-2 text-left hover:bg-gray-100 focus:outline-none focus:bg-gray-100
                        transition-colors duration-150
                        ${value === language.code ? 'bg-blue-50 text-blue-700' : 'text-gray-900'}
                      `}
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-lg">{getLanguageIcon(language)}</span>
                        <div>
                          <div className="text-sm font-medium">{language.nativeName}</div>
                          <div className="text-xs text-gray-500">{language.name}</div>
                        </div>
                      </div>
                    </button>
                  ))}
              </>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;