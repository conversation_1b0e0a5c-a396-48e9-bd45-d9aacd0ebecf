import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppDispatch, useAppSelector } from '../hooks';
import { sendVerificationCode, verifyCodeAndLogin, clearError } from '../store/slices/authSlice';
import Logo from '../components/Logo';

const LoginPage = () => {
  const [email, setEmail] = useState('');
  const [verificationCode, setVerificationCode] = useState('');
  const [isCodeSent, setIsCodeSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [sendError, setSendError] = useState('');
  
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { loading, error, isAuthenticated } = useAppSelector((state) => state.auth);

  useEffect(() => {
    if (isAuthenticated) {
      navigate('/', { replace: true });
    }
  }, [isAuthenticated, navigate]);

  useEffect(() => {
    // 清除错误状态
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const handleSendCode = async () => {
    if (!email || loading) return;
    
    setSendError('');
    const result = await dispatch(sendVerificationCode(email));
    
    if (sendVerificationCode.fulfilled.match(result)) {
      setIsCodeSent(true);
      setCountdown(60);
      
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else {
      setSendError(result.payload as string);
    }
  };

  const handleLogin = async () => {
    if (!email || !verificationCode || loading) return;
    
    const result = await dispatch(verifyCodeAndLogin({ email, code: verificationCode }));
    
    if (verifyCodeAndLogin.fulfilled.match(result)) {
      navigate('/', { replace: true });
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4">
      <div className="max-w-md w-full space-y-8">
        {/* Logo和标题 */}
        <div className="text-center">
          <Logo size="lg" />
          <h2 className="mt-6 text-3xl font-bold text-gray-900">
            登录到 SynText
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            使用邮箱验证码登录
          </p>
        </div>

        {/* 登录表单 */}
        <div className="bg-white py-8 px-6 shadow rounded-lg border border-gray-200">
          <form className="space-y-6" onSubmit={(e) => e.preventDefault()}>
            {/* 邮箱输入 */}
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                邮箱地址
              </label>
              <input
                id="email"
                name="email"
                type="email"
                autoComplete="email"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="请输入您的邮箱地址"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={isCodeSent}
              />
            </div>

            {/* 验证码输入 */}
            {isCodeSent && (
              <div>
                <label htmlFor="code" className="block text-sm font-medium text-gray-700 mb-2">
                  验证码
                </label>
                <input
                  id="code"
                  name="code"
                  type="text"
                  required
                  maxLength={6}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-center text-lg font-mono tracking-widest"
                  placeholder="请输入6位验证码"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                />
                <p className="text-xs text-gray-500 text-center mt-2">
                  验证码已发送至 <span className="font-medium text-blue-600">{email}</span>
                </p>
              </div>
            )}

            {/* 错误提示 */}
            {(sendError || error) && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <div className="flex">
                  <div className="text-sm text-red-700">
                    {sendError || error}
                </div>
                </div>
              </div>
            )}

            {/* 按钮区域 */}
            <div className="space-y-4">
              {!isCodeSent ? (
                <button
                  type="button"
                  onClick={handleSendCode}
                  disabled={!email || loading}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? '发送中...' : '发送验证码'}
                </button>
              ) : (
                <div className="space-y-4">
                  {/* 登录按钮 */}
                  <button
                    type="button"
                    onClick={handleLogin}
                    disabled={!verificationCode || verificationCode.length !== 6 || loading}
                    className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {loading ? '登录中...' : '立即登录'}
                  </button>

                  {/* 重新发送按钮 */}
                  <button
                    type="button"
                    onClick={handleSendCode}
                    disabled={countdown > 0 || loading}
                    className="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {countdown > 0 ? `重新发送 (${countdown}s)` : '重新发送验证码'}
                  </button>
                </div>
              )}
            </div>
          </form>

          {/* 页面底部信息 */}
          <div className="mt-6 text-center">
            <p className="text-xs text-gray-500">
              登录即表示您同意我们的
              <a href="#" className="text-blue-600 hover:text-blue-500 font-medium">服务条款</a>
              和
              <a href="#" className="text-blue-600 hover:text-blue-500 font-medium">隐私政策</a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;