import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, Between } from 'typeorm';
import { Payment, PaymentStatus } from '../../entities/payment.entity';
import { UserSubscription } from '../../entities/user-subscription.entity';
import { User } from '../../entities/user.entity';

export interface BillingSummary {
  totalRevenue: number;
  totalPayments: number;
  completedPayments: number;
  failedPayments: number;
  refundedPayments: number;
  averageOrderValue: number;
  monthlyRevenue: number;
  yearlyRevenue: number;
}

export interface UserBilling {
  userId: string;
  userEmail: string;
  totalSpent: number;
  paymentCount: number;
  lastPaymentDate: Date | null;
  currentSubscription: UserSubscription | null;
  paymentHistory: Payment[];
}

@Injectable()
export class BillingService {
  private readonly logger = new Logger(BillingService.name);

  constructor(
    @InjectRepository(Payment)
    private paymentRepository: Repository<Payment>,
    @InjectRepository(UserSubscription)
    private userSubscriptionRepository: Repository<UserSubscription>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
  ) {}

  async getBillingSummary(startDate?: Date, endDate?: Date): Promise<BillingSummary> {
    const queryBuilder = this.paymentRepository.createQueryBuilder('payment');

    if (startDate && endDate) {
      queryBuilder.where('payment.createdAt BETWEEN :startDate AND :endDate', {
        startDate,
        endDate,
      });
    }

    const payments = await queryBuilder.getMany();

    const completedPayments = payments.filter(p => p.status === PaymentStatus.COMPLETED);
    const failedPayments = payments.filter(p => p.status === PaymentStatus.FAILED);
    const refundedPayments = payments.filter(p => p.status === PaymentStatus.REFUNDED);

    const totalRevenue = completedPayments.reduce((sum, payment) => sum + Number(payment.finalAmount), 0);
    const averageOrderValue = completedPayments.length > 0 ? totalRevenue / completedPayments.length : 0;

    // 计算月度和年度收入
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const startOfYear = new Date(now.getFullYear(), 0, 1);

    const monthlyPayments = await this.paymentRepository.find({
      where: {
        status: PaymentStatus.COMPLETED,
        createdAt: Between(startOfMonth, now),
      },
    });

    const yearlyPayments = await this.paymentRepository.find({
      where: {
        status: PaymentStatus.COMPLETED,
        createdAt: Between(startOfYear, now),
      },
    });

    const monthlyRevenue = monthlyPayments.reduce((sum, payment) => sum + Number(payment.finalAmount), 0);
    const yearlyRevenue = yearlyPayments.reduce((sum, payment) => sum + Number(payment.finalAmount), 0);

    return {
      totalRevenue,
      totalPayments: payments.length,
      completedPayments: completedPayments.length,
      failedPayments: failedPayments.length,
      refundedPayments: refundedPayments.length,
      averageOrderValue: Math.round(averageOrderValue * 100) / 100,
      monthlyRevenue,
      yearlyRevenue,
    };
  }

  async getUserBilling(userId: string): Promise<UserBilling | null> {
    const user = await this.userRepository.findOne({
      where: { id: userId },
    });

    if (!user) {
      return null;
    }

    const payments = await this.paymentRepository.find({
      where: { userId },
      order: { createdAt: 'DESC' },
      relations: ['coupon'],
    });

    const completedPayments = payments.filter(p => p.status === PaymentStatus.COMPLETED);
    const totalSpent = completedPayments.reduce((sum, payment) => sum + Number(payment.finalAmount), 0);
    const lastPaymentDate = completedPayments.length > 0 ? completedPayments[0].createdAt : null;

    const currentSubscription = await this.userSubscriptionRepository.findOne({
      where: { userId, status: 'active' as any },
      relations: ['plan'],
      order: { createdAt: 'DESC' },
    });

    return {
      userId,
      userEmail: user.email,
      totalSpent,
      paymentCount: completedPayments.length,
      lastPaymentDate,
      currentSubscription,
      paymentHistory: payments,
    };
  }

  async generateInvoice(paymentId: string): Promise<any> {
    const payment = await this.paymentRepository.findOne({
      where: { id: paymentId },
      relations: ['user', 'coupon'],
    });

    if (!payment) {
      throw new Error('支付记录不存在');
    }

    const invoice = {
      invoiceNumber: `INV-${payment.transactionId}`,
      paymentId: payment.id,
      transactionId: payment.transactionId,
      customerInfo: {
        userId: payment.user.id,
        email: payment.user.email,
        name: payment.user.email, // 使用邮箱作为名称，实际项目中可能有专门的姓名字段
      },
      paymentInfo: {
        amount: payment.amount,
        discountAmount: payment.discountAmount,
        finalAmount: payment.finalAmount,
        currency: payment.currency,
        method: payment.method,
        status: payment.status,
        createdAt: payment.createdAt,
        completedAt: payment.completedAt,
      },
      couponInfo: payment.coupon ? {
        code: payment.coupon.code,
        name: payment.coupon.name,
        type: payment.coupon.type,
        value: payment.coupon.value,
      } : null,
      description: payment.description,
      metadata: payment.metadata,
    };

    this.logger.log(`Generated invoice for payment ${paymentId}`);
    return invoice;
  }

  async getRevenueAnalytics(days: number = 30): Promise<any[]> {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const payments = await this.paymentRepository
      .createQueryBuilder('payment')
      .select([
        'DATE(payment.createdAt) as date',
        'COUNT(*) as paymentCount',
        'SUM(CASE WHEN payment.status = :completed THEN payment.finalAmount ELSE 0 END) as revenue',
        'COUNT(CASE WHEN payment.status = :completed THEN 1 END) as completedCount',
        'COUNT(CASE WHEN payment.status = :failed THEN 1 END) as failedCount',
      ])
      .where('payment.createdAt >= :startDate', { startDate })
      .andWhere('payment.createdAt <= :endDate', { endDate })
      .setParameter('completed', PaymentStatus.COMPLETED)
      .setParameter('failed', PaymentStatus.FAILED)
      .groupBy('DATE(payment.createdAt)')
      .orderBy('date', 'ASC')
      .getRawMany();

    return payments.map(item => ({
      date: item.date,
      paymentCount: parseInt(item.paymentCount, 10),
      revenue: parseFloat(item.revenue || '0'),
      completedCount: parseInt(item.completedCount, 10),
      failedCount: parseInt(item.failedCount, 10),
      successRate: item.paymentCount > 0 
        ? Math.round((item.completedCount / item.paymentCount) * 100)
        : 0,
    }));
  }

  async getTopCustomers(limit: number = 10): Promise<any[]> {
    const customers = await this.paymentRepository
      .createQueryBuilder('payment')
      .select([
        'payment.userId as userId',
        'user.email as userEmail',
        'COUNT(payment.id) as paymentCount',
        'SUM(CASE WHEN payment.status = :completed THEN payment.finalAmount ELSE 0 END) as totalSpent',
        'MAX(payment.createdAt) as lastPaymentDate',
      ])
      .leftJoin('payment.user', 'user')
      .where('payment.status = :completed')
      .setParameter('completed', PaymentStatus.COMPLETED)
      .groupBy('payment.userId, user.email')
      .orderBy('totalSpent', 'DESC')
      .limit(limit)
      .getRawMany();

    return customers.map(customer => ({
      userId: customer.userId,
      userEmail: customer.userEmail,
      paymentCount: parseInt(customer.paymentCount, 10),
      totalSpent: parseFloat(customer.totalSpent || '0'),
      lastPaymentDate: customer.lastPaymentDate,
    }));
  }

  async getPaymentMethodStats(): Promise<any[]> {
    const stats = await this.paymentRepository
      .createQueryBuilder('payment')
      .select([
        'payment.method as method',
        'COUNT(*) as count',
        'SUM(CASE WHEN payment.status = :completed THEN payment.finalAmount ELSE 0 END) as revenue',
        'COUNT(CASE WHEN payment.status = :completed THEN 1 END) as completedCount',
      ])
      .where('payment.status IN (:...statuses)', { 
        statuses: [PaymentStatus.COMPLETED, PaymentStatus.FAILED, PaymentStatus.PENDING] 
      })
      .setParameter('completed', PaymentStatus.COMPLETED)
      .groupBy('payment.method')
      .orderBy('revenue', 'DESC')
      .getRawMany();

    return stats.map(stat => ({
      method: stat.method,
      totalCount: parseInt(stat.count, 10),
      completedCount: parseInt(stat.completedCount, 10),
      revenue: parseFloat(stat.revenue || '0'),
      successRate: stat.count > 0 
        ? Math.round((stat.completedCount / stat.count) * 100)
        : 0,
    }));
  }

  async exportBillingData(startDate: Date, endDate: Date): Promise<Payment[]> {
    return this.paymentRepository.find({
      where: {
        createdAt: Between(startDate, endDate),
      },
      relations: ['user', 'coupon'],
      order: { createdAt: 'DESC' },
    });
  }
}