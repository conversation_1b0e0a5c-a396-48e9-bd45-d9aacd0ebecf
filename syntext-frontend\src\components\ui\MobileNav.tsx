import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { cn } from '../../utils/cn';

interface NavItem {
  path: string;
  label: string;
  icon: React.ReactNode;
  badge?: number;
}

interface MobileNavProps {
  items: NavItem[];
  className?: string;
}

const MobileNav: React.FC<MobileNavProps> = ({ items, className }) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleNavigation = (path: string) => {
    navigate(path);
  };

  return (
    <nav className={cn(
      'mobile-nav bg-white/95 backdrop-blur-sm border-t border-secondary-200 shadow-lg',
      'fixed bottom-0 left-0 right-0 z-40',
      'flex items-center justify-around py-2 px-4',
      'safe-area-inset-bottom',
      className
    )}>
      {items.map((item) => {
        const isActive = location.pathname === item.path;
        
        return (
          <button
            key={item.path}
            onClick={() => handleNavigation(item.path)}
            className={cn(
              'touch-target flex flex-col items-center justify-center',
              'px-3 py-2 rounded-lg transition-all duration-200',
              'active:scale-95 active:bg-secondary-100',
              isActive 
                ? 'text-primary-600 bg-primary-50' 
                : 'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-50'
            )}
            aria-label={item.label}
          >
            <div className="relative">
              <div className={cn(
                'w-6 h-6 transition-transform duration-200',
                isActive && 'scale-110'
              )}>
                {item.icon}
              </div>
              
              {/* 徽章 */}
              {item.badge && item.badge > 0 && (
                <div className="absolute -top-2 -right-2 min-w-[18px] h-[18px] bg-error-500 text-white text-xs font-bold rounded-full flex items-center justify-center">
                  {item.badge > 99 ? '99+' : item.badge}
                </div>
              )}
            </div>
            
            <span className={cn(
              'text-xs font-medium mt-1 transition-colors duration-200',
              isActive ? 'text-primary-600' : 'text-secondary-600'
            )}>
              {item.label}
            </span>
          </button>
        );
      })}
    </nav>
  );
};

// 预定义的导航项目
export const defaultNavItems: NavItem[] = [
  {
    path: '/',
    label: '首页',
    icon: (
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
      </svg>
    ),
  },
  {
    path: '/translate',
    label: '翻译',
    icon: (
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
      </svg>
    ),
  },
  {
    path: '/history',
    label: '历史',
    icon: (
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    ),
  },
  {
    path: '/profile',
    label: '我的',
    icon: (
      <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="w-full h-full">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
    ),
  },
];

export default MobileNav;
