import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import type { PayloadAction } from '@reduxjs/toolkit';
import api from '../../services/api';

export interface SubscriptionPlan {
  id: string;
  type: 'free' | 'basic' | 'premium' | 'enterprise';
  name: string;
  description: string;
  monthlyPrice: number;
  yearlyPrice: number;
  monthlyCharacterLimit: number;
  dailyCharacterLimit: number;
  maxTranslationsPerDay: number;
  supportsCaching: boolean;
  supportsBatchTranslation: boolean;
  prioritySupport: boolean;
  apiAccess: boolean;
  maxConcurrentTranslations: number;
  status: 'active' | 'inactive';
  sortOrder: number;
}

export interface UserSubscription {
  id: string;
  planId: string;
  plan: SubscriptionPlan;
  status: 'active' | 'expired' | 'cancelled' | 'pending';
  billingCycle: 'monthly' | 'yearly';
  amount: number;
  startDate: string;
  endDate: string;
  nextBillingDate: string | null;
  autoRenewal: boolean;
  isTrial: boolean;
  trialDaysUsed: number;
  monthlyCharactersUsed: number;
  dailyCharactersUsed: number;
  dailyTranslationsUsed: number;
}

export interface UsageStats {
  monthlyCharactersUsed: number;
  monthlyCharacterLimit: number;
  dailyCharactersUsed: number;
  dailyCharacterLimit: number;
  dailyTranslationsUsed: number;
  dailyTranslationLimit: number;
  remainingMonthlyCharacters: number;
  remainingDailyCharacters: number;
  remainingDailyTranslations: number;
  usagePercentage: number;
}

interface SubscriptionState {
  plans: {
    list: SubscriptionPlan[];
    loading: boolean;
    error: string | null;
  };
  currentSubscription: {
    data: UserSubscription | null;
    loading: boolean;
    error: string | null;
  };
  usage: {
    stats: UsageStats | null;
    loading: boolean;
    error: string | null;
  };
  selectedPlan: SubscriptionPlan | null;
  selectedBillingCycle: 'monthly' | 'yearly';
}

const initialState: SubscriptionState = {
  plans: {
    list: [],
    loading: false,
    error: null,
  },
  currentSubscription: {
    data: null,
    loading: false,
    error: null,
  },
  usage: {
    stats: null,
    loading: false,
    error: null,
  },
  selectedPlan: null,
  selectedBillingCycle: 'monthly',
};

// Async thunks
export const fetchSubscriptionPlans = createAsyncThunk(
  'subscription/fetchPlans',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/subscription/plans');
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取订阅套餐失败');
    }
  }
);

export const fetchCurrentSubscription = createAsyncThunk(
  'subscription/fetchCurrent',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/subscription/current');
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取当前订阅失败');
    }
  }
);

export const fetchUsageStats = createAsyncThunk(
  'subscription/fetchUsage',
  async (_, { rejectWithValue }) => {
    try {
      const response = await api.get('/subscription/usage');
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '获取使用量统计失败');
    }
  }
);

export const createSubscription = createAsyncThunk(
  'subscription/create',
  async (data: {
    planType: string;
    billingCycle: 'monthly' | 'yearly';
    autoRenewal?: boolean;
  }, { rejectWithValue }) => {
    try {
      const response = await api.post('/subscription/subscribe', data);
      return response.data.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '创建订阅失败');
    }
  }
);

export const cancelSubscription = createAsyncThunk(
  'subscription/cancel',
  async (reason: string | undefined, { rejectWithValue }) => {
    try {
      const response = await api.delete('/subscription/cancel', {
        data: { reason },
      });
      return response.data;
    } catch (error: any) {
      return rejectWithValue(error.response?.data?.message || '取消订阅失败');
    }
  }
);

const subscriptionSlice = createSlice({
  name: 'subscription',
  initialState,
  reducers: {
    setSelectedPlan: (state, action: PayloadAction<SubscriptionPlan | null>) => {
      state.selectedPlan = action.payload;
    },
    setSelectedBillingCycle: (state, action: PayloadAction<'monthly' | 'yearly'>) => {
      state.selectedBillingCycle = action.payload;
    },
    clearErrors: (state) => {
      state.plans.error = null;
      state.currentSubscription.error = null;
      state.usage.error = null;
    },
  },
  extraReducers: (builder) => {
    // Fetch subscription plans
    builder
      .addCase(fetchSubscriptionPlans.pending, (state) => {
        state.plans.loading = true;
        state.plans.error = null;
      })
      .addCase(fetchSubscriptionPlans.fulfilled, (state, action) => {
        state.plans.loading = false;
        state.plans.list = action.payload;
      })
      .addCase(fetchSubscriptionPlans.rejected, (state, action) => {
        state.plans.loading = false;
        state.plans.error = action.payload as string;
      });

    // Fetch current subscription
    builder
      .addCase(fetchCurrentSubscription.pending, (state) => {
        state.currentSubscription.loading = true;
        state.currentSubscription.error = null;
      })
      .addCase(fetchCurrentSubscription.fulfilled, (state, action) => {
        state.currentSubscription.loading = false;
        state.currentSubscription.data = action.payload;
      })
      .addCase(fetchCurrentSubscription.rejected, (state, action) => {
        state.currentSubscription.loading = false;
        state.currentSubscription.error = action.payload as string;
      });

    // Fetch usage stats
    builder
      .addCase(fetchUsageStats.pending, (state) => {
        state.usage.loading = true;
        state.usage.error = null;
      })
      .addCase(fetchUsageStats.fulfilled, (state, action) => {
        state.usage.loading = false;
        state.usage.stats = action.payload;
      })
      .addCase(fetchUsageStats.rejected, (state, action) => {
        state.usage.loading = false;
        state.usage.error = action.payload as string;
      });

    // Create subscription
    builder
      .addCase(createSubscription.pending, (state) => {
        state.currentSubscription.loading = true;
        state.currentSubscription.error = null;
      })
      .addCase(createSubscription.fulfilled, (state, action) => {
        state.currentSubscription.loading = false;
        state.currentSubscription.data = action.payload;
      })
      .addCase(createSubscription.rejected, (state, action) => {
        state.currentSubscription.loading = false;
        state.currentSubscription.error = action.payload as string;
      });

    // Cancel subscription
    builder
      .addCase(cancelSubscription.fulfilled, (state) => {
        if (state.currentSubscription.data) {
          state.currentSubscription.data.status = 'cancelled';
          state.currentSubscription.data.autoRenewal = false;
        }
      });
  },
});

export const {
  setSelectedPlan,
  setSelectedBillingCycle,
  clearErrors,
} = subscriptionSlice.actions;

export default subscriptionSlice.reducer;