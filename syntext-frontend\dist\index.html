<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Vite + React + TS</title>
    <script type="module" crossorigin src="/assets/index-c7T1_3zn.js"></script>
    <link rel="modulepreload" crossorigin href="/assets/react-vendor-CEjTMBxM.js">
    <link rel="modulepreload" crossorigin href="/assets/redux-vendor-bhcDk3Gk.js">
    <link rel="modulepreload" crossorigin href="/assets/utils-vendor-Bl6Su708.js">
    <link rel="modulepreload" crossorigin href="/assets/router-vendor-DBo3Rl4Z.js">
    <link rel="stylesheet" crossorigin href="/assets/index-C2NIwFQs.css">
  </head>
  <body>
    <div id="root"></div>
  </body>
</html>
