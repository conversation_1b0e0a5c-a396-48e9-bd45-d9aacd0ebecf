import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as nodemailer from 'nodemailer';

@Injectable()
export class EmailService {
  private readonly logger = new Logger(EmailService.name);
  private transporter: nodemailer.Transporter;

  constructor(private configService: ConfigService) {
    this.createTransporter();
  }

  private createTransporter() {
    try {
      const emailHost = this.configService.get<string>('EMAIL_HOST');
      const emailPort = this.configService.get<number>('EMAIL_PORT');
      const emailSecure = this.configService.get<boolean>('EMAIL_SECURE', false);

      this.transporter = nodemailer.createTransport({
        host: emailHost,
        port: emailPort,
        secure: emailSecure, // 阿里云邮箱推送服务使用SSL
        auth: {
          user: this.configService.get<string>('EMAIL_USER'),
          pass: this.configService.get<string>('EMAIL_PASSWORD'),
        },
        // 阿里云邮箱推送服务的额外配置
        tls: {
          rejectUnauthorized: false
        }
      });

      this.transporter.verify((error, success) => {
        if (error) {
          this.logger.error('Email transporter verification failed:', error);
        } else {
          this.logger.log('Email transporter is ready to send messages');
        }
      });
    } catch (error) {
      this.logger.error('Failed to create email transporter:', error);
    }
  }

  async sendVerificationCode(email: string, code: string): Promise<boolean> {
    try {
      this.logger.log(`=== 验证码发送 ===`);
      this.logger.log(`收件人: ${email}`);
      this.logger.log(`验证码: ${code}`);
      this.logger.log(`有效期: 5分钟`);
      this.logger.log(`==================`);

      const isDevelopment = this.configService.get('APP_ENV') === 'development';
      const from = this.configService.get<string>('EMAIL_FROM');

      // 准备邮件选项
      const mailOptions = {
        from,
        to: email,
        subject: 'SynText 验证码 - 请在5分钟内使用',
        html: this.getVerificationCodeTemplate(code),
      };

      // 尝试发送邮件
      try {
        await this.transporter.sendMail(mailOptions);
        this.logger.log(`✅ 验证码邮件已成功发送到 ${email}`);
        return true;
      } catch (emailError) {
        this.logger.error(`❌ 邮件发送失败 (${email}):`, emailError);

        // 在开发环境中，即使邮件发送失败也返回成功（用于测试）
        if (isDevelopment) {
          this.logger.warn('🔧 开发环境：邮件发送失败但返回成功状态');
          return true;
        }

        return false;
      }
    } catch (error) {
      this.logger.error(`验证码发送过程中发生错误 (${email}):`, error);

      // 开发环境容错处理
      const isDevelopment = this.configService.get('APP_ENV') === 'development';
      if (isDevelopment) {
        this.logger.warn('🔧 开发环境：发生错误但返回成功状态');
        return true;
      }

      return false;
    }
  }

  private getVerificationCodeTemplate(code: string): string {
    return `
      <!DOCTYPE html>
      <html>
        <head>
          <meta charset="utf-8">
          <title>SynText 验证码</title>
          <style>
            .container {
              max-width: 600px;
              margin: 0 auto;
              padding: 20px;
              font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
              background-color: #f9f9f9;
            }
            .content {
              background-color: white;
              padding: 30px;
              border-radius: 8px;
              box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .header {
              text-align: center;
              margin-bottom: 30px;
            }
            .logo {
              font-size: 24px;
              font-weight: bold;
              color: #4f46e5;
              margin-bottom: 10px;
            }
            .title {
              font-size: 20px;
              color: #374151;
              margin-bottom: 20px;
            }
            .code-container {
              text-align: center;
              margin: 30px 0;
            }
            .verification-code {
              font-size: 32px;
              font-weight: bold;
              color: #4f46e5;
              background-color: #f3f4f6;
              padding: 15px 30px;
              border-radius: 8px;
              display: inline-block;
              letter-spacing: 4px;
            }
            .message {
              color: #6b7280;
              line-height: 1.6;
              margin-bottom: 20px;
            }
            .warning {
              color: #ef4444;
              font-size: 14px;
              text-align: center;
              margin-top: 20px;
            }
            .footer {
              text-align: center;
              margin-top: 30px;
              padding-top: 20px;
              border-top: 1px solid #e5e7eb;
              color: #9ca3af;
              font-size: 12px;
            }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="content">
              <div class="header">
                <div class="logo">SynText</div>
                <div class="title">验证码登录</div>
              </div>
              
              <div class="message">
                您好！<br><br>
                您正在登录 SynText 翻译系统，请使用以下验证码完成登录：
              </div>
              
              <div class="code-container">
                <div class="verification-code">${code}</div>
              </div>
              
              <div class="message">
                此验证码 5 分钟内有效，请尽快使用。如果您没有请求此验证码，请忽略此邮件。
              </div>
              
              <div class="warning">
                为了您的账户安全，请勿将验证码泄露给他人。
              </div>
              
              <div class="footer">
                此邮件由 SynText 系统自动发送，请勿回复。<br>
                © 2025 SynText. All rights reserved.
              </div>
            </div>
          </div>
        </body>
      </html>
    `;
  }
}