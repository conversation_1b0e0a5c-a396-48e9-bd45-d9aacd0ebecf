import { CouponService } from '../services/coupon.service';
export declare class CouponController {
    private readonly couponService;
    constructor(couponService: CouponService);
    validateCoupon(req: any, body: {
        code: string;
        originalAmount: number;
        planType?: string;
    }): Promise<{
        code: number;
        message: string;
        data: {
            valid: boolean;
            couponId: string;
            couponName: string;
            discountAmount: number;
            finalAmount: number;
            type: import("../../entities").CouponType;
            value: number;
            error?: undefined;
        } | {
            valid: boolean;
            error: string;
            couponId?: undefined;
            couponName?: undefined;
            discountAmount?: undefined;
            finalAmount?: undefined;
            type?: undefined;
            value?: undefined;
        };
    }>;
    getCouponByCode(code: string): Promise<{
        code: number;
        message: string;
        data: {
            name: string;
            description: string;
            type: import("../../entities").CouponType;
            value: number;
            minOrderAmount: number;
            maxDiscountAmount: number;
            validFrom: Date;
            validTo: Date;
            status: import("../../entities").CouponStatus;
        };
    }>;
    getAllCoupons(): Promise<{
        code: number;
        message: string;
        data: import("../../entities").Coupon[];
    }>;
    getCouponStats(couponId: string): Promise<{
        code: number;
        message: string;
        data: {
            coupon: import("../../entities").Coupon;
            usageCount: number;
            totalDiscountAmount: number;
            totalOriginalAmount: number;
            remainingUsage: number;
            usages: import("../../entities/coupon-usage.entity").CouponUsage[];
        };
    }>;
}
