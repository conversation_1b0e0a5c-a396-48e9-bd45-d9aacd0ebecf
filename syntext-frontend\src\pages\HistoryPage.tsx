import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../hooks';
import {
  fetchTranslationHistory,
  fetchFavorites,
  toggleFavorite,
  type Translation
} from '../store/slices/translationSlice';



interface HistoryQuery {
  page: number;
  limit: number;
  search: string;
  sourceLang: string;
  targetLang: string;
  startDate: string;
  endDate: string;
  onlyFavorites: boolean;
}

const HistoryPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { history, favorites, languages } = useAppSelector((state) => state.translation);

  // 计算总页数
  const totalPages = Math.ceil(history.total / history.limit);

  const [selectedTranslations, setSelectedTranslations] = useState<Set<string>>(new Set());
  const [query, setQuery] = useState<HistoryQuery>({
    page: 1,
    limit: 20,
    search: '',
    sourceLang: '',
    targetLang: '',
    startDate: '',
    endDate: '',
    onlyFavorites: false,
  });


  useEffect(() => {
    // 获取翻译历史
    dispatch(fetchTranslationHistory({ page: query.page, limit: query.limit }));

    // 如果显示收藏，也获取收藏列表
    if (query.onlyFavorites) {
      dispatch(fetchFavorites());
    }
  }, [dispatch, query.page, query.limit, query.onlyFavorites]);

  const handleToggleFavorite = (translationId: string) => {
    dispatch(toggleFavorite(translationId));
  };

  const handleSearch = () => {
    setQuery(prev => ({ ...prev, page: 1 }));
    dispatch(fetchTranslationHistory({ page: 1, limit: query.limit }));
  };

  const handlePageChange = (newPage: number) => {
    setQuery(prev => ({ ...prev, page: newPage }));
  };

  const getDisplayTranslations = () => {
    if (query.onlyFavorites) {
      return favorites;
    }
    return history.translations;
  };

  const filteredTranslations = getDisplayTranslations().filter(translation => {
    if (query.search && !translation.sourceText.toLowerCase().includes(query.search.toLowerCase()) &&
        !translation.translatedText.toLowerCase().includes(query.search.toLowerCase())) {
      return false;
    }
    if (query.sourceLang && translation.sourceLanguage !== query.sourceLang) {
      return false;
    }
    if (query.targetLang && translation.targetLanguage !== query.targetLang) {
      return false;
    }
    return true;
  });
  const handleSelectTranslation = (id: string) => {
    const newSelected = new Set(selectedTranslations);
    if (newSelected.has(id)) {
      newSelected.delete(id);
    } else {
      newSelected.add(id);
    }
    setSelectedTranslations(newSelected);
  };

  const handleSelectAll = () => {
    if (selectedTranslations.size === filteredTranslations.length) {
      setSelectedTranslations(new Set());
    } else {
      setSelectedTranslations(new Set(filteredTranslations.map(t => t.id)));
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedTranslations.size === 0) return;

    try {
      // TODO: 调用实际API删除选中的翻译
      // 暂时只清空选择状态，等待API实现
      setSelectedTranslations(new Set());
      console.log('删除功能待实现');
    } catch (error) {
      console.error('删除翻译失败:', error);
    }
  };

  const handleRetranslate = (translation: Translation) => {
    // 跳转到翻译页面并预填内容
    navigate('/translate', {
      state: {
        sourceText: translation.sourceText,
        sourceLanguage: translation.sourceLanguage,
        targetLanguage: translation.targetLanguage,
      }
    });
  };

  const handleCopyTranslation = (text: string) => {
    navigator.clipboard.writeText(text);
    // 显示复制成功反馈
    const toast = document.createElement('div');
    toast.className = 'fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50';
    toast.textContent = '复制成功';
    document.body.appendChild(toast);
    setTimeout(() => {
      document.body.removeChild(toast);
    }, 2000);
  };

  const getLanguageName = (code: string) => {
    const allLanguages = [...languages.popular, ...languages.all];
    const lang = allLanguages.find(l => l.code === code);
    return lang ? lang.name : code.toUpperCase();
  };

  const getQualityBadge = (quality: number) => {
    const percentage = Math.round(quality * 100);
    let colorClass = '';

    if (percentage >= 90) {
      colorClass = 'bg-green-100 text-green-800';
    } else if (percentage >= 70) {
      colorClass = 'bg-yellow-100 text-yellow-800';
    } else {
      colorClass = 'bg-red-100 text-red-800';
    }

    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colorClass}`}>
        {percentage}%
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const truncateText = (text: string, maxLength: number = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="text-gray-500 hover:text-gray-700 mr-4"
              >
                ← 返回首页
              </button>
              <h1 className="text-xl font-bold text-gray-900">翻译历史</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">{user?.name || user?.email}</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 搜索和筛选 */}
        <div className="bg-white shadow rounded-lg p-4 sm:p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3 sm:gap-4">
            <div className="lg:col-span-2">
              <label className="block text-sm font-medium text-gray-700 mb-2">搜索内容</label>
              <div className="flex">
                <input
                  type="text"
                  className="flex-1 block w-full px-3 py-2 border border-gray-300 rounded-l-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                  placeholder="搜索翻译内容..."
                  value={query.search}
                  onChange={(e) => setQuery({ ...query, search: e.target.value })}
                  onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                />
                <button
                  onClick={handleSearch}
                  className="px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100"
                >
                  <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </button>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">源语言</label>
              <select
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={query.sourceLang}
                onChange={(e) => setQuery({ ...query, sourceLang: e.target.value, page: 1 })}
              >
                <option value="">全部语言</option>
                <optgroup label="热门语言">
                  {languages.popular.map((lang) => (
                    <option key={lang.code} value={lang.code}>
                      {lang.name}
                    </option>
                  ))}
                </optgroup>
                {languages.all.length > 0 && (
                  <optgroup label="其他语言">
                    {languages.all.filter(lang =>
                      !languages.popular.some(popular => popular.code === lang.code)
                    ).map((lang) => (
                      <option key={lang.code} value={lang.code}>
                        {lang.name}
                      </option>
                    ))}
                  </optgroup>
                )}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">目标语言</label>
              <select
                className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                value={query.targetLang}
                onChange={(e) => setQuery({ ...query, targetLang: e.target.value, page: 1 })}
              >
                <option value="">全部语言</option>
                <optgroup label="热门语言">
                  {languages.popular.map((lang) => (
                    <option key={lang.code} value={lang.code}>
                      {lang.name}
                    </option>
                  ))}
                </optgroup>
                {languages.all.length > 0 && (
                  <optgroup label="其他语言">
                    {languages.all.filter(lang =>
                      !languages.popular.some(popular => popular.code === lang.code)
                    ).map((lang) => (
                      <option key={lang.code} value={lang.code}>
                        {lang.name}
                      </option>
                    ))}
                  </optgroup>
                )}
              </select>
            </div>

            <div className="flex items-end">
              <label className="flex items-center">
                <input
                  type="checkbox"
                  className="rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50"
                  checked={query.onlyFavorites}
                  onChange={(e) => setQuery({ ...query, onlyFavorites: e.target.checked, page: 1 })}
                />
                <span className="ml-2 text-sm text-gray-700">仅显示收藏</span>
              </label>
            </div>
          </div>

          <div className="mt-4 flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => {
                  setQuery({
                    page: 1,
                    limit: 20,
                    search: '',
                    sourceLang: '',
                    targetLang: '',
                    startDate: '',
                    endDate: '',
                    onlyFavorites: false,
                  });
                  dispatch(fetchTranslationHistory({ page: 1, limit: 20 }));
                }}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50"
              >
                重置筛选
              </button>
            </div>

            {selectedTranslations.size > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-700">
                  已选择 {selectedTranslations.size} 项
                </span>
                <button
                  onClick={handleDeleteSelected}
                  className="px-3 py-1 text-sm text-red-600 hover:text-red-800"
                >
                  删除选中
                </button>
              </div>
            )}
          </div>
        </div>

        {/* 翻译列表 */}
        <div className="bg-white shadow overflow-hidden sm:rounded-lg">
          {history.loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
            </div>
          ) : filteredTranslations.length === 0 ? (
            <div className="text-center py-12">
              <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="mt-2 text-sm font-medium text-gray-900">暂无翻译记录</h3>
              <p className="mt-1 text-sm text-gray-500">开始您的第一次翻译吧！</p>
              <div className="mt-6">
                <button
                  onClick={() => navigate('/translate')}
                  className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
                >
                  开始翻译
                </button>
              </div>
            </div>
          ) : (
            <>
              {/* 列表头部 */}
              <div className="bg-gray-50 px-6 py-3 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                      checked={selectedTranslations.size === filteredTranslations.length && filteredTranslations.length > 0}
                      onChange={handleSelectAll}
                    />
                    <span className="ml-2 text-sm text-gray-700">全选</span>
                  </label>
                  <div className="text-sm text-gray-500">
                    共 {query.onlyFavorites ? favorites.length : history.total} 条记录
                  </div>
                </div>
              </div>

              {/* 翻译项目 */}
              <div className="divide-y divide-gray-200">
                {filteredTranslations.map((translation) => (
                  <div key={translation.id} className="p-4 sm:p-6 hover:bg-gray-50">
                    <div className="flex items-start space-x-3 sm:space-x-4">
                      <input
                        type="checkbox"
                        className="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        checked={selectedTranslations.has(translation.id)}
                        onChange={() => handleSelectTranslation(translation.id)}
                      />

                      <div className="flex-1 space-y-2 sm:space-y-3">
                        {/* 翻译内容 */}
                        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4">
                          <div>
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-xs text-gray-500">原文</span>
                              <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                                {getLanguageName(translation.sourceLanguage)}
                              </span>
                            </div>
                            <p className="text-sm text-gray-900" title={translation.sourceText}>
                              {truncateText(translation.sourceText)}
                            </p>
                          </div>

                          <div>
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-xs text-gray-500">译文</span>
                              <span className="text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded">
                                {getLanguageName(translation.targetLanguage)}
                              </span>
                            </div>
                            <p className="text-sm text-blue-600" title={translation.translatedText}>
                              {truncateText(translation.translatedText)}
                            </p>
                          </div>
                        </div>

                        {/* 底部信息 */}
                        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0">
                          <div className="flex items-center space-x-2 sm:space-x-4 text-xs sm:text-sm text-gray-500">
                            <span>{formatDate(translation.createdAt)}</span>
                            {translation.quality && getQualityBadge(translation.quality)}
                          </div>

                          <div className="flex items-center space-x-1 sm:space-x-2">
                            <button
                              onClick={() => handleToggleFavorite(translation.id)}
                              className={`p-1 rounded ${
                                translation.isFavorite
                                  ? 'text-yellow-500 hover:text-yellow-600'
                                  : 'text-gray-400 hover:text-yellow-500'
                              }`}
                              title={translation.isFavorite ? '取消收藏' : '收藏'}
                            >
                              <svg className="h-5 w-5" fill={translation.isFavorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                              </svg>
                            </button>

                            <button
                              onClick={() => handleRetranslate(translation)}
                              className="p-1 text-gray-400 hover:text-blue-600 rounded"
                              title="重新翻译"
                            >
                              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                            </button>

                            <button
                              onClick={() => handleCopyTranslation(translation.translatedText)}
                              className="p-1 text-gray-400 hover:text-blue-600 rounded"
                              title="复制译文"
                            >
                              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                              </svg>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* 分页 */}
              {totalPages > 1 && (
                <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      disabled={query.page === 1}
                      onClick={() => handlePageChange(query.page - 1)}
                      className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      disabled={query.page === totalPages}
                      onClick={() => handlePageChange(query.page + 1)}
                      className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-gray-700">
                        显示第 <span className="font-medium">{(query.page - 1) * query.limit + 1}</span> 到{' '}
                        <span className="font-medium">
                          {Math.min(query.page * query.limit, history.total)}
                        </span>{' '}
                        条，共 <span className="font-medium">{history.total}</span> 条记录
                      </p>
                    </div>
                    <div>
                      <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                        <button
                          disabled={query.page === 1}
                          onClick={() => handlePageChange(query.page - 1)}
                          className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                        >
                          上一页
                        </button>
                        <button
                          disabled={query.page === totalPages}
                          onClick={() => handlePageChange(query.page + 1)}
                          className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                        >
                          下一页
                        </button>
                      </nav>
                    </div>
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  );
};

export default HistoryPage;