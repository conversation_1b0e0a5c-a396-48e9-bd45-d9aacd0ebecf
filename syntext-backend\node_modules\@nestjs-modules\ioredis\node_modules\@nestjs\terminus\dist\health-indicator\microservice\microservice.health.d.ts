import { HealthIndicator, type HealthIndicatorResult } from '../';
import { type PropType } from '../../utils';
interface MicroserviceOptionsLike {
    transport?: number;
    options?: object;
}
/**
 * The options for the `MicroserviceHealthIndicator`
 */
export type MicroserviceHealthIndicatorOptions<T extends MicroserviceOptionsLike = MicroserviceOptionsLike> = {
    transport: Required<PropType<MicroserviceOptionsLike, 'transport'>>;
    timeout?: number;
} & Partial<T>;
/**
 * The MicroserviceHealthIndicator is a health indicators
 * which is used for health checks related to microservices
 *
 * @publicApi
 * @module TerminusModule
 */
export declare class MicroserviceHealthIndicator extends HealthIndicator {
    private nestJsMicroservices;
    /**
     * Initializes the health indicator
     */
    constructor();
    /**
     * Checks if the dependant packages are present
     */
    private checkDependantPackages;
    private pingMicroservice;
    /**
     * Prepares and throw a HealthCheckError
     * @param key The key which will be used for the result object
     * @param error The thrown error
     * @param timeout The timeout in ms
     *
     * @throws {HealthCheckError}
     */
    private generateError;
    /**
     * Checks if the given microservice is up
     * @param key The key which will be used for the result object
     * @param options The options of the microservice
     *
     * @throws {HealthCheckError} If the microservice is not reachable
     *
     * @example
     * microservice.pingCheck<TcpClientOptions>('tcp', {
     *   transport: Transport.TCP,
     *   options: { host: 'localhost', port: 3001 },
     * })
     */
    pingCheck<MicroserviceClientOptions extends MicroserviceOptionsLike>(key: string, options: MicroserviceHealthIndicatorOptions<MicroserviceClientOptions>): Promise<HealthIndicatorResult>;
}
export {};
