import { Repository } from 'typeorm';
import { Payment, PaymentStatus, PaymentMethod } from '../../entities/payment.entity';
import { UserSubscription } from '../../entities/user-subscription.entity';
import { PaymentGatewayService } from './payment-gateway.service';
import { SubscriptionService } from '../../subscription/services/subscription.service';
export interface CreatePaymentDto {
    userId: string;
    subscriptionId: string;
    amount: number;
    currency: string;
    method: PaymentMethod;
    description: string;
    couponId?: string;
    returnUrl?: string;
}
export declare class PaymentService {
    private paymentRepository;
    private userSubscriptionRepository;
    private paymentGatewayService;
    private subscriptionService;
    private readonly logger;
    constructor(paymentRepository: Repository<Payment>, userSubscriptionRepository: Repository<UserSubscription>, paymentGatewayService: PaymentGatewayService, subscriptionService: SubscriptionService);
    createPayment(createDto: CreatePaymentDto): Promise<{
        paymentId: string;
        transactionId: string;
        amount: number;
        currency: string;
        method: PaymentMethod;
        paymentUrl: string;
        qrCode: string;
        status: PaymentStatus;
    }>;
    handlePaymentCallback(paymentId: string, callbackData: any): Promise<void>;
    markPaymentAsCompleted(paymentId: string): Promise<void>;
    markPaymentAsFailed(paymentId: string, reason: string): Promise<void>;
    getPaymentById(paymentId: string): Promise<Payment | null>;
    getUserPayments(userId: string, limit?: number, offset?: number): Promise<Payment[]>;
    refundPayment(paymentId: string, amount?: number, reason?: string): Promise<boolean>;
    checkPaymentStatus(paymentId: string): Promise<Payment>;
    private generateTransactionId;
}
