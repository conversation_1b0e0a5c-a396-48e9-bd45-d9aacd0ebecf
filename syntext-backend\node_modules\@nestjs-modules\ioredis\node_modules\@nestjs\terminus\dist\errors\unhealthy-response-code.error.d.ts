import { HealthCheckError } from '../health-check/health-check.error';
/**
 * Error which gets thrown when the terminus client receives
 * an unhealthy response code from the server.
 * @publicApi
 */
export declare class UnhealthyResponseCodeError extends HealthCheckError {
    /**
     * Initializes the error
     *
     * @param {string | number} responseCode The response code
     * @param {any} cause The cause of the health check error
     *
     * @internal
     */
    constructor(responseCode: string, cause: any);
}
