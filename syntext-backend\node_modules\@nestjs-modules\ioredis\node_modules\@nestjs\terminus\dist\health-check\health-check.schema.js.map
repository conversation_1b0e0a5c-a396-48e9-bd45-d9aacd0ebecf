{"version": 3, "file": "health-check.schema.js", "sourceRoot": "", "sources": ["../../lib/health-check/health-check.schema.ts"], "names": [], "mappings": ";;;AAIA,8CAA8C;AAC9C,MAAM,UAAU,GAA0B,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC;AACzE,MAAM,aAAa,GAA0B;IAC3C,KAAK,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,mBAAmB,EAAE;CACxD,CAAC;AACF,MAAM,gBAAgB,mCACjB,UAAU,GACV,aAAa,CACjB,CAAC;AAEF,MAAM,qBAAqB,GAAG,CAAC,OAA8B,EAAE,EAAE,CAAC,CAAC;IACjE,IAAI,EAAE,QAAQ;IACd,OAAO;IACP,oBAAoB,EAAE;QACpB,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;aACf;SACF;QACD,oBAAoB,EAAE;YACpB,IAAI,EAAE,QAAQ;SACf;KACF;CACF,CAAC,CAAC;AAEH,SAAgB,oBAAoB,CAAC,MAAyB;IAC5D,OAAO;QACL,IAAI,EAAE,QAAQ;QACd,UAAU,EAAE;YACV,MAAM,EAAE;gBACN,IAAI,EAAE,QAAQ;gBACd,OAAO,EAAE,MAAM;aAChB;YACD,IAAI,kCACC,qBAAqB,CAAC,UAAU,CAAC,KACpC,QAAQ,EAAE,IAAI,GACf;YACD,KAAK,kCACA,qBAAqB,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,KACjE,QAAQ,EAAE,IAAI,GACf;YACD,OAAO,EAAE,qBAAqB,CAC5B,MAAM,KAAK,OAAO,CAAC,CAAC,CAAC,gBAAgB,CAAC,CAAC,CAAC,UAAU,CACnD;SACF;KACF,CAAC;AACJ,CAAC;AArBD,oDAqBC"}