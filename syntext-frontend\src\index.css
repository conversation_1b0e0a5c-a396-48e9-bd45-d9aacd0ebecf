@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  *, *::before, *::after {
    box-sizing: border-box;
  }

  html {
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
  }

  html, body {
    margin: 0;
    padding: 0;
    width: 100%;
    min-height: 100vh;
    font-family: 'Inter', system-ui, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  body {
    background-color: #f9fafb;
    color: #111827;
    line-height: 1.6;
    overflow-x: hidden;
  }

  #root {
    width: 100%;
    min-height: 100vh;
    position: relative;
  }

  /* 改进的滚动条样式 */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
    transition: background-color 0.2s ease;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
  }

  /* Firefox 滚动条 */
  * {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
  }
}

@layer components {
  /* 响应式容器 */
  .container-responsive {
    @apply w-full mx-auto px-4 sm:px-6 lg:px-8;
    max-width: 1280px;
  }

  /* 移动端友好的触摸目标 */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  /* 移动端优化的按钮 */
  .btn-mobile {
    @apply touch-target px-4 py-2 rounded-lg font-medium transition-all duration-200;
    @apply active:scale-95 active:bg-opacity-80;
  }

  /* 卡片悬停效果 */
  .card-hover {
    @apply transition-all duration-300 ease-out;
    @apply hover:shadow-lg hover:-translate-y-1;
  }

  /* 渐变文本 */
  .text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-accent-600 bg-clip-text text-transparent;
  }

  /* 玻璃态效果 */
  .glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }

  /* 移动端导航 */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 bg-white border-t border-secondary-200;
    @apply flex items-center justify-around py-2 px-4;
    @apply safe-area-inset-bottom;
  }

  /* 安全区域适配 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-inset-bottom {
    padding-bottom: max(1rem, env(safe-area-inset-bottom));
  }
}

@layer utilities {
  /* 动画工具类 */
  .animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.5s ease-out;
  }

  /* 文本截断 */
  .text-truncate-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .text-truncate-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 移动端隐藏滚动条 */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* 触摸优化 */
  .touch-manipulation {
    touch-action: manipulation;
  }

  /* 防止文本选择 */
  .select-none-important {
    -webkit-user-select: none !important;
    -moz-user-select: none !important;
    -ms-user-select: none !important;
    user-select: none !important;
  }
}

/* 自定义动画关键帧 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 移动端特定样式 */
@media (max-width: 768px) {
  .mobile-optimized {
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }

  /* 移动端表单优化 */
  input, textarea, select {
    font-size: 16px; /* 防止 iOS Safari 缩放 */
  }

  /* 移动端按钮优化 */
  button {
    -webkit-tap-highlight-color: transparent;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .card-hover {
    @apply border-2 border-secondary-300;
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
