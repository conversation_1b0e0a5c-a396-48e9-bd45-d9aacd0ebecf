import React from 'react';
import type { AppError } from '../utils/errorHandling';
import { ErrorHandler, ErrorCodes } from '../utils/errorHandling';

interface ErrorDisplayProps {
  error: AppError | Error | string | null;
  onRetry?: () => void;
  onDismiss?: () => void;
  className?: string;
  variant?: 'alert' | 'card' | 'inline' | 'toast';
  showDetails?: boolean;
}

export const ErrorDisplay: React.FC<ErrorDisplayProps> = ({
  error,
  onRetry,
  onDismiss,
  className = '',
  variant = 'alert' as 'alert' | 'card' | 'inline' | 'toast',
  showDetails = false,
}) => {
  if (!error) return null;

  // 标准化错误对象
  const appError = typeof error === 'string'
    ? { code: ErrorCodes.UNKNOWN, message: error, timestamp: new Date().toISOString() }
    : error instanceof Error
    ? ErrorHandler.parseError(error)
    : error as AppError;

  const message = ErrorHandler.getUserMessage(appError);
  const isRetryable = ErrorHandler.isRetryableError(appError);

  const getVariantClasses = () => {
    switch (variant) {
      case 'card':
        return 'bg-white border border-red-200 rounded-lg shadow-sm p-6';
      case 'inline':
        return 'bg-red-50 border-l-4 border-red-400 p-4';
      case 'toast':
        return 'bg-red-500 text-white rounded-lg shadow-lg p-4';
      default:
        return 'bg-red-50 border border-red-200 rounded-md p-4';
    }
  };

  const getTextClasses = () => {
    return variant === 'toast' ? 'text-white' : 'text-red-800';
  };

  const getIconClasses = () => {
    return variant === 'toast' ? 'text-white' : 'text-red-400';
  };

  return (
    <div className={`${getVariantClasses()} ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <svg
            className={`h-5 w-5 ${getIconClasses()}`}
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <div className="ml-3 flex-1">
          <h3 className={`text-sm font-medium ${getTextClasses()}`}>
            {variant === 'toast' ? '错误' : '出现错误'}
          </h3>
          <div className={`mt-2 text-sm ${getTextClasses()}`}>
            <p>{message}</p>
          </div>
          
          {showDetails && appError.code && (
            <div className={`mt-2 text-xs ${variant === 'toast' ? 'text-red-100' : 'text-red-600'}`}>
              错误代码: {appError.code}
            </div>
          )}

          {(onRetry || onDismiss) && (
            <div className="mt-4 flex space-x-2">
              {onRetry && isRetryable && (
                <button
                  onClick={onRetry}
                  className={`text-sm font-medium ${
                    variant === 'toast'
                      ? 'text-white hover:text-red-100 underline'
                      : 'text-red-800 hover:text-red-600 underline'
                  }`}
                >
                  重试
                </button>
              )}
              {onDismiss && (
                <button
                  onClick={onDismiss}
                  className={`text-sm font-medium ${
                    variant === 'toast'
                      ? 'text-white hover:text-red-100 underline'
                      : 'text-red-800 hover:text-red-600 underline'
                  }`}
                >
                  关闭
                </button>
              )}
            </div>
          )}
        </div>
        
        {onDismiss && variant !== 'toast' && (
          <div className="ml-auto pl-3">
            <div className="-mx-1.5 -my-1.5">
              <button
                onClick={onDismiss}
                className={`inline-flex rounded-md p-1.5 ${
                  (variant as string) === 'toast'
                    ? 'text-white hover:bg-red-600'
                    : 'text-red-400 hover:bg-red-100'
                } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500`}
              >
                <span className="sr-only">关闭</span>
                <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                    clipRule="evenodd"
                  />
                </svg>
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

// 空状态组件
export const EmptyState: React.FC<{
  title: string;
  description?: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
  className?: string;
}> = ({ title, description, icon, action, className = '' }) => {
  return (
    <div className={`text-center py-12 ${className}`}>
      <div className="mx-auto h-12 w-12 text-gray-400 mb-4">
        {icon || (
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        )}
      </div>
      <h3 className="text-lg font-medium text-gray-900 mb-2">{title}</h3>
      {description && (
        <p className="text-gray-500 mb-6 max-w-sm mx-auto">{description}</p>
      )}
      {action && (
        <button
          onClick={action.onClick}
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          {action.label}
        </button>
      )}
    </div>
  );
};

// 网络状态指示器
export const NetworkStatus: React.FC<{
  isOnline: boolean;
  className?: string;
}> = ({ isOnline, className = '' }) => {
  if (isOnline) return null;

  return (
    <div className={`bg-yellow-50 border-l-4 border-yellow-400 p-4 ${className}`}>
      <div className="flex">
        <div className="flex-shrink-0">
          <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
            <path
              fillRule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clipRule="evenodd"
            />
          </svg>
        </div>
        <div className="ml-3">
          <p className="text-sm text-yellow-700">
            网络连接已断开，某些功能可能无法正常使用
          </p>
        </div>
      </div>
    </div>
  );
};

// 加载状态包装器
export const LoadingWrapper: React.FC<{
  loading: boolean;
  error?: AppError | Error | string | null;
  children: React.ReactNode;
  loadingComponent?: React.ReactNode;
  errorComponent?: React.ReactNode;
  onRetry?: () => void;
  className?: string;
}> = ({
  loading,
  error,
  children,
  loadingComponent,
  errorComponent,
  onRetry,
  className = '',
}) => {
  if (loading) {
    return (
      <div className={className}>
        {loadingComponent || (
          <div className="flex justify-center items-center py-8">
            <div className="w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          </div>
        )}
      </div>
    );
  }

  if (error) {
    return (
      <div className={className}>
        {errorComponent || (
          <ErrorDisplay error={error} onRetry={onRetry} variant="card" />
        )}
      </div>
    );
  }

  return <div className={className}>{children}</div>;
};

export default ErrorDisplay;
