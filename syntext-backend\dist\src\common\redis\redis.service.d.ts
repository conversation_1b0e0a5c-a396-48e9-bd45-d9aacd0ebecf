import { Redis } from 'ioredis';
export declare class RedisService {
    private readonly redis;
    constructor(redis: Redis);
    get(key: string): Promise<string | null>;
    set(key: string, value: string, ttl?: number): Promise<void>;
    del(key: string): Promise<void>;
    exists(key: string): Promise<boolean>;
    expire(key: string, ttl: number): Promise<void>;
    ttl(key: string): Promise<number>;
    hget(key: string, field: string): Promise<string | null>;
    hset(key: string, field: string, value: string): Promise<void>;
    hgetall(key: string): Promise<Record<string, string>>;
    hdel(key: string, field: string): Promise<void>;
    sadd(key: string, member: string): Promise<void>;
    srem(key: string, member: string): Promise<void>;
    smembers(key: string): Promise<string[]>;
    sismember(key: string, member: string): Promise<boolean>;
    cacheTranslation(sourceText: string, sourceLang: string, targetLang: string, translatedText: string, ttl?: number): Promise<void>;
    getCachedTranslation(sourceText: string, sourceLang: string, targetLang: string): Promise<string | null>;
    private getTranslationCacheKey;
    incrementRateLimit(identifier: string, windowSize?: number, limit?: number): Promise<{
        count: number;
        remaining: number;
        resetTime: number;
    }>;
    checkRateLimit(identifier: string, windowSize?: number): Promise<number>;
    setSession(sessionId: string, data: any, ttl?: number): Promise<void>;
    getSession(sessionId: string): Promise<any | null>;
    deleteSession(sessionId: string): Promise<void>;
    setVerificationCode(email: string, code: string, ttl?: number): Promise<void>;
    getVerificationCode(email: string): Promise<string | null>;
    deleteVerificationCode(email: string): Promise<void>;
    incrementUserUsage(userId: string, type: 'translation' | 'api_call', period?: 'daily' | 'monthly'): Promise<number>;
    getUserUsage(userId: string, type: 'translation' | 'api_call', period?: 'daily' | 'monthly'): Promise<number>;
}
