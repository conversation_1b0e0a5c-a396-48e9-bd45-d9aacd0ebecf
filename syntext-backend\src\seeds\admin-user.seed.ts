import { DataSource } from 'typeorm';
import * as bcrypt from 'bcryptjs';
import { User, UserRole, UserStatus } from '../entities/user.entity';

export async function seedAdminUser(dataSource: DataSource) {
  const userRepository = dataSource.getRepository(User);

  // 检查是否已经有管理员用户
  const existingAdmin = await userRepository.findOne({
    where: { role: UserRole.ADMIN },
  });

  if (existingAdmin) {
    console.log('Admin user already exists, skipping seed...');
    return;
  }

  // 创建默认管理员用户
  const adminUser = userRepository.create({
    email: '<EMAIL>',
    name: '系统管理员',
    role: UserRole.ADMIN,
    status: UserStatus.ACTIVE,
    translationCount: 0,
    monthlyTranslationCount: 0,
    lastLoginAt: new Date(),
    preferences: {
      defaultSourceLanguage: 'auto',
      defaultTargetLanguage: 'zh',
      theme: 'light',
    },
  } as any);

  await userRepository.save(adminUser);
  console.log('Admin user created successfully!');
  console.log('Email: <EMAIL>');
  console.log('Please set up authentication through the application.');
}
