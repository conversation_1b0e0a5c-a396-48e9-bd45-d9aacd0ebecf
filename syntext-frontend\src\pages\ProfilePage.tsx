import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../hooks';
import { logout } from '../store/slices/authSlice';



interface UserProfile {
  id: string;
  email: string;
  name: string;
  role: string;
  status: string;
  translationCount: number;
  monthlyTranslationCount: number;
  createdAt: string;
  lastLoginAt: string;
  preferences: {
    defaultSourceLanguage?: string;
    defaultTargetLanguage?: string;
    theme?: string;
  };
}

interface UsageStats {
  totalTranslations: number;
  totalCharacters: number;
  monthlyTranslations: number;
  monthlyCharacters: number;
  favoriteTranslations: number;
  averageConfidence: number;
}

interface SubscriptionInfo {
  planType: string;
  status: string;
  currentPeriodStart: string;
  currentPeriodEnd: string;
  usageLimit: number;
  usageCount: number;
  features: string[];
}

const ProfilePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);

  
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [stats, setStats] = useState<UsageStats | null>(null);
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null);
  const [loading, setLoading] = useState(true);



  useEffect(() => {
    fetchUserData();
  }, []);

  const fetchUserData = async () => {
    try {
      setLoading(true);
      // TODO: 调用实际API获取用户数据
      // 模拟数据
      setTimeout(() => {
        const mockProfile: UserProfile = {
          id: user?.id || '1',
          email: user?.email || '<EMAIL>',
          name: user?.name || '张三',
          role: user?.role || 'user',
          status: 'active',
          translationCount: 156,
          monthlyTranslationCount: 45,
          createdAt: '2025-01-15T10:00:00Z',
          lastLoginAt: '2025-08-03T14:30:00Z',
          preferences: {
            defaultSourceLanguage: 'auto',
            defaultTargetLanguage: 'zh',
            theme: 'light',
          },
        };

        const mockStats: UsageStats = {
          totalTranslations: 156,
          totalCharacters: 8450,
          monthlyTranslations: 45,
          monthlyCharacters: 2340,
          favoriteTranslations: 12,
          averageConfidence: 0.94,
        };

        const mockSubscription: SubscriptionInfo = {
          planType: 'premium',
          status: 'active',
          currentPeriodStart: '2025-08-01T00:00:00Z',
          currentPeriodEnd: '2025-09-01T00:00:00Z',
          usageLimit: -1,
          usageCount: 45,
          features: [
            '无限翻译',
            '高质量翻译',
            '批量翻译',
            '优先支持',
          ],
        };

        setProfile(mockProfile);
        setStats(mockStats);
        setSubscription(mockSubscription);

        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('获取用户数据失败:', error);
      setLoading(false);
    }
  };



  const handleLogout = () => {
    dispatch(logout());
    navigate('/login');
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getPlanBadge = (planType: string) => {
    const colors = {
      free: 'bg-gray-100 text-gray-800',
      premium: 'bg-yellow-100 text-yellow-800',
      enterprise: 'bg-purple-100 text-purple-800',
    };
    const labels = {
      free: '免费版',
      premium: '高级版',
      enterprise: '企业版',
    };
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[planType as keyof typeof colors]}`}>
        {labels[planType as keyof typeof labels]}
      </span>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex justify-center items-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="text-gray-500 hover:text-gray-700 mr-4"
              >
                ← 返回首页
              </button>
              <h1 className="text-xl font-bold text-gray-900">个人中心</h1>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleLogout}
                className="text-gray-500 hover:text-gray-700"
              >
                退出登录
              </button>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* 左侧：用户信息 */}
          <div className="lg:col-span-1">
            <div className="bg-white shadow rounded-lg p-4 sm:p-6">
              <div className="text-center">
                <div className="mx-auto h-24 w-24 bg-primary-100 rounded-full flex items-center justify-center">
                  <span className="text-2xl font-bold text-primary-600">
                    {profile?.name?.charAt(0) || profile?.email.charAt(0).toUpperCase()}
                  </span>
                </div>
                <h2 className="mt-4 text-xl font-bold text-gray-900">
                  {profile?.name || '未设置姓名'}
                </h2>
                <p className="text-gray-500">{profile?.email}</p>
                <div className="mt-3">
                  {subscription && getPlanBadge(subscription.planType)}
                </div>
              </div>

              <div className="mt-6 border-t pt-6">
                <dl className="space-y-3">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">注册时间</dt>
                    <dd className="text-sm text-gray-900">
                      {profile?.createdAt && formatDate(profile.createdAt)}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">最后登录</dt>
                    <dd className="text-sm text-gray-900">
                      {profile?.lastLoginAt && formatDate(profile.lastLoginAt)}
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">账户状态</dt>
                    <dd className="text-sm text-green-600 font-medium">正常</dd>
                  </div>
                </dl>
              </div>

              <div className="mt-6">
                <button
                  onClick={() => navigate('/settings')}
                  className="w-full bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors"
                >
                  编辑个人信息
                </button>
              </div>
            </div>
          </div>

          {/* 右侧：详细信息 */}
          <div className="lg:col-span-2 space-y-6 lg:space-y-8">
            {/* 使用统计 */}
            <div className="bg-white shadow rounded-lg p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4 sm:mb-6">使用统计</h3>
              <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary-600">
                    {stats?.totalTranslations.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500">总翻译次数</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {stats?.totalCharacters.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500">总翻译字符</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {stats?.monthlyTranslations.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500">本月翻译</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {stats?.favoriteTranslations.toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-500">收藏翻译</div>
                </div>
              </div>

              <div className="mt-6">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm font-medium text-gray-500">翻译质量</span>
                  <span className="text-sm font-bold text-gray-900">
                    {stats && Math.round(stats.averageConfidence * 100)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${stats && stats.averageConfidence * 100}%` }}
                  ></div>
                </div>
              </div>
            </div>

            {/* 订阅信息 */}
            {subscription && (
              <div className="bg-white shadow rounded-lg p-4 sm:p-6">
                <div className="flex justify-between items-center mb-6">
                  <h3 className="text-lg font-medium text-gray-900">订阅信息</h3>
                  <button
                    onClick={() => navigate('/subscription')}
                    className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                  >
                    管理订阅
                  </button>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-2">当前套餐</h4>
                    <div className="flex items-center space-x-2">
                      {getPlanBadge(subscription.planType)}
                      <span className="text-sm text-green-600">
                        ({subscription.status === 'active' ? '有效' : '无效'})
                      </span>
                    </div>
                  </div>

                  <div>
                    <h4 className="text-sm font-medium text-gray-500 mb-2">订阅周期</h4>
                    <p className="text-sm text-gray-900">
                      {formatDate(subscription.currentPeriodStart)} 
                      <br />
                      至 {formatDate(subscription.currentPeriodEnd)}
                    </p>
                  </div>
                </div>

                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-500 mb-3">套餐特权</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                    {subscription.features.map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <svg className="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                        <span className="text-sm text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {subscription.usageLimit > 0 && (
                  <div className="mt-6">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-500">本月用量</span>
                      <span className="text-sm text-gray-900">
                        {subscription.usageCount} / {subscription.usageLimit}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-primary-600 h-2 rounded-full"
                        style={{ 
                          width: `${Math.min((subscription.usageCount / subscription.usageLimit) * 100, 100)}%` 
                        }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* 快速操作 */}
            <div className="bg-white shadow rounded-lg p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium text-gray-900 mb-4 sm:mb-6">快速操作</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                <button
                  onClick={() => navigate('/translate')}
                  className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
                >
                  <div className="text-center">
                    <svg className="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                    </svg>
                    <span className="text-sm font-medium text-gray-900">开始翻译</span>
                  </div>
                </button>

                <button
                  onClick={() => navigate('/history')}
                  className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
                >
                  <div className="text-center">
                    <svg className="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm font-medium text-gray-900">翻译历史</span>
                  </div>
                </button>

                <button
                  onClick={() => navigate('/help')}
                  className="flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors"
                >
                  <div className="text-center">
                    <svg className="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-sm font-medium text-gray-900">帮助支持</span>
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfilePage;