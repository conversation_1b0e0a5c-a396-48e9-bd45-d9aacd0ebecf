"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateSubscriptionTables1691000002000 = void 0;
const typeorm_1 = require("typeorm");
class CreateSubscriptionTables1691000002000 {
    name = 'CreateSubscriptionTables1691000002000';
    async up(queryRunner) {
        await queryRunner.query(`
      CREATE TYPE "subscription_plan" AS ENUM('free', 'basic', 'premium', 'enterprise')
    `);
        await queryRunner.query(`
      CREATE TYPE "subscription_status" AS ENUM('active', 'inactive', 'cancelled', 'expired')
    `);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'subscription_plans',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'name',
                    type: 'varchar',
                    length: '100',
                    isNullable: false,
                },
                {
                    name: 'plan',
                    type: 'enum',
                    enum: ['free', 'basic', 'premium', 'enterprise'],
                    isNullable: false,
                },
                {
                    name: 'price',
                    type: 'decimal',
                    precision: 10,
                    scale: 2,
                    default: 0,
                },
                {
                    name: 'monthlyQuota',
                    type: 'int',
                    isNullable: false,
                },
                {
                    name: 'features',
                    type: 'jsonb',
                    default: "'{}'::jsonb",
                },
                {
                    name: 'isActive',
                    type: 'boolean',
                    default: true,
                },
                {
                    name: 'createdAt',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'user_subscriptions',
            columns: [
                {
                    name: 'id',
                    type: 'uuid',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid_generate_v4()',
                },
                {
                    name: 'userId',
                    type: 'uuid',
                    isNullable: false,
                },
                {
                    name: 'planId',
                    type: 'uuid',
                    isNullable: false,
                },
                {
                    name: 'status',
                    type: 'enum',
                    enum: ['active', 'inactive', 'cancelled', 'expired'],
                    default: "'active'",
                },
                {
                    name: 'usedQuota',
                    type: 'int',
                    default: 0,
                },
                {
                    name: 'startDate',
                    type: 'timestamp with time zone',
                    isNullable: false,
                },
                {
                    name: 'endDate',
                    type: 'timestamp with time zone',
                    isNullable: true,
                },
                {
                    name: 'autoRenew',
                    type: 'boolean',
                    default: false,
                },
                {
                    name: 'createdAt',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updatedAt',
                    type: 'timestamp with time zone',
                    default: 'CURRENT_TIMESTAMP',
                },
            ],
        }), true);
        await queryRunner.createForeignKey('user_subscriptions', new typeorm_1.TableForeignKey({
            columnNames: ['userId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'users',
            onDelete: 'CASCADE',
        }));
        await queryRunner.createForeignKey('user_subscriptions', new typeorm_1.TableForeignKey({
            columnNames: ['planId'],
            referencedColumnNames: ['id'],
            referencedTableName: 'subscription_plans',
            onDelete: 'RESTRICT',
        }));
        await queryRunner.createIndex('subscription_plans', new typeorm_1.TableIndex({ name: 'IDX_subscription_plans_plan', columnNames: ['plan'] }));
        await queryRunner.createIndex('user_subscriptions', new typeorm_1.TableIndex({ name: 'IDX_user_subscriptions_user_id', columnNames: ['userId'] }));
        await queryRunner.createIndex('user_subscriptions', new typeorm_1.TableIndex({ name: 'IDX_user_subscriptions_status', columnNames: ['status'] }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('user_subscriptions');
        await queryRunner.dropTable('subscription_plans');
        await queryRunner.query(`DROP TYPE "subscription_status"`);
        await queryRunner.query(`DROP TYPE "subscription_plan"`);
    }
}
exports.CreateSubscriptionTables1691000002000 = CreateSubscriptionTables1691000002000;
//# sourceMappingURL=1691000002000-CreateSubscriptionTables.js.map