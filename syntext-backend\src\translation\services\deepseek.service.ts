import { Injectable, Logger } from '@nestjs/common';
import { HttpService } from '@nestjs/axios';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

export interface DeepSeekTranslationRequest {
  sourceText: string;
  sourceLanguage: string;
  targetLanguage: string;
  context?: string;
}

export interface DeepSeekTranslationResponse {
  translatedText: string;
  confidence: number;
  processingTime: number;
}

@Injectable()
export class DeepSeekService {
  private readonly logger = new Logger(DeepSeekService.name);
  private readonly apiKey: string;
  private readonly apiUrl: string;

  constructor(
    private readonly httpService: HttpService,
    private readonly configService: ConfigService,
  ) {
    this.apiKey = this.configService.get<string>('DEEPSEEK_API_KEY') || '';
    this.apiUrl = this.configService.get<string>('DEEPSEEK_API_URL') || '';
  }

  async translateText(request: DeepSeekTranslationRequest): Promise<DeepSeekTranslationResponse> {
    const startTime = Date.now();
    
    try {
      const prompt = this.buildTranslationPrompt(request);
      
      const response = await firstValueFrom(
        this.httpService.post(
          `${this.apiUrl}/chat/completions`,
          {
            model: 'deepseek-chat',
            messages: [
              {
                role: 'system',
                content: '你是一个专业的翻译助手，专注于提供准确、自然的翻译结果。请直接返回翻译结果，不要添加任何解释或额外内容。',
              },
              {
                role: 'user',
                content: prompt,
              },
            ],
            temperature: 0.3,
            max_tokens: 2000,
            stream: false,
          },
          {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`,
              'Content-Type': 'application/json',
            },
            timeout: 30000, // 30秒超时
          },
        ),
      );

      const translatedText = response.data.choices[0]?.message?.content?.trim();
      
      if (!translatedText) {
        throw new Error('DeepSeek API返回了空的翻译结果');
      }

      const processingTime = Date.now() - startTime;
      
      // 简单的置信度评估
      const confidence = this.calculateConfidence(request.sourceText, translatedText);

      this.logger.log(`Translation completed in ${processingTime}ms`);

      return {
        translatedText,
        confidence,
        processingTime,
      };
    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.logger.error('DeepSeek translation failed:', error);
      throw new Error(`翻译服务暂时不可用: ${error.message}`);
    }
  }

  async batchTranslate(requests: DeepSeekTranslationRequest[]): Promise<DeepSeekTranslationResponse[]> {
    const results: DeepSeekTranslationResponse[] = [];
    
    // 并发处理批量翻译，但限制并发数量
    const batchSize = 3;
    for (let i = 0; i < requests.length; i += batchSize) {
      const batch = requests.slice(i, i + batchSize);
      const batchPromises = batch.map(request => this.translateText(request));
      
      try {
        const batchResults = await Promise.all(batchPromises);
        results.push(...batchResults);
      } catch (error) {
        this.logger.error(`Batch translation failed for batch ${i / batchSize + 1}:`, error);
        throw error;
      }
    }
    
    return results;
  }

  private buildTranslationPrompt(request: DeepSeekTranslationRequest): string {
    const { sourceText, sourceLanguage, targetLanguage, context } = request;
    
    let prompt = `请将以下${this.getLanguageName(sourceLanguage)}文本翻译成${this.getLanguageName(targetLanguage)}：\n\n${sourceText}`;
    
    if (context) {
      prompt += `\n\n上下文信息：${context}`;
    }
    
    prompt += '\n\n要求：\n1. 保持原文的语义和语调\n2. 确保翻译自然流畅\n3. 只返回翻译结果，不要添加任何解释';
    
    return prompt;
  }

  private getLanguageName(languageCode: string): string {
    const languageMap: Record<string, string> = {
      'zh': '中文',
      'en': '英文',
      'ja': '日文',
      'ko': '韩文',
      'es': '西班牙语',
      'fr': '法语',
      'de': '德语',
      'ru': '俄语',
      'it': '意大利语',
      'pt': '葡萄牙语',
      'ar': '阿拉伯语',
      'hi': '印地语',
      'auto': '自动检测',
    };
    
    return languageMap[languageCode] || languageCode;
  }

  private calculateConfidence(sourceText: string, translatedText: string): number {
    // 简单的置信度计算算法
    let confidence = 0.8; // 基础置信度
    
    // 根据文本长度调整
    const lengthRatio = translatedText.length / sourceText.length;
    if (lengthRatio > 0.3 && lengthRatio < 3) {
      confidence += 0.1;
    }
    
    // 检查是否包含明显的翻译失败标志
    const errorPatterns = ['翻译失败', 'translation failed', '无法翻译', 'error'];
    const hasError = errorPatterns.some(pattern => 
      translatedText.toLowerCase().includes(pattern.toLowerCase())
    );
    
    if (hasError) {
      confidence -= 0.3;
    }
    
    // 确保置信度在0-1范围内
    return Math.max(0, Math.min(1, confidence));
  }
}