{"name": "check-disk-space", "version": "3.4.0", "description": "Light multi-platform disk space checker without third party for Node.js", "files": ["dist", "README.md"], "main": "./dist/check-disk-space.cjs", "module": "./dist/check-disk-space.mjs", "types": "./dist/check-disk-space.d.ts", "exports": {"import": "./dist/check-disk-space.mjs", "require": "./dist/check-disk-space.cjs", "types": "./dist/check-disk-space.d.ts"}, "scripts": {"build:lib": "rollup --config", "build:dts": "rollup --config rollup.dts.config.mjs", "build": "npm-run-all build:lib build:dts", "lint": "eslint . --cache", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit", "test": "npm-run-all test:coverage lint typecheck", "test:unit": "NODE_ENV=test TS_NODE_PROJECT='tsconfig.test.json' ava", "test:coverage": "nyc --reporter=lcov --reporter=text npm run test:unit --silent"}, "ava": {"files": ["test/**", "!test/__helpers__/**"], "extensions": ["ts"], "require": ["tsconfig-paths/register", "ts-node/register", "source-map-support/register"]}, "engines": {"node": ">=16"}, "devDependencies": {"@alex-d/eslint-config": "2.2.0", "@istanbuljs/nyc-config-typescript": "1.0.2", "@rollup/plugin-typescript": "11.1.1", "@types/node": "16.11.7", "@typescript-eslint/eslint-plugin": "5.59.6", "@typescript-eslint/parser": "5.59.6", "ava": "5.2.0", "eslint": "8.41.0", "eslint-plugin-ava": "14.0.0", "eslint-plugin-import": "2.27.5", "eslint-plugin-simple-import-sort": "10.0.0", "npm-run-all": "4.1.5", "nyc": "15.1.0", "rollup": "3.22.0", "rollup-plugin-dts": "5.3.0", "source-map-support": "0.5.21", "ts-node": "10.9.1", "tsconfig-paths": "4.2.0", "tslib": "2.5.2", "typescript": "5.0.4"}, "repository": {"type": "git", "url": "git+https://github.com/Alex-D/check-disk-space.git"}, "keywords": ["disk", "space", "hdd", "free"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>> (https://alex-d.fr)", "license": "MIT", "bugs": {"url": "https://github.com/Alex-D/check-disk-space/issues"}, "homepage": "https://github.com/Alex-D/check-disk-space#readme"}