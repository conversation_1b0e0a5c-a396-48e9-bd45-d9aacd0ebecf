import React, { useState, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import { useNavigate } from 'react-router-dom';
import {
  fetchSubscriptionPlans,
  fetchCurrentSubscription,

  createSubscription,
  type SubscriptionPlan,
} from '../store/slices/subscriptionSlice';
import {
  createPayment,

  clearCurrentPayment,
} from '../store/slices/paymentSlice';
import SubscriptionPlans from '../components/SubscriptionPlans';
import CouponInput from '../components/CouponInput';
import PaymentMethods, { type PaymentMethod } from '../components/PaymentMethods';

const Subscription: React.FC = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const { currentSubscription, selectedPlan, selectedBillingCycle } = useAppSelector(
    (state) => state.subscription
  );
  const { coupon, currentPayment } = useAppSelector((state) => state.payment);

  const [step, setStep] = useState<'plans' | 'payment' | 'processing'>('plans');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod>('alipay');
  const [finalAmount, setFinalAmount] = useState(0);
  const [originalAmount, setOriginalAmount] = useState(0);

  useEffect(() => {
    dispatch(fetchSubscriptionPlans());
    dispatch(fetchCurrentSubscription());
  }, [dispatch]);

  useEffect(() => {
    if (selectedPlan) {
      const amount = selectedBillingCycle === 'yearly' 
        ? selectedPlan.yearlyPrice 
        : selectedPlan.monthlyPrice;
      setOriginalAmount(amount);
      setFinalAmount(amount);
    }
  }, [selectedPlan, selectedBillingCycle]);

  const handlePlanSelect = (plan: SubscriptionPlan, _billingCycle: 'monthly' | 'yearly') => {
    if (plan.type === 'free') {
      // 免费套餐直接跳转回翻译页面
      navigate('/translate');
      return;
    }
    setStep('payment');
  };

  const handleCouponValidation = (validation: any) => {
    if (validation?.valid && validation.finalAmount !== undefined) {
      setFinalAmount(validation.finalAmount);
    } else {
      setFinalAmount(originalAmount);
    }
  };

  const handlePayment = async () => {
    if (!selectedPlan) return;

    setStep('processing');

    try {
      // 1. 创建订阅
      const subscriptionResult = await dispatch(createSubscription({
        planType: selectedPlan.type,
        billingCycle: selectedBillingCycle,
        autoRenewal: true,
      }));

      if (createSubscription.rejected.match(subscriptionResult)) {
        throw new Error(subscriptionResult.payload as string);
      }

      const subscription = subscriptionResult.payload;

      // 2. 创建支付
      const paymentResult = await dispatch(createPayment({
        subscriptionId: subscription.id,
        amount: finalAmount,
        currency: 'CNY',
        method: selectedPaymentMethod,
        description: `${selectedPlan.name} - ${selectedBillingCycle === 'yearly' ? '年费' : '月费'}`,
        couponId: coupon.validation?.valid ? coupon.validation.couponId : undefined,
        returnUrl: `${window.location.origin}/subscription/success`,
      }));

      if (createPayment.rejected.match(paymentResult)) {
        throw new Error(paymentResult.payload as string);
      }

      const payment = paymentResult.payload;

      // 3. 跳转到支付页面
      if (payment.paymentUrl) {
        window.open(payment.paymentUrl, '_blank');
      } else if (payment.qrCode) {
        // 显示二维码支付
        // 这里可以实现二维码支付界面
      }

      // 启动支付状态轮询
      startPaymentPolling(payment.paymentId);

    } catch (error: any) {
      console.error('Payment creation failed:', error);
      setStep('payment');
      // 这里可以显示错误提示
    }
  };

  const startPaymentPolling = (paymentId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`/api/v1/payment/${paymentId}/status`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
          },
        });
        const result = await response.json();
        
        if (result.data.status === 'completed') {
          clearInterval(pollInterval);
          navigate('/subscription/success');
        } else if (result.data.status === 'failed') {
          clearInterval(pollInterval);
          setStep('payment');
          // 显示支付失败提示
        }
      } catch (error) {
        console.error('Payment status check failed:', error);
      }
    }, 3000); // 每3秒检查一次

    // 5分钟后停止轮询
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 300000);
  };

  const handleBack = () => {
    if (step === 'payment') {
      setStep('plans');
      dispatch(clearCurrentPayment());
    } else if (step === 'processing') {
      setStep('payment');
    }
  };

  const getCurrentSubscriptionDisplay = () => {
    if (!currentSubscription.data) return null;

    const sub = currentSubscription.data;
    const isActive = sub.status === 'active';
    const endDate = new Date(sub.endDate);
    const now = new Date();
    const daysLeft = Math.ceil((endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));

    return (
      <div className="mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">当前订阅</h3>
            <p className="text-sm text-gray-600 mt-1">
              {sub.plan.name} - {sub.billingCycle === 'yearly' ? '年付' : '月付'}
            </p>
            {isActive && daysLeft > 0 && (
              <p className="text-sm text-green-600 mt-2">
                还有 {daysLeft} 天到期
              </p>
            )}
          </div>
          <div className="text-right">
            <div className={`
              px-3 py-1 rounded-full text-sm font-medium
              ${isActive 
                ? 'bg-green-100 text-green-700' 
                : 'bg-red-100 text-red-700'
              }
            `}>
              {isActive ? '活跃' : '已过期'}
            </div>
            <div className="text-sm text-gray-600 mt-2">
              ¥{sub.amount} / {sub.billingCycle === 'yearly' ? '年' : '月'}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">选择订阅套餐</h1>
          <p className="text-gray-600">升级您的 SynText 体验，享受更多翻译额度和高级功能</p>
        </div>

        {/* 当前订阅显示 */}
        {getCurrentSubscriptionDisplay()}

        {/* 步骤指示器 */}
        <div className="flex items-center justify-center mb-8">
          <div className="flex items-center space-x-4">
            <div className={`
              flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
              ${step === 'plans' 
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
              }
            `}>
              1
            </div>
            <div className="w-16 h-0.5 bg-gray-200"></div>
            <div className={`
              flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
              ${step === 'payment' || step === 'processing'
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
              }
            `}>
              2
            </div>
            <div className="w-16 h-0.5 bg-gray-200"></div>
            <div className={`
              flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
              ${step === 'processing'
                ? 'bg-blue-600 text-white' 
                : 'bg-gray-200 text-gray-600'
              }
            `}>
              3
            </div>
          </div>
        </div>

        {/* 主要内容 */}
        {step === 'plans' && (
          <SubscriptionPlans onPlanSelect={handlePlanSelect} />
        )}

        {step === 'payment' && selectedPlan && (
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-semibold text-gray-900">支付信息</h2>
                <button
                  onClick={handleBack}
                  className="text-sm text-blue-600 hover:text-blue-700"
                >
                  返回选择套餐
                </button>
              </div>

              {/* 订单摘要 */}
              <div className="mb-6 p-4 bg-gray-50 rounded-lg">
                <h3 className="font-medium text-gray-900 mb-2">订单摘要</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>套餐</span>
                    <span>{selectedPlan.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>计费周期</span>
                    <span>{selectedBillingCycle === 'yearly' ? '年付' : '月付'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>原价</span>
                    <span>¥{originalAmount}</span>
                  </div>
                  {coupon.validation?.valid && (
                    <div className="flex justify-between text-green-600">
                      <span>优惠折扣</span>
                      <span>-¥{coupon.validation.discountAmount?.toFixed(2)}</span>
                    </div>
                  )}
                  <div className="border-t pt-2 flex justify-between font-medium">
                    <span>应付金额</span>
                    <span className="text-lg">¥{finalAmount.toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* 优惠码输入 */}
              <div className="mb-6">
                <CouponInput
                  originalAmount={originalAmount}
                  planType={selectedPlan.type}
                  onValidationChange={handleCouponValidation}
                />
              </div>

              {/* 支付方式选择 */}
              <div className="mb-6">
                <PaymentMethods
                  selectedMethod={selectedPaymentMethod}
                  onMethodChange={setSelectedPaymentMethod}
                />
              </div>

              {/* 支付按钮 */}
              <button
                onClick={handlePayment}
                disabled={!selectedPlan || currentPayment.loading}
                className={`
                  w-full py-3 px-4 rounded-lg text-white font-medium transition-colors
                  ${currentPayment.loading
                    ? 'bg-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 hover:bg-blue-700'
                  }
                `}
              >
                {currentPayment.loading ? '处理中...' : `支付 ¥${finalAmount.toFixed(2)}`}
              </button>
            </div>
          </div>
        )}

        {step === 'processing' && (
          <div className="max-w-md mx-auto text-center">
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">正在处理支付</h3>
              <p className="text-gray-600">请在新窗口中完成支付，支付完成后页面将自动跳转...</p>
              <button
                onClick={handleBack}
                className="mt-4 text-sm text-blue-600 hover:text-blue-700"
              >
                返回修改支付方式
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Subscription;