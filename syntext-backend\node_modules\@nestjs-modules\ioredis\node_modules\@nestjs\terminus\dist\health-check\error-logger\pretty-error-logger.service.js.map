{"version": 3, "file": "pretty-error-logger.service.js", "sourceRoot": "", "sources": ["../../../lib/health-check/error-logger/pretty-error-logger.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAC5C,+BAA+B;AAI/B,MAAM,KAAK,GAAG,iBAAiB,CAAC;AAChC,MAAM,GAAG,GAAG,iBAAiB,CAAC;AAC9B,MAAM,UAAU,GAAG,SAAS,CAAC;AAGtB,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IACpB,WAAW,CAAC,KAAa;QAC/B,IAAI,KAAK,KAAK,CAAC,EAAE;YACf,OAAO,EAAE,CAAC;SACX;QACD,OAAO,GAAG,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC;IACtC,CAAC;IAEO,qBAAqB,CAAC,MAAW,EAAE,KAAK,GAAG,CAAC;QAClD,MAAM,QAAQ,GAAa,EAAE,CAAC;QAC9B,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACjD,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,EAAE;gBAC/C,QAAQ,CAAC,IAAI,CACX,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAC9D,KAAK,EACL,KAAK,GAAG,CAAC,CACV,EAAE,CACJ,CAAC;aACH;iBAAM;gBACL,MAAM,GAAG,GAAG,CAAC,KAAa,aAAb,KAAK,uBAAL,KAAK,CAAU,QAAQ;oBAClC,CAAC,CAAE,KAAa,CAAC,QAAQ,EAAE;oBAC3B,CAAC,CAAC,KAAK,CAAC;gBACV,QAAQ,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC;aAC3D;SACF;QACD,OAAO,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEO,YAAY,CAAC,MAA6B;QAChD,IAAI,OAAO,GAAG,EAAE,CAAC;QAEjB,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE;YACjD,MAAM,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;YAElD,IAAI,KAAK,CAAC,MAAM,KAAK,IAAI,EAAE;gBACzB,OAAO;oBACL,KAAK;wBACJ,KAAa,CAAC,OAAO,EAAE;4BACtB,OAAO,EAAE,CAAC;4BACV,KAAK,EAAE,KAAK,GAAG,EAAE;yBAClB,CAAC;wBACF,UAAU;wBACV,IAAI,CAAC;aACR;YACD,IAAI,KAAK,CAAC,MAAM,KAAK,MAAM,EAAE;gBAC3B,OAAO;oBACL,GAAG;wBACF,KAAa,CAAC,OAAO,EAAE;4BACtB,OAAO,EAAE,CAAC;4BACV,KAAK,EAAE,KAAK,GAAG,EAAE;yBAClB,CAAC;wBACF,UAAU;wBACV,IAAI,CAAC;aACR;SACF;QACD,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,eAAe,CAAC,OAAe,EAAE,MAA6B;QAC5D,OAAO,GAAG,OAAO,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,EAAE,CAAC;IACtD,CAAC;CACF,CAAA;AA7DY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;GACA,iBAAiB,CA6D7B"}