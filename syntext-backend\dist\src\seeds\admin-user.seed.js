"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.seedAdminUser = seedAdminUser;
const user_entity_1 = require("../entities/user.entity");
async function seedAdminUser(dataSource) {
    const userRepository = dataSource.getRepository(user_entity_1.User);
    const existingAdmin = await userRepository.findOne({
        where: { role: user_entity_1.UserRole.ADMIN },
    });
    if (existingAdmin) {
        console.log('Admin user already exists, skipping seed...');
        return;
    }
    const adminUser = userRepository.create({
        email: '<EMAIL>',
        name: '系统管理员',
        role: user_entity_1.UserRole.ADMIN,
        status: user_entity_1.UserStatus.ACTIVE,
        translationCount: 0,
        monthlyTranslationCount: 0,
        lastLoginAt: new Date(),
        preferences: {
            defaultSourceLanguage: 'auto',
            defaultTargetLanguage: 'zh',
            theme: 'light',
        },
    });
    await userRepository.save(adminUser);
    console.log('Admin user created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Please set up authentication through the application.');
}
//# sourceMappingURL=admin-user.seed.js.map