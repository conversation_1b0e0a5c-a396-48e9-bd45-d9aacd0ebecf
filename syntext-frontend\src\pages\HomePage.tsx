import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '../hooks';
import { logout } from '../store/slices/authSlice';
import { fetchTranslationHistory } from '../store/slices/translationSlice';
import { websocketService } from '../services/websocket';
import Logo from '../components/Logo';
import { Button } from '../components/ui/Button';
import { Card, CardHeader, CardTitle, CardContent } from '../components/ui/Card';
import ResponsiveLayout, { useDeviceType } from '../components/ui/ResponsiveLayout';
import { HoverEffect, RippleEffect } from '../components/ui/PageTransition';
import { useCountAnimation, useRippleEffect } from '../hooks/useAnimations';

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { user } = useAppSelector((state) => state.auth);
  const { history, websocket } = useAppSelector((state) => state.translation);
  const deviceType = useDeviceType();

  // 动画Hooks
  const { currentValue: totalTranslations } = useCountAnimation(history.total, 1500, 500);
  const { createRipple } = useRippleEffect('rgba(255, 255, 255, 0.4)');

  useEffect(() => {
    // 获取翻译历史数据
    dispatch(fetchTranslationHistory({ page: 1, limit: 5 }));

    // 连接WebSocket
    if (!websocket.connected && !websocket.connecting) {
      websocketService.connect();
    }
  }, [dispatch, websocket.connected, websocket.connecting]);

  const handleLogout = () => {
    websocketService.disconnect();
    dispatch(logout());
    navigate('/login');
  };

  // 渲染头部组件
  const renderHeader = () => (
    <div className="bg-white shadow-sm border-b border-secondary-200">
      <div className="container-responsive">
        <div className="flex justify-between items-center py-4 lg:py-6">
          <div className="flex items-center space-x-4">
            <Logo size={deviceType === 'mobile' ? 'sm' : 'md'} />
            <div>
              <h1 className="text-xl lg:text-2xl font-bold text-secondary-900">SynText</h1>
              <p className="text-sm text-secondary-600 hidden sm:block">智能翻译平台</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 lg:space-x-6">
            {/* WebSocket状态 */}
            <div className="hidden sm:flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${
                websocket.connected ? 'bg-success-500' :
                websocket.connecting ? 'bg-warning-500' : 'bg-error-500'
              }`}></div>
              <span className="text-sm text-secondary-600">
                {websocket.connected ? '在线' : websocket.connecting ? '连接中' : '离线'}
              </span>
            </div>

            <div className="flex items-center space-x-3 lg:space-x-4">
              <div className="text-right hidden sm:block">
                <p className="text-sm font-medium text-secondary-900">{user?.name || user?.email}</p>
                <p className="text-xs text-secondary-500">{user?.role === 'premium' ? '高级用户' : '普通用户'}</p>
              </div>

              <Button
                variant="destructive"
                size={deviceType === 'mobile' ? 'sm' : 'md'}
                onClick={handleLogout}
                leftIcon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                  </svg>
                }
              >
                {deviceType === 'mobile' ? '退出' : '退出登录'}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  // 渲染欢迎区域
  const renderWelcomeSection = () => (
    <Card className="mb-6 lg:mb-8 overflow-hidden" variant="gradient">
      <div className="bg-gradient-to-r from-primary-600 to-accent-600 px-6 lg:px-8 py-8 lg:py-12">
        <div className="text-center text-white">
          <h2 className="text-2xl lg:text-3xl font-bold mb-4 animate-fade-in">欢迎使用 SynText</h2>
          <p className="text-lg lg:text-xl opacity-90 mb-6 lg:mb-8 animate-fade-in-up">
            AI 驱动的智能翻译平台，让语言不再是障碍
          </p>
          <RippleEffect
            className="inline-block"
            color="rgba(255, 255, 255, 0.4)"
          >
            <Button
              variant="secondary"
              size={deviceType === 'mobile' ? 'md' : 'lg'}
              onClick={(e) => {
                createRipple(e);
                setTimeout(() => navigate('/translate'), 200);
              }}
              className="bg-white text-primary-600 hover:bg-secondary-50 shadow-lg animate-bounce-soft hover:animate-hover-lift transition-all duration-300"
              leftIcon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
              }
            >
              立即开始翻译
            </Button>
          </RippleEffect>
        </div>
      </div>
    </Card>
  );

  return (
    <ResponsiveLayout
      header={renderHeader()}
      className="bg-gradient-to-br from-primary-50 to-accent-50"
    >
      <div className="space-y-6 lg:space-y-8 animate-fade-in-up">
        {/* 欢迎区域 */}
        {renderWelcomeSection()}

        {/* 快速操作区域 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* 快速操作 */}
          <div className="lg:col-span-2">
            <Card hover="lift" className="animate-slide-in-left">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-secondary-900">快速操作</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {/* 翻译按钮 */}
                  <HoverEffect effect="lift" className="animate-stagger-fade">
                    <Button
                      variant="ghost"
                      className="h-auto p-4 justify-start bg-primary-50 hover:bg-primary-100 text-left transition-all duration-300 hover:shadow-lg"
                      onClick={() => navigate('/translate')}
                    >
                      <div className="flex items-center w-full">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center group-hover:bg-primary-700 transition-colors">
                            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                            </svg>
                          </div>
                        </div>
                        <div className="ml-4 text-left">
                          <p className="text-sm font-medium text-secondary-900">文本翻译</p>
                          <p className="text-sm text-secondary-500">实时智能翻译</p>
                        </div>
                      </div>
                    </Button>
                  </HoverEffect>

                  {/* 历史记录按钮 */}
                  <HoverEffect effect="lift" className="animate-stagger-fade">
                    <Button
                      variant="ghost"
                      className="h-auto p-4 justify-start bg-success-50 hover:bg-success-100 text-left transition-all duration-300 hover:shadow-lg"
                      onClick={() => navigate('/history')}
                    >
                      <div className="flex items-center w-full">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-success-600 rounded-lg flex items-center justify-center group-hover:bg-success-700 transition-colors">
                            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                          </div>
                        </div>
                        <div className="ml-4 text-left">
                          <p className="text-sm font-medium text-secondary-900">翻译历史</p>
                          <p className="text-sm text-secondary-500">查看历史记录</p>
                        </div>
                      </div>
                    </Button>
                  </HoverEffect>

                  {/* 收藏夹按钮 */}
                  <HoverEffect effect="scale" className="animate-stagger-fade">
                    <Button
                      variant="ghost"
                      className="h-auto p-4 justify-start bg-warning-50 hover:bg-warning-100 text-left transition-all duration-300 hover:shadow-lg"
                    >
                      <div className="flex items-center w-full">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-warning-600 rounded-lg flex items-center justify-center group-hover:bg-warning-700 transition-colors">
                            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                          </div>
                        </div>
                        <div className="ml-4 text-left">
                          <p className="text-sm font-medium text-secondary-900">收藏夹</p>
                          <p className="text-sm text-secondary-500">收藏的翻译</p>
                        </div>
                      </div>
                    </Button>
                  </HoverEffect>

                  {/* 设置按钮 */}
                  <HoverEffect effect="glow" className="animate-stagger-fade">
                    <Button
                      variant="ghost"
                      className="h-auto p-4 justify-start bg-secondary-50 hover:bg-secondary-100 text-left transition-all duration-300 hover:shadow-lg"
                      onClick={() => navigate('/profile')}
                    >
                      <div className="flex items-center w-full">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 bg-secondary-600 rounded-lg flex items-center justify-center group-hover:bg-secondary-700 transition-colors">
                            <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            </svg>
                          </div>
                        </div>
                        <div className="ml-4 text-left">
                          <p className="text-sm font-medium text-secondary-900">个人中心</p>
                          <p className="text-sm text-secondary-500">个人设置</p>
                        </div>
                      </div>
                    </Button>
                  </HoverEffect>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* 统计和最近活动 */}
          <div className="space-y-6">
            {/* 使用统计 */}
            <Card hover="lift" className="animate-slide-in-right">
              <CardHeader>
                <CardTitle className="text-lg font-semibold text-secondary-900">使用统计</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">今日翻译</span>
                    <span className="text-lg font-semibold text-primary-600">0</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">本月翻译</span>
                    <span className="text-lg font-semibold text-success-600">0</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-secondary-600">总计翻译</span>
                    <span className="text-lg font-semibold text-accent-600 animate-pulse-soft">{totalTranslations}</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* 最近翻译 */}
            <Card hover="lift" className="animate-slide-in-right" style={{ animationDelay: '0.1s' }}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-lg font-semibold text-secondary-900">最近翻译</CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate('/history')}
                    className="text-primary-600 hover:text-primary-800"
                  >
                    查看全部
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {history.translations.slice(0, 3).map((translation) => (
                    <div key={translation.id} className="p-3 bg-secondary-50 rounded-lg hover:bg-secondary-100 transition-colors">
                      <p className="text-sm text-secondary-900 truncate mb-1">
                        {translation.sourceText}
                      </p>
                      <p className="text-xs text-secondary-500">
                        {translation.sourceLanguage} → {translation.targetLanguage}
                      </p>
                    </div>
                  ))}
                  {history.translations.length === 0 && (
                    <div className="text-center py-8">
                      <svg className="w-12 h-12 text-secondary-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <p className="text-sm text-secondary-500">暂无翻译记录</p>
                      <Button
                        variant="outline"
                        size="sm"
                        className="mt-3"
                        onClick={() => navigate('/translate')}
                      >
                        开始翻译
                      </Button>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </ResponsiveLayout>
  );
};

export default HomePage;