import { BillingService } from '../services/billing.service';
export declare class BillingController {
    private readonly billingService;
    constructor(billingService: BillingService);
    getBillingSummary(startDate?: string, endDate?: string): Promise<{
        code: number;
        message: string;
        data: import("../services/billing.service").BillingSummary;
    }>;
    getUserBilling(req: any): Promise<{
        code: number;
        message: string;
        data: import("../services/billing.service").UserBilling;
    }>;
    getSpecificUserBilling(userId: string): Promise<{
        code: number;
        message: string;
        data: import("../services/billing.service").UserBilling;
    }>;
    generateInvoice(paymentId: string): Promise<{
        code: number;
        message: string;
        data: any;
    }>;
    getRevenueAnalytics(days?: string): Promise<{
        code: number;
        message: string;
        data: any[];
    }>;
    getTopCustomers(limit?: string): Promise<{
        code: number;
        message: string;
        data: any[];
    }>;
    getPaymentMethodStats(): Promise<{
        code: number;
        message: string;
        data: any[];
    }>;
    exportBillingData(startDate: string, endDate: string): Promise<{
        code: number;
        message: string;
        data: import("../../entities").Payment[];
    }>;
}
