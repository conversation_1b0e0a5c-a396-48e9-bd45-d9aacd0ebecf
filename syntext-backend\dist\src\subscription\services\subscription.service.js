"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var SubscriptionService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.SubscriptionService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const user_subscription_entity_1 = require("../../entities/user-subscription.entity");
const user_entity_1 = require("../../entities/user.entity");
const subscription_plan_service_1 = require("./subscription-plan.service");
let SubscriptionService = SubscriptionService_1 = class SubscriptionService {
    userSubscriptionRepository;
    userRepository;
    subscriptionPlanService;
    logger = new common_1.Logger(SubscriptionService_1.name);
    constructor(userSubscriptionRepository, userRepository, subscriptionPlanService) {
        this.userSubscriptionRepository = userSubscriptionRepository;
        this.userRepository = userRepository;
        this.subscriptionPlanService = subscriptionPlanService;
    }
    async getUserSubscription(userId) {
        return this.userSubscriptionRepository.findOne({
            where: { userId, status: user_subscription_entity_1.SubscriptionStatus.ACTIVE },
            relations: ['plan'],
            order: { createdAt: 'DESC' },
        });
    }
    async createSubscription(createDto) {
        const user = await this.userRepository.findOne({
            where: { id: createDto.userId },
        });
        if (!user) {
            throw new common_1.NotFoundException('用户不存在');
        }
        const plan = await this.subscriptionPlanService.getPlanByType(createDto.planType);
        if (!plan) {
            throw new common_1.NotFoundException('订阅套餐不存在');
        }
        const existingSubscription = await this.getUserSubscription(createDto.userId);
        if (existingSubscription) {
            throw new common_1.BadRequestException('用户已有活跃订阅');
        }
        const now = new Date();
        const amount = createDto.billingCycle === user_subscription_entity_1.BillingCycle.YEARLY
            ? plan.yearlyPrice
            : plan.monthlyPrice;
        const endDate = new Date();
        if (createDto.billingCycle === user_subscription_entity_1.BillingCycle.YEARLY) {
            endDate.setFullYear(endDate.getFullYear() + 1);
        }
        else {
            endDate.setMonth(endDate.getMonth() + 1);
        }
        const nextBillingDate = createDto.autoRenewal ? new Date(endDate) : new Date(endDate);
        const subscription = this.userSubscriptionRepository.create({
            userId: createDto.userId,
            planId: plan.id,
            status: user_subscription_entity_1.SubscriptionStatus.PENDING,
            billingCycle: createDto.billingCycle,
            amount,
            startDate: now,
            endDate,
            nextBillingDate,
            autoRenewal: createDto.autoRenewal || false,
            monthlyCharactersUsed: 0,
            dailyCharactersUsed: 0,
            dailyTranslationsUsed: 0,
            lastUsageResetDate: new Date(),
        });
        const savedSubscription = await this.userSubscriptionRepository.save(subscription);
        this.logger.log(`Created subscription ${savedSubscription.id} for user ${createDto.userId}`);
        const result = await this.userSubscriptionRepository.findOne({
            where: { id: savedSubscription.id },
            relations: ['plan'],
        });
        return result;
    }
    async activateSubscription(subscriptionId) {
        const subscription = await this.userSubscriptionRepository.findOne({
            where: { id: subscriptionId },
            relations: ['plan'],
        });
        if (!subscription) {
            throw new common_1.NotFoundException('订阅不存在');
        }
        subscription.status = user_subscription_entity_1.SubscriptionStatus.ACTIVE;
        await this.userSubscriptionRepository.save(subscription);
        this.logger.log(`Activated subscription ${subscriptionId}`);
        return subscription;
    }
    async cancelSubscription(userId, reason) {
        const subscription = await this.getUserSubscription(userId);
        if (!subscription) {
            throw new common_1.NotFoundException('未找到活跃订阅');
        }
        subscription.status = user_subscription_entity_1.SubscriptionStatus.CANCELLED;
        subscription.autoRenewal = false;
        if (reason) {
            subscription.metadata = {
                ...subscription.metadata,
                cancellationReason: reason,
                cancelledAt: new Date(),
            };
        }
        await this.userSubscriptionRepository.save(subscription);
        this.logger.log(`Cancelled subscription ${subscription.id} for user ${userId}`);
    }
    async renewSubscription(userId) {
        const currentSubscription = await this.getUserSubscription(userId);
        if (!currentSubscription) {
            throw new common_1.NotFoundException('未找到活跃订阅');
        }
        const plan = await this.subscriptionPlanService.getPlanById(currentSubscription.planId);
        if (!plan) {
            throw new common_1.NotFoundException('订阅套餐不存在');
        }
        currentSubscription.status = user_subscription_entity_1.SubscriptionStatus.EXPIRED;
        await this.userSubscriptionRepository.save(currentSubscription);
        const newEndDate = new Date();
        if (currentSubscription.billingCycle === user_subscription_entity_1.BillingCycle.YEARLY) {
            newEndDate.setFullYear(newEndDate.getFullYear() + 1);
        }
        else {
            newEndDate.setMonth(newEndDate.getMonth() + 1);
        }
        const renewedSubscription = this.userSubscriptionRepository.create({
            userId,
            planId: plan.id,
            status: user_subscription_entity_1.SubscriptionStatus.ACTIVE,
            billingCycle: currentSubscription.billingCycle,
            amount: currentSubscription.amount,
            startDate: new Date(),
            endDate: newEndDate,
            nextBillingDate: currentSubscription.autoRenewal ? new Date(newEndDate) : new Date(newEndDate),
            autoRenewal: currentSubscription.autoRenewal,
            monthlyCharactersUsed: 0,
            dailyCharactersUsed: 0,
            dailyTranslationsUsed: 0,
            lastUsageResetDate: new Date(),
        });
        const saved = await this.userSubscriptionRepository.save(renewedSubscription);
        this.logger.log(`Renewed subscription for user ${userId}`);
        const result = await this.userSubscriptionRepository.findOne({
            where: { id: saved.id },
            relations: ['plan'],
        });
        return result;
    }
    async checkAndExpireSubscriptions() {
        const expiredSubscriptions = await this.userSubscriptionRepository.find({
            where: {
                status: user_subscription_entity_1.SubscriptionStatus.ACTIVE,
                endDate: new Date(),
            },
        });
        for (const subscription of expiredSubscriptions) {
            if (subscription.autoRenewal) {
                try {
                    await this.renewSubscription(subscription.userId);
                }
                catch (error) {
                    this.logger.error(`Failed to auto-renew subscription ${subscription.id}:`, error);
                    subscription.status = user_subscription_entity_1.SubscriptionStatus.EXPIRED;
                    await this.userSubscriptionRepository.save(subscription);
                }
            }
            else {
                subscription.status = user_subscription_entity_1.SubscriptionStatus.EXPIRED;
                await this.userSubscriptionRepository.save(subscription);
            }
        }
        if (expiredSubscriptions.length > 0) {
            this.logger.log(`Processed ${expiredSubscriptions.length} expired subscriptions`);
        }
    }
    async getSubscriptionHistory(userId) {
        return this.userSubscriptionRepository.find({
            where: { userId },
            relations: ['plan'],
            order: { createdAt: 'DESC' },
        });
    }
};
exports.SubscriptionService = SubscriptionService;
exports.SubscriptionService = SubscriptionService = SubscriptionService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(user_subscription_entity_1.UserSubscription)),
    __param(1, (0, typeorm_1.InjectRepository)(user_entity_1.User)),
    __metadata("design:paramtypes", [typeorm_2.Repository,
        typeorm_2.Repository,
        subscription_plan_service_1.SubscriptionPlanService])
], SubscriptionService);
//# sourceMappingURL=subscription.service.js.map