import React from 'react';

export type PaymentMethod = 'alipay' | 'wechat_pay' | 'credit_card' | 'paypal';

interface PaymentMethodsProps {
  selectedMethod: PaymentMethod;
  onMethodChange: (method: PaymentMethod) => void;
  disabled?: boolean;
}

export const PaymentMethods: React.FC<PaymentMethodsProps> = ({
  selectedMethod,
  onMethodChange,
  disabled = false,
}) => {
  const paymentMethods = [
    {
      id: 'alipay' as PaymentMethod,
      name: '支付宝',
      icon: '💙',
      description: '使用支付宝扫码支付',
      popular: true,
    },
    {
      id: 'wechat_pay' as PaymentMethod,
      name: '微信支付',
      icon: '💚',
      description: '使用微信扫码支付',
      popular: true,
    },
    {
      id: 'credit_card' as PaymentMethod,
      name: '信用卡',
      icon: '💳',
      description: '支持 Visa、MasterCard 等',
      popular: false,
    },
    {
      id: 'paypal' as PaymentMethod,
      name: 'PayPal',
      icon: '🌐',
      description: '使用 PayPal 账户支付',
      popular: false,
    },
  ];

  return (
    <div className="space-y-3">
      <h3 className="text-lg font-medium text-gray-900 mb-4">选择支付方式</h3>
      
      <div className="grid grid-cols-1 gap-3">
        {paymentMethods.map((method) => (
          <label
            key={method.id}
            className={`
              relative flex items-center p-4 border rounded-lg cursor-pointer transition-all
              ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:border-blue-300 hover:bg-blue-50'}
              ${selectedMethod === method.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 bg-white'
              }
            `}
          >
            <input
              type="radio"
              name="paymentMethod"
              value={method.id}
              checked={selectedMethod === method.id}
              onChange={(e) => onMethodChange(e.target.value as PaymentMethod)}
              disabled={disabled}
              className="sr-only"
            />
            
            {/* 选择指示器 */}
            <div className={`
              flex-shrink-0 w-4 h-4 border-2 rounded-full mr-4 transition-colors
              ${selectedMethod === method.id
                ? 'border-blue-500 bg-blue-500'
                : 'border-gray-300'
              }
            `}>
              {selectedMethod === method.id && (
                <div className="w-full h-full rounded-full bg-white transform scale-50"></div>
              )}
            </div>

            {/* 支付方式图标 */}
            <div className="flex-shrink-0 w-8 h-8 mr-4 text-2xl flex items-center justify-center">
              {method.icon}
            </div>

            {/* 支付方式信息 */}
            <div className="flex-1">
              <div className="flex items-center space-x-2">
                <span className="font-medium text-gray-900">{method.name}</span>
                {method.popular && (
                  <span className="px-2 py-1 text-xs bg-orange-100 text-orange-600 rounded-full">
                    推荐
                  </span>
                )}
              </div>
              <p className="text-sm text-gray-600 mt-1">{method.description}</p>
            </div>

            {/* 选中状态指示 */}
            {selectedMethod === method.id && (
              <div className="flex-shrink-0 ml-4">
                <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </div>
            )}
          </label>
        ))}
      </div>

      {/* 安全提示 */}
      <div className="mt-4 p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V6a4 4 0 00-8 0v3h8z" />
          </svg>
          <span>所有支付信息均采用 SSL 加密保护，确保您的资金安全</span>
        </div>
      </div>
    </div>
  );
};

export default PaymentMethods;