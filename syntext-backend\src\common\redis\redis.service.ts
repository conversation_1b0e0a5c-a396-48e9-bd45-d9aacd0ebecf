import { Injectable, Inject } from '@nestjs/common';
import { Redis } from 'ioredis';

@Injectable()
export class RedisService {
  constructor(@Inject('REDIS_CLIENT') private readonly redis: Redis) {}

  async get(key: string): Promise<string | null> {
    return this.redis.get(key);
  }

  async set(key: string, value: string, ttl?: number): Promise<void> {
    if (ttl) {
      await this.redis.setex(key, ttl, value);
    } else {
      await this.redis.set(key, value);
    }
  }

  async del(key: string): Promise<void> {
    await this.redis.del(key);
  }

  async exists(key: string): Promise<boolean> {
    const result = await this.redis.exists(key);
    return result === 1;
  }

  async expire(key: string, ttl: number): Promise<void> {
    await this.redis.expire(key, ttl);
  }

  async ttl(key: string): Promise<number> {
    return this.redis.ttl(key);
  }

  async hget(key: string, field: string): Promise<string | null> {
    return this.redis.hget(key, field);
  }

  async hset(key: string, field: string, value: string): Promise<void> {
    await this.redis.hset(key, field, value);
  }

  async hgetall(key: string): Promise<Record<string, string>> {
    return this.redis.hgetall(key);
  }

  async hdel(key: string, field: string): Promise<void> {
    await this.redis.hdel(key, field);
  }

  async sadd(key: string, member: string): Promise<void> {
    await this.redis.sadd(key, member);
  }

  async srem(key: string, member: string): Promise<void> {
    await this.redis.srem(key, member);
  }

  async smembers(key: string): Promise<string[]> {
    return this.redis.smembers(key);
  }

  async sismember(key: string, member: string): Promise<boolean> {
    const result = await this.redis.sismember(key, member);
    return result === 1;
  }

  // Translation caching methods
  async cacheTranslation(
    sourceText: string,
    sourceLang: string,
    targetLang: string,
    translatedText: string,
    ttl: number = 3600,
  ): Promise<void> {
    const key = this.getTranslationCacheKey(sourceText, sourceLang, targetLang);
    await this.set(key, translatedText, ttl);
  }

  async getCachedTranslation(
    sourceText: string,
    sourceLang: string,
    targetLang: string,
  ): Promise<string | null> {
    const key = this.getTranslationCacheKey(sourceText, sourceLang, targetLang);
    return this.get(key);
  }

  private getTranslationCacheKey(
    sourceText: string,
    sourceLang: string,
    targetLang: string,
  ): string {
    const hash = Buffer.from(sourceText).toString('base64').slice(0, 32);
    return `translation:${sourceLang}:${targetLang}:${hash}`;
  }

  // Rate limiting methods
  async incrementRateLimit(
    identifier: string,
    windowSize: number = 60,
    limit: number = 100,
  ): Promise<{ count: number; remaining: number; resetTime: number }> {
    const key = `rate_limit:${identifier}`;
    const now = Math.floor(Date.now() / 1000);
    const windowStart = now - (now % windowSize);
    const windowKey = `${key}:${windowStart}`;

    const count = await this.redis.incr(windowKey);
    if (count === 1) {
      await this.redis.expire(windowKey, windowSize);
    }

    const remaining = Math.max(0, limit - count);
    const resetTime = windowStart + windowSize;

    return { count, remaining, resetTime };
  }

  async checkRateLimit(
    identifier: string,
    windowSize: number = 60,
  ): Promise<number> {
    const key = `rate_limit:${identifier}`;
    const now = Math.floor(Date.now() / 1000);
    const windowStart = now - (now % windowSize);
    const windowKey = `${key}:${windowStart}`;

    const count = await this.redis.get(windowKey);
    return count ? parseInt(count, 10) : 0;
  }

  // Session management
  async setSession(sessionId: string, data: any, ttl: number = 86400): Promise<void> {
    const key = `session:${sessionId}`;
    await this.set(key, JSON.stringify(data), ttl);
  }

  async getSession(sessionId: string): Promise<any | null> {
    const key = `session:${sessionId}`;
    const data = await this.get(key);
    return data ? JSON.parse(data) : null;
  }

  async deleteSession(sessionId: string): Promise<void> {
    const key = `session:${sessionId}`;
    await this.del(key);
  }

  // Email verification codes
  async setVerificationCode(
    email: string,
    code: string,
    ttl: number = 300,
  ): Promise<void> {
    const key = `verification:${email}`;
    await this.set(key, code, ttl);
  }

  async getVerificationCode(email: string): Promise<string | null> {
    const key = `verification:${email}`;
    return this.get(key);
  }

  async deleteVerificationCode(email: string): Promise<void> {
    const key = `verification:${email}`;
    await this.del(key);
  }

  // User usage tracking
  async incrementUserUsage(
    userId: string,
    type: 'translation' | 'api_call',
    period: 'daily' | 'monthly' = 'monthly',
  ): Promise<number> {
    const now = new Date();
    const key = period === 'monthly'
      ? `usage:${type}:${userId}:${now.getFullYear()}-${now.getMonth() + 1}`
      : `usage:${type}:${userId}:${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}`;

    const count = await this.redis.incr(key);

    // Set expiration for the key
    const ttl = period === 'monthly' ? 86400 * 32 : 86400 * 2; // 32 days for monthly, 2 days for daily
    if (count === 1) {
      await this.redis.expire(key, ttl);
    }

    return count;
  }

  async getUserUsage(
    userId: string,
    type: 'translation' | 'api_call',
    period: 'daily' | 'monthly' = 'monthly',
  ): Promise<number> {
    const now = new Date();
    const key = period === 'monthly'
      ? `usage:${type}:${userId}:${now.getFullYear()}-${now.getMonth() + 1}`
      : `usage:${type}:${userId}:${now.getFullYear()}-${now.getMonth() + 1}-${now.getDate()}`;

    const count = await this.get(key);
    return count ? parseInt(count, 10) : 0;
  }
}