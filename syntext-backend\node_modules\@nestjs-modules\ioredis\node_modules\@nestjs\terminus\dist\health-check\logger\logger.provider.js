"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLoggerProvider = exports.TERMINUS_LOGGER = void 0;
const default_logger_service_1 = require("./default-logger.service");
exports.TERMINUS_LOGGER = 'TERMINUS_LOGGER';
function getLoggerProvider(logger) {
    if (logger === true || logger === undefined) {
        return {
            provide: exports.TERMINUS_LOGGER,
            useClass: default_logger_service_1.DefaultTerminusLogger,
        };
    }
    if (logger === false) {
        return {
            provide: exports.TERMINUS_LOGGER,
            useValue: {
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                log: () => { },
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                error: () => { },
                // eslint-disable-next-line @typescript-eslint/no-empty-function
                warn: () => { },
            },
        };
    }
    return {
        provide: exports.TERMINUS_LOGGER,
        useClass: logger,
    };
}
exports.getLoggerProvider = getLoggerProvider;
//# sourceMappingURL=logger.provider.js.map