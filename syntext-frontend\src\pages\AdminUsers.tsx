import React, { useEffect, useState } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  role: 'user' | 'admin' | 'premium';
  status: 'active' | 'inactive' | 'banned';
  translationCount: number;
  createdAt: string;
  lastLoginAt: string;
  subscription?: {
    planType: string;
    status: string;
  };
}

interface UsersQuery {
  page: number;
  limit: number;
  search: string;
  role: string;
  status: string;
}

const AdminUsers: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [query, setQuery] = useState<UsersQuery>({
    page: 1,
    limit: 20,
    search: '',
    role: '',
    status: '',
  });
  const [pagination, setPagination] = useState({
    total: 0,
    totalPages: 0,
  });


  useEffect(() => {
    fetchUsers();
  }, [query.page, query.role, query.status]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      // TODO: 调用实际API
      // const response = await adminApi.getUsers(query);
      // setUsers(response.data);
      // setPagination(response.meta.pagination);
      
      // 模拟数据
      setTimeout(() => {
        const mockUsers: User[] = [
          {
            id: '1',
            email: '<EMAIL>',
            name: '张三',
            role: 'user',
            status: 'active',
            translationCount: 150,
            createdAt: '2025-08-01T10:00:00Z',
            lastLoginAt: '2025-08-03T09:00:00Z',
            subscription: {
              planType: 'premium',
              status: 'active',
            },
          },
          {
            id: '2',
            email: '<EMAIL>',
            name: '李四',
            role: 'premium',
            status: 'active',
            translationCount: 500,
            createdAt: '2025-07-15T10:00:00Z',
            lastLoginAt: '2025-08-02T14:30:00Z',
          },
          {
            id: '3',
            email: '<EMAIL>',
            name: '管理员',
            role: 'admin',
            status: 'active',
            translationCount: 0,
            createdAt: '2025-06-01T10:00:00Z',
            lastLoginAt: '2025-08-03T08:00:00Z',
          },
        ];
        setUsers(mockUsers);
        setPagination({ total: 3, totalPages: 1 });
        setLoading(false);
      }, 1000);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      setLoading(false);
    }
  };

  const handleSearch = () => {
    setQuery({ ...query, page: 1 });
    fetchUsers();
  };

  const handleRoleChange = (role: string) => {
    setQuery({ ...query, role, page: 1 });
  };

  const handleStatusChange = (status: string) => {
    setQuery({ ...query, status, page: 1 });
  };

  const handleEditUser = (_user: User) => {
    // TODO: 实现编辑用户功能
    console.log('编辑用户功能待实现');
  };

  const handleToggleStatus = async (_user: User) => {
    try {
      // TODO: 调用实际API
      // await adminApi.toggleUserStatus(user.id);
      fetchUsers();
    } catch (error) {
      console.error('切换用户状态失败:', error);
    }
  };

  const getRoleBadge = (role: string) => {
    const colors = {
      user: 'bg-gray-100 text-gray-800',
      premium: 'bg-yellow-100 text-yellow-800',
      admin: 'bg-red-100 text-red-800',
    };
    const labels = {
      user: '普通用户',
      premium: '高级用户',
      admin: '管理员',
    };
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[role as keyof typeof colors]}`}>
        {labels[role as keyof typeof labels]}
      </span>
    );
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      active: 'bg-green-100 text-green-800',
      inactive: 'bg-gray-100 text-gray-800',
      banned: 'bg-red-100 text-red-800',
    };
    const labels = {
      active: '活跃',
      inactive: '非活跃',
      banned: '已禁用',
    };
    return (
      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${colors[status as keyof typeof colors]}`}>
        {labels[status as keyof typeof labels]}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">用户管理</h1>
        <p className="mt-2 text-sm text-gray-600">管理系统中的所有用户</p>
      </div>

      {/* 搜索和筛选 */}
      <div className="bg-white shadow rounded-lg p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">搜索用户</label>
            <div className="flex">
              <input
                type="text"
                className="flex-1 block w-full px-3 py-2 border border-gray-300 rounded-l-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                placeholder="邮箱或姓名"
                value={query.search}
                onChange={(e) => setQuery({ ...query, search: e.target.value })}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
              />
              <button
                onClick={handleSearch}
                className="px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100"
              >
                <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </button>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">角色</label>
            <select
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              value={query.role}
              onChange={(e) => handleRoleChange(e.target.value)}
            >
              <option value="">全部角色</option>
              <option value="user">普通用户</option>
              <option value="premium">高级用户</option>
              <option value="admin">管理员</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">状态</label>
            <select
              className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              value={query.status}
              onChange={(e) => handleStatusChange(e.target.value)}
            >
              <option value="">全部状态</option>
              <option value="active">活跃</option>
              <option value="inactive">非活跃</option>
              <option value="banned">已禁用</option>
            </select>
          </div>

          <div className="flex items-end">
            <button
              onClick={() => window.location.reload()}
              className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              重置筛选
            </button>
          </div>
        </div>
      </div>

      {/* 用户列表 */}
      <div className="bg-white shadow overflow-hidden sm:rounded-lg">
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"></div>
          </div>
        ) : (
          <>
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    用户信息
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    角色/状态
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    翻译次数
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    注册时间
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    最后登录
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    操作
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {user.name?.charAt(0) || user.email.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{user.name || '未设置'}</div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="space-y-1">
                        {getRoleBadge(user.role)}
                        {getStatusBadge(user.status)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {user.translationCount.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(user.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(user.lastLoginAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex justify-end space-x-2">
                        <button
                          onClick={() => handleEditUser(user)}
                          className="text-primary-600 hover:text-primary-900"
                        >
                          编辑
                        </button>
                        <button
                          onClick={() => handleToggleStatus(user)}
                          className={`${
                            user.status === 'active' ? 'text-red-600 hover:text-red-900' : 'text-green-600 hover:text-green-900'
                          }`}
                        >
                          {user.status === 'active' ? '禁用' : '启用'}
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {/* 分页 */}
            <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div className="flex-1 flex justify-between sm:hidden">
                <button
                  disabled={query.page === 1}
                  className="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  上一页
                </button>
                <button
                  disabled={query.page === pagination.totalPages}
                  className="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50"
                >
                  下一页
                </button>
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700">
                    显示第 <span className="font-medium">{(query.page - 1) * query.limit + 1}</span> 到{' '}
                    <span className="font-medium">
                      {Math.min(query.page * query.limit, pagination.total)}
                    </span>{' '}
                    条，共 <span className="font-medium">{pagination.total}</span> 条记录
                  </p>
                </div>
                <div>
                  <nav className="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      disabled={query.page === 1}
                      onClick={() => setQuery({ ...query, page: query.page - 1 })}
                      className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      上一页
                    </button>
                    <button
                      disabled={query.page === pagination.totalPages}
                      onClick={() => setQuery({ ...query, page: query.page + 1 })}
                      className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50"
                    >
                      下一页
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default AdminUsers;