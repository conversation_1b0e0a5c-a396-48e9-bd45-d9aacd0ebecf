import { ModuleRef } from '@nestjs/core';
import { HealthIndicator, type HealthIndicatorResult } from '../';
export interface TypeOrmPingCheckSettings {
    /**
     * The connection which the ping check should get executed
     */
    connection?: any;
    /**
     * The amount of time the check should require in ms
     */
    timeout?: number;
}
/**
 * The TypeOrmHealthIndicator contains health indicators
 * which are used for health checks related to TypeOrm
 *
 * @publicApi
 * @module TerminusModule
 */
export declare class TypeOrmHealthIndicator extends HealthIndicator {
    private moduleRef;
    /**
     * Initializes the TypeOrmHealthIndicator
     *
     * @param {ModuleRef} moduleRef The NestJS module reference
     */
    constructor(moduleRef: ModuleRef);
    /**
     * Checks if the dependant packages are present
     */
    private checkDependantPackages;
    /**
     * Returns the connection of the current DI context
     */
    private getContextConnection;
    private checkMongoDBConnection;
    /**
     * Pings a typeorm connection
     *
     * @param connection The connection which the ping should get executed
     * @param timeout The timeout how long the ping should maximum take
     *
     */
    private pingDb;
    /**
     * Checks if responds in (default) 1000ms and
     * returns a result object corresponding to the result
     * @param key The key which will be used for the result object
     * @param options The options for the ping
     *
     * @example
     * typeOrmHealthIndicator.pingCheck('database', { timeout: 1500 });
     */
    pingCheck(key: string, options?: TypeOrmPingCheckSettings): Promise<HealthIndicatorResult>;
}
