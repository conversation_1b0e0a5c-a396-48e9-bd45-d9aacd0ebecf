import { SubscriptionService } from '../services/subscription.service';
import { SubscriptionPlanService } from '../services/subscription-plan.service';
import { UsageTrackingService } from '../services/usage-tracking.service';
import { BillingCycle } from '../../entities/user-subscription.entity';
import { PlanType } from '../../entities/subscription-plan.entity';
export declare class SubscriptionController {
    private readonly subscriptionService;
    private readonly subscriptionPlanService;
    private readonly usageTrackingService;
    constructor(subscriptionService: SubscriptionService, subscriptionPlanService: SubscriptionPlanService, usageTrackingService: UsageTrackingService);
    getPlans(): Promise<{
        code: number;
        message: string;
        data: import("../../entities/subscription-plan.entity").SubscriptionPlan[];
    }>;
    getCurrentSubscription(req: any): Promise<{
        code: number;
        message: string;
        data: import("../../entities/user-subscription.entity").UserSubscription;
    }>;
    createSubscription(req: any, body: {
        planType: PlanType;
        billingCycle: BillingCycle;
        autoRenewal?: boolean;
    }): Promise<{
        code: number;
        message: string;
        data: import("../../entities/user-subscription.entity").UserSubscription;
    }>;
    activateSubscription(subscriptionId: string): Promise<{
        code: number;
        message: string;
        data: import("../../entities/user-subscription.entity").UserSubscription;
    }>;
    cancelSubscription(req: any, body: {
        reason?: string;
    }): Promise<{
        code: number;
        message: string;
    }>;
    renewSubscription(req: any): Promise<{
        code: number;
        message: string;
        data: import("../../entities/user-subscription.entity").UserSubscription;
    }>;
    getSubscriptionHistory(req: any): Promise<{
        code: number;
        message: string;
        data: import("../../entities/user-subscription.entity").UserSubscription[];
    }>;
    getUserUsage(req: any): Promise<{
        code: number;
        message: string;
        data: import("../services/usage-tracking.service").UsageStats;
    }>;
    getUsageHistory(req: any, body?: {
        days?: number;
    }): Promise<{
        code: number;
        message: string;
        data: any[];
    }>;
}
