{"version": 3, "file": "translation.gateway.js", "sourceRoot": "", "sources": ["../../../../src/translation/gateways/translation.gateway.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,mDAQ4B;AAC5B,2CAAmD;AACnD,yCAA2C;AAC3C,qCAAyC;AACzC,yEAAqE;AACrE,wDAAoD;AAc7C,IAAM,kBAAkB,0BAAxB,MAAM,kBAAkB;IAQV;IACA;IAPnB,MAAM,CAAS;IAEE,MAAM,GAAG,IAAI,eAAM,CAAC,oBAAkB,CAAC,IAAI,CAAC,CAAC;IACtD,cAAc,GAAG,IAAI,GAAG,EAA+B,CAAC;IAEhE,YACmB,UAAsB,EACtB,kBAAsC;QADtC,eAAU,GAAV,UAAU,CAAY;QACtB,uBAAkB,GAAlB,kBAAkB,CAAoB;IACtD,CAAC;IAEJ,KAAK,CAAC,gBAAgB,CAAC,MAA2B;QAChD,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,OAAO,EAAE,aAAa,EAAE,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;YAE9G,IAAI,CAAC,KAAK,EAAE,CAAC;gBACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,MAAM,CAAC,EAAE,0BAA0B,CAAC,CAAC;gBAChE,MAAM,CAAC,UAAU,EAAE,CAAC;gBACpB,OAAO;YACT,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YAC9C,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,GAAG,CAAC;YAC5B,MAAM,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC;YAEjC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,MAAO,EAAE,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,SAAS,0BAA0B,CAAC,CAAC;YAEpE,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE;gBACvB,OAAO,EAAE,MAAM;gBACf,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,oCAAoC,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YAC3E,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,EAAE,CAAC;QACtB,CAAC;IACH,CAAC;IAED,gBAAgB,CAAC,MAA2B;QAC1C,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YAC1C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,MAAM,CAAC,SAAS,eAAe,CAAC,CAAC;QAC3D,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,sBAAsB,CACX,IAAkB,EACd,MAA2B;QAE9C,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YACvD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,iCAAiC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAGlE,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,OAAO,EAAE,SAAS;gBAClB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,cAAc,EAAE,IAAI,CAAC,cAAc;gBACnC,cAAc,EAAE,IAAI,CAAC,cAAc;aACpC,CAAC,CAAC;YAGH,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,QAAQ,EAAE,EAAE;gBACZ,OAAO,EAAE,WAAW;aACrB,CAAC,CAAC;YAEH,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;oBAClC,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,aAAa;iBACvB,CAAC,CAAC;YACL,CAAC,EAAE,GAAG,CAAC,CAAC;YAER,UAAU,CAAC,GAAG,EAAE;gBACd,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;oBAClC,QAAQ,EAAE,EAAE;oBACZ,OAAO,EAAE,aAAa;iBACvB,CAAC,CAAC;YACL,CAAC,EAAE,IAAI,CAAC,CAAC;YAGT,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;gBACzD,GAAG,IAAI;gBACP,MAAM,EAAE,MAAM,CAAC,MAAM;aACtB,CAAC,CAAC;YAGH,MAAM,CAAC,IAAI,CAAC,sBAAsB,EAAE;gBAClC,QAAQ,EAAE,GAAG;gBACb,OAAO,EAAE,MAAM;gBACf,MAAM;aACP,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,kCAAkC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QACrE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,MAAM;aACjC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,sBAAsB,CACX,IAAsC,EAClC,MAA2B;QAE9C,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YACvD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;YAExE,MAAM,iBAAiB,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;YACnD,IAAI,qBAAqB,GAAG,CAAC,CAAC;YAE9B,MAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;gBACrC,OAAO,EAAE,WAAW;gBACpB,KAAK,EAAE,iBAAiB;aACzB,CAAC,CAAC;YAEH,MAAM,OAAO,GAAU,EAAE,CAAC;YAE1B,KAAK,MAAM,WAAW,IAAI,IAAI,CAAC,YAAY,EAAE,CAAC;gBAC5C,IAAI,CAAC;oBACH,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;wBACxC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,qBAAqB,GAAG,iBAAiB,CAAC,GAAG,GAAG,CAAC;wBACvE,SAAS,EAAE,qBAAqB;wBAChC,KAAK,EAAE,iBAAiB;wBACxB,OAAO,EAAE,WAAW,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK;qBACzD,CAAC,CAAC;oBAEH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,aAAa,CAAC;wBACzD,GAAG,WAAW;wBACd,MAAM,EAAE,MAAM,CAAC,MAAM;qBACtB,CAAC,CAAC;oBAEH,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACrB,qBAAqB,EAAE,CAAC;gBAE1B,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;oBAC3D,OAAO,CAAC,IAAI,CAAC;wBACX,KAAK,EAAE,KAAK,CAAC,OAAO;wBACpB,UAAU,EAAE,WAAW,CAAC,UAAU;qBACnC,CAAC,CAAC;oBACH,qBAAqB,EAAE,CAAC;gBAC1B,CAAC;YACH,CAAC;YAED,MAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE;gBACxC,OAAO,EAAE,QAAQ;gBACjB,OAAO;gBACP,KAAK,EAAE,iBAAiB;gBACxB,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAE,CAAS,CAAC,KAAK,CAAC,CAAC,MAAM;gBACzD,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAE,CAAS,CAAC,KAAK,CAAC,CAAC,MAAM;aACrD,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,wCAAwC,MAAM,CAAC,MAAM,EAAE,CAAC,CAAC;QAC3E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,qCAAqC,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAChF,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,QAAQ;aACnC,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,gBAAgB,CACL,IAAuC,EACnC,MAA2B;QAE9C,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YACvD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CACjE,MAAM,CAAC,MAAM,EACb,IAAI,CAAC,IAAI,IAAI,CAAC,EACd,IAAI,CAAC,KAAK,IAAI,EAAE,CACjB,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,qBAAqB,EAAE;gBACjC,GAAG,OAAO;gBACV,OAAO,EAAE,UAAU;aACpB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,+BAA+B,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAC1E,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,OAAO,EAAE,UAAU;aACpB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGK,AAAN,KAAK,CAAC,oBAAoB,CACT,IAA+B,EAC3B,MAA2B;QAE9C,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;YACnB,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YACvD,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,cAAc,CACzD,IAAI,CAAC,aAAa,EAClB,MAAM,CAAC,MAAM,CACd,CAAC;YAEF,MAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;gBAC1C,WAAW,EAAE,MAAM;gBACnB,OAAO,EAAE,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO;aAChD,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,mCAAmC,MAAM,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;YAC9E,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;gBAC/B,OAAO,EAAE,MAAM;aAChB,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGD,UAAU,CAAC,MAAc,EAAE,KAAa,EAAE,IAAS;QACjD,MAAM,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAC/C,IAAI,MAAM,EAAE,CAAC;YACX,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAGD,SAAS,CAAC,KAAa,EAAE,IAAS;QAChC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;IAChC,CAAC;CACF,CAAA;AAtPY,gDAAkB;AAE7B;IADC,IAAA,4BAAe,GAAE;8BACV,kBAAM;kDAAC;AA8CT;IADL,IAAA,6BAAgB,EAAC,mBAAmB,CAAC;IAEnC,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;qCADG,4BAAY;;gEA2DlC;AAGK;IADL,IAAA,6BAAgB,EAAC,mBAAmB,CAAC;IAEnC,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;;;gEA8DnB;AAGK;IADL,IAAA,6BAAgB,EAAC,qBAAqB,CAAC;IAErC,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;;;0DAwBnB;AAGK;IADL,IAAA,6BAAgB,EAAC,sBAAsB,CAAC;IAEtC,WAAA,IAAA,wBAAW,GAAE,CAAA;IACb,WAAA,IAAA,4BAAe,GAAE,CAAA;;;;8DAuBnB;6BAxOU,kBAAkB;IAP9B,IAAA,6BAAgB,EAAC;QAChB,IAAI,EAAE;YACJ,MAAM,EAAE,IAAI;YACZ,WAAW,EAAE,IAAI;SAClB;QACD,SAAS,EAAE,aAAa;KACzB,CAAC;qCAS+B,gBAAU;QACF,wCAAkB;GAT9C,kBAAkB,CAsP9B"}