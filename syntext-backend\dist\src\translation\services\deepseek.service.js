"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var DeepSeekService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.DeepSeekService = void 0;
const common_1 = require("@nestjs/common");
const axios_1 = require("@nestjs/axios");
const config_1 = require("@nestjs/config");
const rxjs_1 = require("rxjs");
let DeepSeekService = DeepSeekService_1 = class DeepSeekService {
    httpService;
    configService;
    logger = new common_1.Logger(DeepSeekService_1.name);
    apiKey;
    apiUrl;
    constructor(httpService, configService) {
        this.httpService = httpService;
        this.configService = configService;
        this.apiKey = this.configService.get('DEEPSEEK_API_KEY') || '';
        this.apiUrl = this.configService.get('DEEPSEEK_API_URL') || '';
    }
    async translateText(request) {
        const startTime = Date.now();
        try {
            const prompt = this.buildTranslationPrompt(request);
            const response = await (0, rxjs_1.firstValueFrom)(this.httpService.post(`${this.apiUrl}/chat/completions`, {
                model: 'deepseek-chat',
                messages: [
                    {
                        role: 'system',
                        content: '你是一个专业的翻译助手，专注于提供准确、自然的翻译结果。请直接返回翻译结果，不要添加任何解释或额外内容。',
                    },
                    {
                        role: 'user',
                        content: prompt,
                    },
                ],
                temperature: 0.3,
                max_tokens: 2000,
                stream: false,
            }, {
                headers: {
                    'Authorization': `Bearer ${this.apiKey}`,
                    'Content-Type': 'application/json',
                },
                timeout: 30000,
            }));
            const translatedText = response.data.choices[0]?.message?.content?.trim();
            if (!translatedText) {
                throw new Error('DeepSeek API返回了空的翻译结果');
            }
            const processingTime = Date.now() - startTime;
            const confidence = this.calculateConfidence(request.sourceText, translatedText);
            this.logger.log(`Translation completed in ${processingTime}ms`);
            return {
                translatedText,
                confidence,
                processingTime,
            };
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            this.logger.error('DeepSeek translation failed:', error);
            throw new Error(`翻译服务暂时不可用: ${error.message}`);
        }
    }
    async batchTranslate(requests) {
        const results = [];
        const batchSize = 3;
        for (let i = 0; i < requests.length; i += batchSize) {
            const batch = requests.slice(i, i + batchSize);
            const batchPromises = batch.map(request => this.translateText(request));
            try {
                const batchResults = await Promise.all(batchPromises);
                results.push(...batchResults);
            }
            catch (error) {
                this.logger.error(`Batch translation failed for batch ${i / batchSize + 1}:`, error);
                throw error;
            }
        }
        return results;
    }
    buildTranslationPrompt(request) {
        const { sourceText, sourceLanguage, targetLanguage, context } = request;
        let prompt = `请将以下${this.getLanguageName(sourceLanguage)}文本翻译成${this.getLanguageName(targetLanguage)}：\n\n${sourceText}`;
        if (context) {
            prompt += `\n\n上下文信息：${context}`;
        }
        prompt += '\n\n要求：\n1. 保持原文的语义和语调\n2. 确保翻译自然流畅\n3. 只返回翻译结果，不要添加任何解释';
        return prompt;
    }
    getLanguageName(languageCode) {
        const languageMap = {
            'zh': '中文',
            'en': '英文',
            'ja': '日文',
            'ko': '韩文',
            'es': '西班牙语',
            'fr': '法语',
            'de': '德语',
            'ru': '俄语',
            'it': '意大利语',
            'pt': '葡萄牙语',
            'ar': '阿拉伯语',
            'hi': '印地语',
            'auto': '自动检测',
        };
        return languageMap[languageCode] || languageCode;
    }
    calculateConfidence(sourceText, translatedText) {
        let confidence = 0.8;
        const lengthRatio = translatedText.length / sourceText.length;
        if (lengthRatio > 0.3 && lengthRatio < 3) {
            confidence += 0.1;
        }
        const errorPatterns = ['翻译失败', 'translation failed', '无法翻译', 'error'];
        const hasError = errorPatterns.some(pattern => translatedText.toLowerCase().includes(pattern.toLowerCase()));
        if (hasError) {
            confidence -= 0.3;
        }
        return Math.max(0, Math.min(1, confidence));
    }
};
exports.DeepSeekService = DeepSeekService;
exports.DeepSeekService = DeepSeekService = DeepSeekService_1 = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [axios_1.HttpService,
        config_1.ConfigService])
], DeepSeekService);
//# sourceMappingURL=deepseek.service.js.map