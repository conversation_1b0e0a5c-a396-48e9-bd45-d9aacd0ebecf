import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAppSelector } from '../hooks';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
  category: string;
}

interface GuideStep {
  title: string;
  description: string;
  icon: React.ReactElement;
}

const HelpPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAppSelector((state) => state.auth);
  
  const [activeCategory, setActiveCategory] = useState('all');
  const [expandedFAQ, setExpandedFAQ] = useState<number | null>(null);
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    subject: '',
    message: '',
  });
  const [submitting, setSubmitting] = useState(false);

  const categories = [
    { id: 'all', name: '全部' },
    { id: 'basic', name: '基础使用' },
    { id: 'account', name: '账户管理' },
    { id: 'translation', name: '翻译功能' },
    { id: 'billing', name: '订阅付费' },
    { id: 'technical', name: '技术问题' },
  ];

  const faqs: FAQItem[] = [
    {
      id: 1,
      question: '如何开始使用 SynText 翻译服务？',
      answer: '您只需要注册账户并登录，然后在翻译页面输入要翻译的文本，选择源语言和目标语言，点击翻译按钮即可。我们支持自动检测源语言功能。',
      category: 'basic',
    },
    {
      id: 2,
      question: 'SynText 支持哪些语言？',
      answer: '我们支持50多种主流语言，包括中文、英文、日文、韩文、法语、德语、西班牙语、俄语、阿拉伯语等。您可以在翻译页面查看完整的语言列表。',
      category: 'translation',
    },
    {
      id: 3,
      question: '翻译结果的准确性如何？',
      answer: '我们使用基于 DeepSeek AI 的先进翻译引擎，翻译准确率通常在90%以上。系统会显示置信度分数，帮助您评估翻译质量。对于专业术语，建议结合语境进行人工校对。',
      category: 'translation',
    },
    {
      id: 4,
      question: '如何查看和管理翻译历史？',
      answer: '在翻译历史页面，您可以查看所有翻译记录，支持按语言、时间、收藏状态进行筛选。您还可以收藏重要翻译、重新翻译或复制翻译结果。',
      category: 'basic',
    },
    {
      id: 5,
      question: '免费版和高级版有什么区别？',
      answer: '免费版提供基础翻译功能，每月有字符数限制。高级版提供无限翻译、批量翻译、优先处理、高级API接口等功能。企业版还包含定制化服务和专属支持。',
      category: 'billing',
    },
    {
      id: 6,
      question: '如何修改个人信息和偏好设置？',
      answer: '在个人设置页面，您可以修改姓名、默认翻译语言、界面主题、通知设置等。还可以启用两步验证来提高账户安全性。',
      category: 'account',
    },
    {
      id: 7,
      question: '翻译速度慢怎么办？',
      answer: '翻译速度可能受网络状况、文本长度和服务器负载影响。建议检查网络连接，避免翻译过长文本。高级版用户享有优先处理，速度更快。',
      category: 'technical',
    },
    {
      id: 8,
      question: '如何导出我的翻译数据？',
      answer: '在个人设置的安全设置中，您可以申请导出所有翻译数据。我们会将数据整理后通过邮件发送给您，通常需要1-3个工作日。',
      category: 'account',
    },
  ];

  const quickGuide: GuideStep[] = [
    {
      title: '注册账户',
      description: '创建您的 SynText 账户，获取翻译服务权限',
      icon: (
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
        </svg>
      ),
    },
    {
      title: '输入文本',
      description: '在翻译页面输入要翻译的内容，选择语言',
      icon: (
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
      ),
    },
    {
      title: '获取翻译',
      description: '点击翻译按钮，即可获得高质量的翻译结果',
      icon: (
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
        </svg>
      ),
    },
    {
      title: '管理历史',
      description: '查看翻译历史，收藏重要内容，随时回顾',
      icon: (
        <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
  ];

  const filteredFAQs = activeCategory === 'all' 
    ? faqs 
    : faqs.filter(faq => faq.category === activeCategory);

  const handleFAQToggle = (id: number) => {
    setExpandedFAQ(expandedFAQ === id ? null : id);
  };

  const handleContactSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    
    try {
      // TODO: 调用实际API提交联系表单
      // await supportApi.submitContact(contactForm);
      
      // 模拟提交
      setTimeout(() => {
        alert('消息已发送，我们会尽快回复您！');
        setContactForm({ name: '', email: '', subject: '', message: '' });
        setSubmitting(false);
      }, 1000);
    } catch (error) {
      console.error('提交失败:', error);
      alert('提交失败，请重试');
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 顶部导航 */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <button
                onClick={() => navigate('/')}
                className="text-gray-500 hover:text-gray-700 mr-4"
              >
                ← 返回首页
              </button>
              <h1 className="text-xl font-bold text-gray-900">帮助中心</h1>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-700">{user?.name || user?.email}</span>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 欢迎区域 */}
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            我们来帮助您
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            在这里您可以找到使用 SynText 的详细指南、常见问题解答和联系支持的方式
          </p>
        </div>

        {/* 快速指南 */}
        <div className="mb-12">
          <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">快速开始指南</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
            {quickGuide.map((step, index) => (
              <div key={index} className="bg-white rounded-lg shadow-sm p-4 sm:p-6 text-center">
                <div className="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4">
                  <div className="text-primary-600">{step.icon}</div>
                </div>
                <h4 className="text-lg font-semibold text-gray-900 mb-2">{step.title}</h4>
                <p className="text-sm text-gray-600">{step.description}</p>
                <div className="mt-4 text-xs text-gray-400">步骤 {index + 1}</div>
              </div>
            ))}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
          {/* 左侧：常见问题 */}
          <div className="lg:col-span-2">
            <div className="bg-white shadow rounded-lg">
              <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                <h3 className="text-base sm:text-lg font-medium text-gray-900">常见问题</h3>
              </div>

              {/* 分类筛选 */}
              <div className="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
                <div className="flex flex-wrap gap-2">
                  {categories.map((category) => (
                    <button
                      key={category.id}
                      onClick={() => setActiveCategory(category.id)}
                      className={`px-3 py-1 text-sm rounded-full transition-colors ${
                        activeCategory === category.id
                          ? 'bg-primary-100 text-primary-700'
                          : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                      }`}
                    >
                      {category.name}
                    </button>
                  ))}
                </div>
              </div>

              {/* FAQ 列表 */}
              <div className="divide-y divide-gray-200">
                {filteredFAQs.map((faq) => (
                  <div key={faq.id} className="px-4 sm:px-6 py-3 sm:py-4">
                    <button
                      onClick={() => handleFAQToggle(faq.id)}
                      className="w-full text-left flex justify-between items-center"
                    >
                      <h4 className="text-sm font-medium text-gray-900 pr-4">
                        {faq.question}
                      </h4>
                      <svg
                        className={`h-5 w-5 text-gray-500 transition-transform ${
                          expandedFAQ === faq.id ? 'rotate-180' : ''
                        }`}
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </button>
                    {expandedFAQ === faq.id && (
                      <div className="mt-3 text-sm text-gray-600">
                        {faq.answer}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* 右侧：联系支持 */}
          <div className="space-y-6">
            {/* 快速链接 */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">快速链接</h3>
              <div className="space-y-3">
                <button
                  onClick={() => navigate('/translate')}
                  className="w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50"
                >
                  <svg className="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                  </svg>
                  开始翻译
                </button>
                <button
                  onClick={() => navigate('/history')}
                  className="w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50"
                >
                  <svg className="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  翻译历史
                </button>
                <button
                  onClick={() => navigate('/settings')}
                  className="w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50"
                >
                  <svg className="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  个人设置
                </button>
                <button
                  onClick={() => navigate('/profile')}
                  className="w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50"
                >
                  <svg className="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                  </svg>
                  个人中心
                </button>
              </div>
            </div>

            {/* 联系我们 */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">联系我们</h3>
              <form onSubmit={handleContactSubmit} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    姓名
                  </label>
                  <input
                    type="text"
                    value={contactForm.name}
                    onChange={(e) => setContactForm({ ...contactForm, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    邮箱
                  </label>
                  <input
                    type="email"
                    value={contactForm.email}
                    onChange={(e) => setContactForm({ ...contactForm, email: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    主题
                  </label>
                  <select
                    value={contactForm.subject}
                    onChange={(e) => setContactForm({ ...contactForm, subject: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    required
                  >
                    <option value="">请选择主题</option>
                    <option value="technical">技术问题</option>
                    <option value="billing">订阅付费</option>
                    <option value="feature">功能建议</option>
                    <option value="other">其他问题</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    消息内容
                  </label>
                  <textarea
                    value={contactForm.message}
                    onChange={(e) => setContactForm({ ...contactForm, message: e.target.value })}
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
                    placeholder="请详细描述您的问题..."
                    required
                  />
                </div>

                <button
                  type="submit"
                  disabled={submitting}
                  className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50"
                >
                  {submitting ? '发送中...' : '发送消息'}
                </button>
              </form>
            </div>

            {/* 其他支持方式 */}
            <div className="bg-white shadow rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-900 mb-4">其他支持方式</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <svg className="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="text-sm text-gray-600"><EMAIL></span>
                </div>
                <div className="flex items-center">
                  <svg className="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm text-gray-600">工作日 9:00-18:00</span>
                </div>
                <div className="flex items-center">
                  <svg className="h-5 w-5 text-gray-400 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm text-gray-600">24小时内回复</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HelpPage;