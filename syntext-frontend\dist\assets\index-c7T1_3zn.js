import{c as e,a as t,b as n,u as r,d as a,r as s,P as l}from"./redux-vendor-bhcDk3Gk.js";import{r as i,a as o}from"./react-vendor-CEjTMBxM.js";import{a as c,A as u}from"./utils-vendor-Bl6Su708.js";import{u as d,L as m,B as h,R as f,a as p,N as g}from"./router-vendor-DBo3Rl4Z.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var x,y,b={exports:{}},v={};var w,j,k,N,S=(y||(y=1,b.exports=function(){if(x)return v;x=1;var e=Symbol.for("react.transitional.element"),t=Symbol.for("react.fragment");function n(t,n,r){var a=null;if(void 0!==r&&(a=""+r),void 0!==n.key&&(a=""+n.key),"key"in n)for(var s in r={},n)"key"!==s&&(r[s]=n[s]);else r=n;return n=r.ref,{$$typeof:e,type:t,key:a,ref:void 0!==n?n:null,props:r}}return v.Fragment=t,v.jsx=n,v.jsxs=n,v}()),b.exports),C={exports:{}},L={},T={exports:{}},E={};function _(){return j||(j=1,T.exports=(w||(w=1,function(e){function t(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,s=e[r];if(!(0<a(s,t)))break e;e[r]=t,e[n]=s,n=r}}function n(e){return 0===e.length?null:e[0]}function r(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,s=e.length,l=s>>>1;r<l;){var i=2*(r+1)-1,o=e[i],c=i+1,u=e[c];if(0>a(o,n))c<s&&0>a(u,o)?(e[r]=u,e[c]=n,r=c):(e[r]=o,e[i]=n,r=i);else{if(!(c<s&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(e.unstable_now=void 0,"object"==typeof performance&&"function"==typeof performance.now){var s=performance;e.unstable_now=function(){return s.now()}}else{var l=Date,i=l.now();e.unstable_now=function(){return l.now()-i}}var o=[],c=[],u=1,d=null,m=3,h=!1,f=!1,p=!1,g=!1,x="function"==typeof setTimeout?setTimeout:null,y="function"==typeof clearTimeout?clearTimeout:null,b="undefined"!=typeof setImmediate?setImmediate:null;function v(e){for(var a=n(c);null!==a;){if(null===a.callback)r(c);else{if(!(a.startTime<=e))break;r(c),a.sortIndex=a.expirationTime,t(o,a)}a=n(c)}}function w(e){if(p=!1,v(e),!f)if(null!==n(o))f=!0,k||(k=!0,j());else{var t=n(c);null!==t&&P(w,t.startTime-e)}}var j,k=!1,N=-1,S=5,C=-1;function L(){return!(!g&&e.unstable_now()-C<S)}function T(){if(g=!1,k){var t=e.unstable_now();C=t;var a=!0;try{e:{f=!1,p&&(p=!1,y(N),N=-1),h=!0;var s=m;try{t:{for(v(t),d=n(o);null!==d&&!(d.expirationTime>t&&L());){var l=d.callback;if("function"==typeof l){d.callback=null,m=d.priorityLevel;var i=l(d.expirationTime<=t);if(t=e.unstable_now(),"function"==typeof i){d.callback=i,v(t),a=!0;break t}d===n(o)&&r(o),v(t)}else r(o);d=n(o)}if(null!==d)a=!0;else{var u=n(c);null!==u&&P(w,u.startTime-t),a=!1}}break e}finally{d=null,m=s,h=!1}a=void 0}}finally{a?j():k=!1}}}if("function"==typeof b)j=function(){b(T)};else if("undefined"!=typeof MessageChannel){var E=new MessageChannel,_=E.port2;E.port1.onmessage=T,j=function(){_.postMessage(null)}}else j=function(){x(T,0)};function P(t,n){N=x(function(){t(e.unstable_now())},n)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(e){e.callback=null},e.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):S=0<e?Math.floor(1e3/e):5},e.unstable_getCurrentPriorityLevel=function(){return m},e.unstable_next=function(e){switch(m){case 1:case 2:case 3:var t=3;break;default:t=m}var n=m;m=t;try{return e()}finally{m=n}},e.unstable_requestPaint=function(){g=!0},e.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=m;m=e;try{return t()}finally{m=n}},e.unstable_scheduleCallback=function(r,a,s){var l=e.unstable_now();switch(s="object"==typeof s&&null!==s&&"number"==typeof(s=s.delay)&&0<s?l+s:l,r){case 1:var i=-1;break;case 2:i=250;break;case 5:i=**********;break;case 4:i=1e4;break;default:i=5e3}return r={id:u++,callback:a,priorityLevel:r,startTime:s,expirationTime:i=s+i,sortIndex:-1},s>l?(r.sortIndex=s,t(c,r),null===n(o)&&r===n(c)&&(p?(y(N),N=-1):p=!0,P(w,s-l))):(r.sortIndex=i,t(o,r),f||h||(f=!0,k||(k=!0,j()))),r},e.unstable_shouldYield=L,e.unstable_wrapCallback=function(e){var t=m;return function(){var n=m;m=t;try{return e.apply(this,arguments)}finally{m=n}}}}(E)),E)),T.exports}
/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */function P(){if(k)return L;k=1;var e=_(),t=i(),n=o();function r(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function s(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{!!(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function l(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function c(e){if(s(e)!==e)throw Error(r(188))}function u(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=u(e)))return t;e=e.sibling}return null}var d=Object.assign,m=Symbol.for("react.element"),h=Symbol.for("react.transitional.element"),f=Symbol.for("react.portal"),p=Symbol.for("react.fragment"),g=Symbol.for("react.strict_mode"),x=Symbol.for("react.profiler"),y=Symbol.for("react.provider"),b=Symbol.for("react.consumer"),v=Symbol.for("react.context"),w=Symbol.for("react.forward_ref"),j=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),S=Symbol.for("react.memo"),C=Symbol.for("react.lazy"),T=Symbol.for("react.activity"),E=Symbol.for("react.memo_cache_sentinel"),P=Symbol.iterator;function z(e){return null===e||"object"!=typeof e?null:"function"==typeof(e=P&&e[P]||e["@@iterator"])?e:null}var A=Symbol.for("react.client.reference");function O(e){if(null==e)return null;if("function"==typeof e)return e.$$typeof===A?null:e.displayName||e.name||null;if("string"==typeof e)return e;switch(e){case p:return"Fragment";case x:return"Profiler";case g:return"StrictMode";case j:return"Suspense";case N:return"SuspenseList";case T:return"Activity"}if("object"==typeof e)switch(e.$$typeof){case f:return"Portal";case v:return(e.displayName||"Context")+".Provider";case b:return(e._context.displayName||"Context")+".Consumer";case w:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case S:return null!==(t=e.displayName||null)?t:O(e.type)||"Memo";case C:t=e._payload,e=e._init;try{return O(e(t))}catch(n){}}return null}var M=Array.isArray,R=t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,D=n.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F={pending:!1,data:null,method:null,action:null},B=[],I=-1;function U(e){return{current:e}}function W(e){0>I||(e.current=B[I],B[I]=null,I--)}function q(e,t){I++,B[I]=e.current,e.current=t}var H=U(null),V=U(null),$=U(null),Q=U(null);function K(e,t){switch(q($,t),q(V,e),q(H,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?ld(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)e=id(t=ld(t),e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}W(H),q(H,e)}function Y(){W(H),W(V),W($)}function X(e){null!==e.memoizedState&&q(Q,e);var t=H.current,n=id(t,e.type);t!==n&&(q(V,e),q(H,n))}function Z(e){V.current===e&&(W(H),W(V)),Q.current===e&&(W(Q),Xd._currentValue=F)}var G=Object.prototype.hasOwnProperty,J=e.unstable_scheduleCallback,ee=e.unstable_cancelCallback,te=e.unstable_shouldYield,ne=e.unstable_requestPaint,re=e.unstable_now,ae=e.unstable_getCurrentPriorityLevel,se=e.unstable_ImmediatePriority,le=e.unstable_UserBlockingPriority,ie=e.unstable_NormalPriority,oe=e.unstable_LowPriority,ce=e.unstable_IdlePriority,ue=e.log,de=e.unstable_setDisableYieldValue,me=null,he=null;function fe(e){if("function"==typeof ue&&de(e),he&&"function"==typeof he.setStrictMode)try{he.setStrictMode(me,e)}catch(t){}}var pe=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(ge(e)/xe|0)|0},ge=Math.log,xe=Math.LN2;var ye=256,be=4194304;function ve(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194048&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function we(e,t,n){var r=e.pendingLanes;if(0===r)return 0;var a=0,s=e.suspendedLanes,l=e.pingedLanes;e=e.warmLanes;var i=134217727&r;return 0!==i?0!==(r=i&~s)?a=ve(r):0!==(l&=i)?a=ve(l):n||0!==(n=i&~e)&&(a=ve(n)):0!==(i=r&~s)?a=ve(i):0!==l?a=ve(l):n||0!==(n=r&~e)&&(a=ve(n)),0===a?0:0!==t&&t!==a&&0===(t&s)&&((s=a&-a)>=(n=t&-t)||32===s&&4194048&n)?t:a}function je(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function ke(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function Ne(){var e=ye;return!(4194048&(ye<<=1))&&(ye=256),e}function Se(){var e=be;return!(62914560&(be<<=1))&&(be=4194304),e}function Ce(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Le(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Te(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-pe(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194090&n}function Ee(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-pe(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}function _e(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Pe(e){return 2<(e&=-e)?8<e?134217727&e?32:268435456:8:2}function ze(){var e=D.p;return 0!==e?e:void 0===(e=window.event)?32:dm(e.type)}var Ae=Math.random().toString(36).slice(2),Oe="__reactFiber$"+Ae,Me="__reactProps$"+Ae,Re="__reactContainer$"+Ae,De="__reactEvents$"+Ae,Fe="__reactListeners$"+Ae,Be="__reactHandles$"+Ae,Ie="__reactResources$"+Ae,Ue="__reactMarker$"+Ae;function We(e){delete e[Oe],delete e[Me],delete e[De],delete e[Fe],delete e[Be]}function qe(e){var t=e[Oe];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Re]||n[Oe]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=wd(e);null!==e;){if(n=e[Oe])return n;e=wd(e)}return t}n=(e=n).parentNode}return null}function He(e){if(e=e[Oe]||e[Re]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Ve(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(r(33))}function $e(e){var t=e[Ie];return t||(t=e[Ie]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Qe(e){e[Ue]=!0}var Ke=new Set,Ye={};function Xe(e,t){Ze(e,t),Ze(e+"Capture",t)}function Ze(e,t){for(Ye[e]=t,e=0;e<t.length;e++)Ke.add(t[e])}var Ge,Je,et=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),tt={},nt={};function rt(e,t,n){if(a=t,G.call(nt,a)||!G.call(tt,a)&&(et.test(a)?nt[a]=!0:(tt[a]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var a}function at(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function st(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function lt(e){if(void 0===Ge)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Ge=t&&t[1]||"",Je=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+Ge+e+Je}var it=!1;function ot(e,t){if(!e||it)return"";it=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"==typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(a){var r=a}Reflect.construct(e,[],n)}else{try{n.call()}catch(s){r=s}e.call(n.prototype)}}else{try{throw Error()}catch(l){r=l}(n=e())&&"function"==typeof n.catch&&n.catch(function(){})}}catch(i){if(i&&r&&"string"==typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var s=r.DetermineComponentFrameRoot(),l=s[0],i=s[1];if(l&&i){var o=l.split("\n"),c=i.split("\n");for(a=r=0;r<o.length&&!o[r].includes("DetermineComponentFrameRoot");)r++;for(;a<c.length&&!c[a].includes("DetermineComponentFrameRoot");)a++;if(r===o.length||a===c.length)for(r=o.length-1,a=c.length-1;1<=r&&0<=a&&o[r]!==c[a];)a--;for(;1<=r&&0<=a;r--,a--)if(o[r]!==c[a]){if(1!==r||1!==a)do{if(r--,0>--a||o[r]!==c[a]){var u="\n"+o[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=a);break}}}finally{it=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?lt(n):""}function ct(e){switch(e.tag){case 26:case 27:case 5:return lt(e.type);case 16:return lt("Lazy");case 13:return lt("Suspense");case 19:return lt("SuspenseList");case 0:case 15:return ot(e.type,!1);case 11:return ot(e.type.render,!1);case 1:return ot(e.type,!0);case 31:return lt("Activity");default:return""}}function ut(e){try{var t="";do{t+=ct(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function dt(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function mt(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function ht(e){e._valueTracker||(e._valueTracker=function(e){var t=mt(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&void 0!==n&&"function"==typeof n.get&&"function"==typeof n.set){var a=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,s.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=mt(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if(void 0===(e=e||("undefined"!=typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var gt=/[\n"\\]/g;function xt(e){return e.replace(gt,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function yt(e,t,n,r,a,s,l,i){e.name="",null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l?e.type=l:e.removeAttribute("type"),null!=t?"number"===l?(0===t&&""===e.value||e.value!=t)&&(e.value=""+dt(t)):e.value!==""+dt(t)&&(e.value=""+dt(t)):"submit"!==l&&"reset"!==l||e.removeAttribute("value"),null!=t?vt(e,l,dt(t)):null!=n?vt(e,l,dt(n)):null!=r&&e.removeAttribute("value"),null==a&&null!=s&&(e.defaultChecked=!!s),null!=a&&(e.checked=a&&"function"!=typeof a&&"symbol"!=typeof a),null!=i&&"function"!=typeof i&&"symbol"!=typeof i&&"boolean"!=typeof i?e.name=""+dt(i):e.removeAttribute("name")}function bt(e,t,n,r,a,s,l,i){if(null!=s&&"function"!=typeof s&&"symbol"!=typeof s&&"boolean"!=typeof s&&(e.type=s),null!=t||null!=n){if(("submit"===s||"reset"===s)&&null==t)return;n=null!=n?""+dt(n):"",t=null!=t?""+dt(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!=typeof(r=null!=r?r:a)&&"symbol"!=typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=l&&"function"!=typeof l&&"symbol"!=typeof l&&"boolean"!=typeof l&&(e.name=l)}function vt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function wt(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+dt(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function jt(e,t,n){null==t||((t=""+dt(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+dt(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function kt(e,t,n,a){if(null==t){if(null!=a){if(null!=n)throw Error(r(92));if(M(a)){if(1<a.length)throw Error(r(93));a=a[0]}n=a}null==n&&(n=""),t=n}n=dt(t),e.defaultValue=n,(a=e.textContent)===n&&""!==a&&null!==a&&(e.value=a)}function Nt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Ct(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"==typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!=typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Lt(e,t,n){if(null!=t&&"object"!=typeof t)throw Error(r(62));if(e=e.style,null!=n){for(var a in n)!n.hasOwnProperty(a)||null!=t&&t.hasOwnProperty(a)||(0===a.indexOf("--")?e.setProperty(a,""):"float"===a?e.cssFloat="":e[a]="");for(var s in t)a=t[s],t.hasOwnProperty(s)&&n[s]!==a&&Ct(e,s,a)}else for(var l in t)t.hasOwnProperty(l)&&Ct(e,l,t[l])}function Tt(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Et=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),_t=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Pt(e){return _t.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var zt=null;function At(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Ot=null,Mt=null;function Rt(e){var t=He(e);if(t&&(e=t.stateNode)){var n=e[Me]||null;e:switch(e=t.stateNode,t.type){case"input":if(yt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+xt(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var a=n[t];if(a!==e&&a.form===e.form){var s=a[Me]||null;if(!s)throw Error(r(90));yt(a,s.value,s.defaultValue,s.defaultValue,s.checked,s.defaultChecked,s.type,s.name)}}for(t=0;t<n.length;t++)(a=n[t]).form===e.form&&ft(a)}break e;case"textarea":jt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&wt(e,!!n.multiple,t,!1)}}}var Dt=!1;function Ft(e,t,n){if(Dt)return e(t,n);Dt=!0;try{return e(t)}finally{if(Dt=!1,(null!==Ot||null!==Mt)&&(qc(),Ot&&(t=Ot,e=Mt,Mt=Ot=null,Rt(t),e)))for(t=0;t<e.length;t++)Rt(e[t])}}function Bt(e,t){var n=e.stateNode;if(null===n)return null;var a=n[Me]||null;if(null===a)return null;n=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(a=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!a;break e;default:e=!1}if(e)return null;if(n&&"function"!=typeof n)throw Error(r(231,t,typeof n));return n}var It=!("undefined"==typeof window||void 0===window.document||void 0===window.document.createElement),Ut=!1;if(It)try{var Wt={};Object.defineProperty(Wt,"passive",{get:function(){Ut=!0}}),window.addEventListener("test",Wt,Wt),window.removeEventListener("test",Wt,Wt)}catch(Mm){Ut=!1}var qt=null,Ht=null,Vt=null;function $t(){if(Vt)return Vt;var e,t,n=Ht,r=n.length,a="value"in qt?qt.value:qt.textContent,s=a.length;for(e=0;e<r&&n[e]===a[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===a[s-t];t++);return Vt=a.slice(e,1<t?1-t:void 0)}function Qt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function Kt(){return!0}function Yt(){return!1}function Xt(e){function t(t,n,r,a,s){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=s,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(a):a[l]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?Kt:Yt,this.isPropagationStopped=Yt,this}return d(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!=typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=Kt)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!=typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=Kt)},persist:function(){},isPersistent:Kt}),t}var Zt,Gt,Jt,en={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},tn=Xt(en),nn=d({},en,{view:0,detail:0}),rn=Xt(nn),an=d({},nn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:gn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Jt&&(Jt&&"mousemove"===e.type?(Zt=e.screenX-Jt.screenX,Gt=e.screenY-Jt.screenY):Gt=Zt=0,Jt=e),Zt)},movementY:function(e){return"movementY"in e?e.movementY:Gt}}),sn=Xt(an),ln=Xt(d({},an,{dataTransfer:0})),on=Xt(d({},nn,{relatedTarget:0})),cn=Xt(d({},en,{animationName:0,elapsedTime:0,pseudoElement:0})),un=Xt(d({},en,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),dn=Xt(d({},en,{data:0})),mn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},hn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},fn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function pn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=fn[e])&&!!t[e]}function gn(){return pn}var xn=Xt(d({},nn,{key:function(e){if(e.key){var t=mn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Qt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?hn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:gn,charCode:function(e){return"keypress"===e.type?Qt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Qt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),yn=Xt(d({},an,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),bn=Xt(d({},nn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:gn})),vn=Xt(d({},en,{propertyName:0,elapsedTime:0,pseudoElement:0})),wn=Xt(d({},an,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),jn=Xt(d({},en,{newState:0,oldState:0})),kn=[9,13,27,32],Nn=It&&"CompositionEvent"in window,Sn=null;It&&"documentMode"in document&&(Sn=document.documentMode);var Cn=It&&"TextEvent"in window&&!Sn,Ln=It&&(!Nn||Sn&&8<Sn&&11>=Sn),Tn=String.fromCharCode(32),En=!1;function _n(e,t){switch(e){case"keyup":return-1!==kn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pn(e){return"object"==typeof(e=e.detail)&&"data"in e?e.data:null}var zn=!1;var An={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function On(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!An[e.type]:"textarea"===t}function Mn(e,t,n,r){Ot?Mt?Mt.push(r):Mt=[r]:Ot=r,0<(t=$u(t,"onChange")).length&&(n=new tn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Rn=null,Dn=null;function Fn(e){Fu(e,0)}function Bn(e){if(ft(Ve(e)))return e}function In(e,t){if("change"===e)return t}var Un=!1;if(It){var Wn;if(It){var qn="oninput"in document;if(!qn){var Hn=document.createElement("div");Hn.setAttribute("oninput","return;"),qn="function"==typeof Hn.oninput}Wn=qn}else Wn=!1;Un=Wn&&(!document.documentMode||9<document.documentMode)}function Vn(){Rn&&(Rn.detachEvent("onpropertychange",$n),Dn=Rn=null)}function $n(e){if("value"===e.propertyName&&Bn(Dn)){var t=[];Mn(t,Dn,e,At(e)),Ft(Fn,t)}}function Qn(e,t,n){"focusin"===e?(Vn(),Dn=n,(Rn=t).attachEvent("onpropertychange",$n)):"focusout"===e&&Vn()}function Kn(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Bn(Dn)}function Yn(e,t){if("click"===e)return Bn(t)}function Xn(e,t){if("input"===e||"change"===e)return Bn(t)}var Zn="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t};function Gn(e,t){if(Zn(e,t))return!0;if("object"!=typeof e||null===e||"object"!=typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!G.call(t,a)||!Zn(e[a],t[a]))return!1}return!0}function Jn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function er(e,t){var n,r=Jn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Jn(r)}}function tr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?tr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function nr(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"==typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function rr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}var ar=It&&"documentMode"in document&&11>=document.documentMode,sr=null,lr=null,ir=null,or=!1;function cr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;or||null==sr||sr!==pt(r)||("selectionStart"in(r=sr)&&rr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ir&&Gn(ir,r)||(ir=r,0<(r=$u(lr,"onSelect")).length&&(t=new tn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=sr)))}function ur(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var dr={animationend:ur("Animation","AnimationEnd"),animationiteration:ur("Animation","AnimationIteration"),animationstart:ur("Animation","AnimationStart"),transitionrun:ur("Transition","TransitionRun"),transitionstart:ur("Transition","TransitionStart"),transitioncancel:ur("Transition","TransitionCancel"),transitionend:ur("Transition","TransitionEnd")},mr={},hr={};function fr(e){if(mr[e])return mr[e];if(!dr[e])return e;var t,n=dr[e];for(t in n)if(n.hasOwnProperty(t)&&t in hr)return mr[e]=n[t];return e}It&&(hr=document.createElement("div").style,"AnimationEvent"in window||(delete dr.animationend.animation,delete dr.animationiteration.animation,delete dr.animationstart.animation),"TransitionEvent"in window||delete dr.transitionend.transition);var pr=fr("animationend"),gr=fr("animationiteration"),xr=fr("animationstart"),yr=fr("transitionrun"),br=fr("transitionstart"),vr=fr("transitioncancel"),wr=fr("transitionend"),jr=new Map,kr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Nr(e,t){jr.set(e,t),Xe(t,[e])}kr.push("scrollEnd");var Sr=new WeakMap;function Cr(e,t){if("object"==typeof e&&null!==e){var n=Sr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:ut(t)},Sr.set(e,t),t)}return{value:e,source:t,stack:ut(t)}}var Lr=[],Tr=0,Er=0;function _r(){for(var e=Tr,t=Er=Tr=0;t<e;){var n=Lr[t];Lr[t++]=null;var r=Lr[t];Lr[t++]=null;var a=Lr[t];Lr[t++]=null;var s=Lr[t];if(Lr[t++]=null,null!==r&&null!==a){var l=r.pending;null===l?a.next=a:(a.next=l.next,l.next=a),r.pending=a}0!==s&&Or(n,a,s)}}function Pr(e,t,n,r){Lr[Tr++]=e,Lr[Tr++]=t,Lr[Tr++]=n,Lr[Tr++]=r,Er|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function zr(e,t,n,r){return Pr(e,t,n,r),Mr(e)}function Ar(e,t){return Pr(e,null,null,t),Mr(e)}function Or(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var a=!1,s=e.return;null!==s;)s.childLanes|=n,null!==(r=s.alternate)&&(r.childLanes|=n),22===s.tag&&(null===(e=s.stateNode)||1&e._visibility||(a=!0)),e=s,s=s.return;return 3===e.tag?(s=e.stateNode,a&&null!==t&&(a=31-pe(n),null===(r=(e=s.hiddenUpdates)[a])?e[a]=[t]:r.push(t),t.lane=536870912|n),s):null}function Mr(e){if(50<Oc)throw Oc=0,Mc=null,Error(r(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Rr={};function Dr(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Fr(e,t,n,r){return new Dr(e,t,n,r)}function Br(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ir(e,t){var n=e.alternate;return null===n?((n=Fr(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=65011712&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Ur(e,t){e.flags&=65011714;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Wr(e,t,n,a,s,l){var i=0;if(a=e,"function"==typeof e)Br(e)&&(i=1);else if("string"==typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!=typeof t.precedence||"string"!=typeof t.href||""===t.href)break;return!0;case"link":if("string"!=typeof t.rel||"string"!=typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"==typeof t.precedence&&null==e);case"script":if(t.async&&"function"!=typeof t.async&&"symbol"!=typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"==typeof t.src)return!0}return!1}(e,n,H.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case T:return(e=Fr(31,n,t,s)).elementType=T,e.lanes=l,e;case p:return qr(n.children,s,l,t);case g:i=8,s|=24;break;case x:return(e=Fr(12,n,t,2|s)).elementType=x,e.lanes=l,e;case j:return(e=Fr(13,n,t,s)).elementType=j,e.lanes=l,e;case N:return(e=Fr(19,n,t,s)).elementType=N,e.lanes=l,e;default:if("object"==typeof e&&null!==e)switch(e.$$typeof){case y:case v:i=10;break e;case b:i=9;break e;case w:i=11;break e;case S:i=14;break e;case C:i=16,a=null;break e}i=29,n=Error(r(130,null===e?"null":typeof e,"")),a=null}return(t=Fr(i,n,t,s)).elementType=e,t.type=a,t.lanes=l,t}function qr(e,t,n,r){return(e=Fr(7,e,r,t)).lanes=n,e}function Hr(e,t,n){return(e=Fr(6,e,null,t)).lanes=n,e}function Vr(e,t,n){return(t=Fr(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var $r=[],Qr=0,Kr=null,Yr=0,Xr=[],Zr=0,Gr=null,Jr=1,ea="";function ta(e,t){$r[Qr++]=Yr,$r[Qr++]=Kr,Kr=e,Yr=t}function na(e,t,n){Xr[Zr++]=Jr,Xr[Zr++]=ea,Xr[Zr++]=Gr,Gr=e;var r=Jr;e=ea;var a=32-pe(r)-1;r&=~(1<<a),n+=1;var s=32-pe(t)+a;if(30<s){var l=a-a%5;s=(r&(1<<l)-1).toString(32),r>>=l,a-=l,Jr=1<<32-pe(t)+a|n<<a|r,ea=s+e}else Jr=1<<s|n<<a|r,ea=e}function ra(e){null!==e.return&&(ta(e,1),na(e,1,0))}function aa(e){for(;e===Kr;)Kr=$r[--Qr],$r[Qr]=null,Yr=$r[--Qr],$r[Qr]=null;for(;e===Gr;)Gr=Xr[--Zr],Xr[Zr]=null,ea=Xr[--Zr],Xr[Zr]=null,Jr=Xr[--Zr],Xr[Zr]=null}var sa=null,la=null,ia=!1,oa=null,ca=!1,ua=Error(r(519));function da(e){throw xa(Cr(Error(r(418,"")),e)),ua}function ma(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Oe]=e,t[Me]=r,n){case"dialog":Bu("cancel",t),Bu("close",t);break;case"iframe":case"object":case"embed":Bu("load",t);break;case"video":case"audio":for(n=0;n<Ru.length;n++)Bu(Ru[n],t);break;case"source":Bu("error",t);break;case"img":case"image":case"link":Bu("error",t),Bu("load",t);break;case"details":Bu("toggle",t);break;case"input":Bu("invalid",t),bt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),ht(t);break;case"select":Bu("invalid",t);break;case"textarea":Bu("invalid",t),kt(t,r.value,r.defaultValue,r.children),ht(t)}"string"!=typeof(n=r.children)&&"number"!=typeof n&&"bigint"!=typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Gu(t.textContent,n)?(null!=r.popover&&(Bu("beforetoggle",t),Bu("toggle",t)),null!=r.onScroll&&Bu("scroll",t),null!=r.onScrollEnd&&Bu("scrollend",t),null!=r.onClick&&(t.onclick=Ju),t=!0):t=!1,t||da(e)}function ha(e){for(sa=e.return;sa;)switch(sa.tag){case 5:case 13:return void(ca=!1);case 27:case 3:return void(ca=!0);default:sa=sa.return}}function fa(e){if(e!==sa)return!1;if(!ia)return ha(e),ia=!0,!1;var t,n=e.tag;if((t=3!==n&&27!==n)&&((t=5===n)&&(t=!("form"!==(t=e.type)&&"button"!==t)||od(e.type,e.memoizedProps)),t=!t),t&&la&&da(e),ha(e),13===n){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(r(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){la=bd(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}la=null}}else 27===n?(n=la,pd(e.type)?(e=vd,vd=null,la=e):la=n):la=sa?bd(e.stateNode.nextSibling):null;return!0}function pa(){la=sa=null,ia=!1}function ga(){var e=oa;return null!==e&&(null===wc?wc=e:wc.push.apply(wc,e),oa=null),e}function xa(e){null===oa?oa=[e]:oa.push(e)}var ya=U(null),ba=null,va=null;function wa(e,t,n){q(ya,t._currentValue),t._currentValue=n}function ja(e){e._currentValue=ya.current,W(ya)}function ka(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Na(e,t,n,a){var s=e.child;for(null!==s&&(s.return=e);null!==s;){var l=s.dependencies;if(null!==l){var i=s.child;l=l.firstContext;e:for(;null!==l;){var o=l;l=s;for(var c=0;c<t.length;c++)if(o.context===t[c]){l.lanes|=n,null!==(o=l.alternate)&&(o.lanes|=n),ka(l.return,n,e),a||(i=null);break e}l=o.next}}else if(18===s.tag){if(null===(i=s.return))throw Error(r(341));i.lanes|=n,null!==(l=i.alternate)&&(l.lanes|=n),ka(i,n,e),i=null}else i=s.child;if(null!==i)i.return=s;else for(i=s;null!==i;){if(i===e){i=null;break}if(null!==(s=i.sibling)){s.return=i.return,i=s;break}i=i.return}s=i}}function Sa(e,t,n,a){e=null;for(var s=t,l=!1;null!==s;){if(!l)if(524288&s.flags)l=!0;else if(262144&s.flags)break;if(10===s.tag){var i=s.alternate;if(null===i)throw Error(r(387));if(null!==(i=i.memoizedProps)){var o=s.type;Zn(s.pendingProps.value,i.value)||(null!==e?e.push(o):e=[o])}}else if(s===Q.current){if(null===(i=s.alternate))throw Error(r(387));i.memoizedState.memoizedState!==s.memoizedState.memoizedState&&(null!==e?e.push(Xd):e=[Xd])}s=s.return}null!==e&&Na(t,e,n,a),t.flags|=262144}function Ca(e){for(e=e.firstContext;null!==e;){if(!Zn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function La(e){ba=e,va=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ta(e){return _a(ba,e)}function Ea(e,t){return null===ba&&La(e),_a(e,t)}function _a(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===va){if(null===e)throw Error(r(308));va=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else va=va.next=t;return n}var Pa="undefined"!=typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach(function(e){return e()})}},za=e.unstable_scheduleCallback,Aa=e.unstable_NormalPriority,Oa={$$typeof:v,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Ma(){return{controller:new Pa,data:new Map,refCount:0}}function Ra(e){e.refCount--,0===e.refCount&&za(Aa,function(){e.controller.abort()})}var Da=null,Fa=0,Ba=0,Ia=null;function Ua(){if(0===--Fa&&null!==Da){null!==Ia&&(Ia.status="fulfilled");var e=Da;Da=null,Ba=0,Ia=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Wa=R.S;R.S=function(e,t){"object"==typeof t&&null!==t&&"function"==typeof t.then&&function(e,t){if(null===Da){var n=Da=[];Fa=0,Ba=Pu(),Ia={status:"pending",value:void 0,then:function(e){n.push(e)}}}Fa++,t.then(Ua,Ua)}(0,t),null!==Wa&&Wa(e,t)};var qa=U(null);function Ha(){var e=qa.current;return null!==e?e:sc.pooledCache}function Va(e,t){q(qa,null===t?qa.current:t.pool)}function $a(){var e=Ha();return null===e?null:{parent:Oa._currentValue,pool:e}}var Qa=Error(r(460)),Ka=Error(r(474)),Ya=Error(r(542)),Xa={then:function(){}};function Za(e){return"fulfilled"===(e=e.status)||"rejected"===e}function Ga(){}function Ja(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(Ga,Ga),t=n),t.status){case"fulfilled":return t.value;case"rejected":throw ns(e=t.reason),e;default:if("string"==typeof t.status)t.then(Ga,Ga);else{if(null!==(e=sc)&&100<e.shellSuspendCounter)throw Error(r(482));(e=t).status="pending",e.then(function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}},function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw ns(e=t.reason),e}throw es=t,Qa}}var es=null;function ts(){if(null===es)throw Error(r(459));var e=es;return es=null,e}function ns(e){if(e===Qa||e===Ya)throw Error(r(483))}var rs=!1;function as(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ss(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function ls(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function is(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,2&ac){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,t=Mr(e),Or(e,null,n),t}return Pr(e,r,t,n),Mr(e)}function os(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,4194048&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ee(e,n)}}function cs(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,s=null;if(null!==(n=n.firstBaseUpdate)){do{var l={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===s?a=s=l:s=s.next=l,n=n.next}while(null!==n);null===s?a=s=t:s=s.next=t}else a=s=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:s,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var us=!1;function ds(){if(us){if(null!==Ia)throw Ia}}function ms(e,t,n,r){us=!1;var a=e.updateQueue;rs=!1;var s=a.firstBaseUpdate,l=a.lastBaseUpdate,i=a.shared.pending;if(null!==i){a.shared.pending=null;var o=i,c=o.next;o.next=null,null===l?s=c:l.next=c,l=o;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==l&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=o))}if(null!==s){var m=a.baseState;for(l=0,u=c=o=null,i=s;;){var h=-536870913&i.lane,f=h!==i.lane;if(f?(ic&h)===h:(r&h)===h){0!==h&&h===Ba&&(us=!0),null!==u&&(u=u.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var p=e,g=i;h=t;var x=n;switch(g.tag){case 1:if("function"==typeof(p=g.payload)){m=p.call(x,m,h);break e}m=p;break e;case 3:p.flags=-65537&p.flags|128;case 0:if(null==(h="function"==typeof(p=g.payload)?p.call(x,m,h):p))break e;m=d({},m,h);break e;case 2:rs=!0}}null!==(h=i.callback)&&(e.flags|=64,f&&(e.flags|=8192),null===(f=a.callbacks)?a.callbacks=[h]:f.push(h))}else f={lane:h,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=f,o=m):u=u.next=f,l|=h;if(null===(i=i.next)){if(null===(i=a.shared.pending))break;i=(f=i).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}null===u&&(o=m),a.baseState=o,a.firstBaseUpdate=c,a.lastBaseUpdate=u,null===s&&(a.shared.lanes=0),pc|=l,e.lanes=l,e.memoizedState=m}}function hs(e,t){if("function"!=typeof e)throw Error(r(191,e));e.call(t)}function fs(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)hs(n[e],t)}var ps=U(null),gs=U(0);function xs(e,t){q(gs,e=hc),q(ps,t),hc=e|t.baseLanes}function ys(){q(gs,hc),q(ps,ps.current)}function bs(){hc=gs.current,W(ps),W(gs)}var vs=0,ws=null,js=null,ks=null,Ns=!1,Ss=!1,Cs=!1,Ls=0,Ts=0,Es=null,_s=0;function Ps(){throw Error(r(321))}function zs(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Zn(e[n],t[n]))return!1;return!0}function As(e,t,n,r,a,s){return vs=s,ws=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,R.H=null===e||null===e.memoizedState?Ql:Kl,Cs=!1,s=n(r,a),Cs=!1,Ss&&(s=Ms(t,n,r,a)),Os(e),s}function Os(e){R.H=$l;var t=null!==js&&null!==js.next;if(vs=0,ks=js=ws=null,Ns=!1,Ts=0,Es=null,t)throw Error(r(300));null===e||Ti||null!==(e=e.dependencies)&&Ca(e)&&(Ti=!0)}function Ms(e,t,n,a){ws=e;var s=0;do{if(Ss&&(Es=null),Ts=0,Ss=!1,25<=s)throw Error(r(301));if(s+=1,ks=js=null,null!=e.updateQueue){var l=e.updateQueue;l.lastEffect=null,l.events=null,l.stores=null,null!=l.memoCache&&(l.memoCache.index=0)}R.H=Yl,l=t(n,a)}while(Ss);return l}function Rs(){var e=R.H,t=e.useState()[0];return t="function"==typeof t.then?Ws(t):t,e=e.useState()[0],(null!==js?js.memoizedState:null)!==e&&(ws.flags|=1024),t}function Ds(){var e=0!==Ls;return Ls=0,e}function Fs(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function Bs(e){if(Ns){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}Ns=!1}vs=0,ks=js=ws=null,Ss=!1,Ts=Ls=0,Es=null}function Is(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ks?ws.memoizedState=ks=e:ks=ks.next=e,ks}function Us(){if(null===js){var e=ws.alternate;e=null!==e?e.memoizedState:null}else e=js.next;var t=null===ks?ws.memoizedState:ks.next;if(null!==t)ks=t,js=e;else{if(null===e){if(null===ws.alternate)throw Error(r(467));throw Error(r(310))}e={memoizedState:(js=e).memoizedState,baseState:js.baseState,baseQueue:js.baseQueue,queue:js.queue,next:null},null===ks?ws.memoizedState=ks=e:ks=ks.next=e}return ks}function Ws(e){var t=Ts;return Ts+=1,null===Es&&(Es=[]),e=Ja(Es,e,t),t=ws,null===(null===ks?t.memoizedState:ks.next)&&(t=t.alternate,R.H=null===t||null===t.memoizedState?Ql:Kl),e}function qs(e){if(null!==e&&"object"==typeof e){if("function"==typeof e.then)return Ws(e);if(e.$$typeof===v)return Ta(e)}throw Error(r(438,String(e)))}function Hs(e){var t=null,n=ws.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=ws.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map(function(e){return e.slice()}),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},ws.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=E;return t.index++,n}function Vs(e,t){return"function"==typeof t?t(e):t}function $s(e){return Qs(Us(),js,e)}function Qs(e,t,n){var a=e.queue;if(null===a)throw Error(r(311));a.lastRenderedReducer=n;var s=e.baseQueue,l=a.pending;if(null!==l){if(null!==s){var i=s.next;s.next=l.next,l.next=i}t.baseQueue=s=l,a.pending=null}if(l=e.baseState,null===s)e.memoizedState=l;else{var o=i=null,c=null,u=t=s.next,d=!1;do{var m=-536870913&u.lane;if(m!==u.lane?(ic&m)===m:(vs&m)===m){var h=u.revertLane;if(0===h)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),m===Ba&&(d=!0);else{if((vs&h)===h){u=u.next,h===Ba&&(d=!0);continue}m={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(o=c=m,i=l):c=c.next=m,ws.lanes|=h,pc|=h}m=u.action,Cs&&n(l,m),l=u.hasEagerState?u.eagerState:n(l,m)}else h={lane:m,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(o=c=h,i=l):c=c.next=h,ws.lanes|=m,pc|=m;u=u.next}while(null!==u&&u!==t);if(null===c?i=l:c.next=o,!Zn(l,e.memoizedState)&&(Ti=!0,d&&null!==(n=Ia)))throw n;e.memoizedState=l,e.baseState=i,e.baseQueue=c,a.lastRenderedState=l}return null===s&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Ks(e){var t=Us(),n=t.queue;if(null===n)throw Error(r(311));n.lastRenderedReducer=e;var a=n.dispatch,s=n.pending,l=t.memoizedState;if(null!==s){n.pending=null;var i=s=s.next;do{l=e(l,i.action),i=i.next}while(i!==s);Zn(l,t.memoizedState)||(Ti=!0),t.memoizedState=l,null===t.baseQueue&&(t.baseState=l),n.lastRenderedState=l}return[l,a]}function Ys(e,t,n){var a=ws,s=Us(),l=ia;if(l){if(void 0===n)throw Error(r(407));n=n()}else n=t();var i=!Zn((js||s).memoizedState,n);if(i&&(s.memoizedState=n,Ti=!0),s=s.queue,yl(2048,8,Gs.bind(null,a,s,e),[e]),s.getSnapshot!==t||i||null!==ks&&1&ks.memoizedState.tag){if(a.flags|=2048,pl(9,{destroy:void 0,resource:void 0},Zs.bind(null,a,s,n,t),null),null===sc)throw Error(r(349));l||124&vs||Xs(a,t,n)}return n}function Xs(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ws.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},ws.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Zs(e,t,n,r){t.value=n,t.getSnapshot=r,Js(t)&&el(e)}function Gs(e,t,n){return n(function(){Js(t)&&el(e)})}function Js(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Zn(e,n)}catch(r){return!0}}function el(e){var t=Ar(e,2);null!==t&&Fc(t,e,2)}function tl(e){var t=Is();if("function"==typeof e){var n=e;if(e=n(),Cs){fe(!0);try{n()}finally{fe(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vs,lastRenderedState:e},t}function nl(e,t,n,r){return e.baseState=n,Qs(e,js,"function"==typeof r?r:Vs)}function rl(e,t,n,a,s){if(ql(e))throw Error(r(485));if(null!==(e=t.action)){var l={payload:s,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){l.listeners.push(e)}};null!==R.T?n(!0):l.isTransition=!1,a(l),null===(n=t.pending)?(l.next=t.pending=l,al(t,l)):(l.next=n.next,t.pending=n.next=l)}}function al(e,t){var n=t.action,r=t.payload,a=e.state;if(t.isTransition){var s=R.T,l={};R.T=l;try{var i=n(a,r),o=R.S;null!==o&&o(l,i),sl(e,t,i)}catch(c){il(e,t,c)}finally{R.T=s}}else try{sl(e,t,s=n(a,r))}catch(u){il(e,t,u)}}function sl(e,t,n){null!==n&&"object"==typeof n&&"function"==typeof n.then?n.then(function(n){ll(e,t,n)},function(n){return il(e,t,n)}):ll(e,t,n)}function ll(e,t,n){t.status="fulfilled",t.value=n,ol(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,al(e,n)))}function il(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,ol(t),t=t.next}while(t!==r)}e.action=null}function ol(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function cl(e,t){return t}function ul(e,t){if(ia){var n=sc.formState;if(null!==n){e:{var r=ws;if(ia){if(la){t:{for(var a=la,s=ca;8!==a.nodeType;){if(!s){a=null;break t}if(null===(a=bd(a.nextSibling))){a=null;break t}}a="F!"===(s=a.data)||"F"===s?a:null}if(a){la=bd(a.nextSibling),r="F!"===a.data;break e}}da(r)}r=!1}r&&(t=n[0])}}return(n=Is()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:cl,lastRenderedState:t},n.queue=r,n=Il.bind(null,ws,r),r.dispatch=n,r=tl(!1),s=Wl.bind(null,ws,!1,r.queue),a={state:t,dispatch:null,action:e,pending:null},(r=Is()).queue=a,n=rl.bind(null,ws,a,s,n),a.dispatch=n,r.memoizedState=e,[t,n,!1]}function dl(e){return ml(Us(),js,e)}function ml(e,t,n){if(t=Qs(e,t,cl)[0],e=$s(Vs)[0],"object"==typeof t&&null!==t&&"function"==typeof t.then)try{var r=Ws(t)}catch(l){if(l===Qa)throw Ya;throw l}else r=t;var a=(t=Us()).queue,s=a.dispatch;return n!==t.memoizedState&&(ws.flags|=2048,pl(9,{destroy:void 0,resource:void 0},hl.bind(null,a,n),null)),[r,s,e]}function hl(e,t){e.action=t}function fl(e){var t=Us(),n=js;if(null!==n)return ml(t,n,e);Us(),t=t.memoizedState;var r=(n=Us()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function pl(e,t,n,r){return e={tag:e,create:n,deps:r,inst:t,next:null},null===(t=ws.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},ws.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function gl(){return Us().memoizedState}function xl(e,t,n,r){var a=Is();r=void 0===r?null:r,ws.flags|=e,a.memoizedState=pl(1|t,{destroy:void 0,resource:void 0},n,r)}function yl(e,t,n,r){var a=Us();r=void 0===r?null:r;var s=a.memoizedState.inst;null!==js&&null!==r&&zs(r,js.memoizedState.deps)?a.memoizedState=pl(t,s,n,r):(ws.flags|=e,a.memoizedState=pl(1|t,s,n,r))}function bl(e,t){xl(8390656,8,e,t)}function vl(e,t){yl(2048,8,e,t)}function wl(e,t){return yl(4,2,e,t)}function jl(e,t){return yl(4,4,e,t)}function kl(e,t){if("function"==typeof t){e=e();var n=t(e);return function(){"function"==typeof n?n():t(null)}}if(null!=t)return e=e(),t.current=e,function(){t.current=null}}function Nl(e,t,n){n=null!=n?n.concat([e]):null,yl(4,4,kl.bind(null,t,e),n)}function Sl(){}function Cl(e,t){var n=Us();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&zs(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Ll(e,t){var n=Us();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&zs(t,r[1]))return r[0];if(r=e(),Cs){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r}function Tl(e,t,n){return void 0===n||1073741824&vs?e.memoizedState=t:(e.memoizedState=n,e=Dc(),ws.lanes|=e,pc|=e,n)}function El(e,t,n,r){return Zn(n,t)?n:null!==ps.current?(e=Tl(e,n,r),Zn(e,t)||(Ti=!0),e):42&vs?(e=Dc(),ws.lanes|=e,pc|=e,t):(Ti=!0,e.memoizedState=n)}function _l(e,t,n,r,a){var s=D.p;D.p=0!==s&&8>s?s:8;var l,i,o,c=R.T,u={};R.T=u,Wl(e,!1,t,n);try{var d=a(),m=R.S;if(null!==m&&m(u,d),null!==d&&"object"==typeof d&&"function"==typeof d.then)Ul(e,t,(l=r,i=[],o={status:"pending",value:null,reason:null,then:function(e){i.push(e)}},d.then(function(){o.status="fulfilled",o.value=l;for(var e=0;e<i.length;e++)(0,i[e])(l)},function(e){for(o.status="rejected",o.reason=e,e=0;e<i.length;e++)(0,i[e])(void 0)}),o),Rc());else Ul(e,t,r,Rc())}catch(h){Ul(e,t,{then:function(){},status:"rejected",reason:h},Rc())}finally{D.p=s,R.T=c}}function Pl(){}function zl(e,t,n,a){if(5!==e.tag)throw Error(r(476));var s=Al(e).queue;_l(e,s,t,F,null===n?Pl:function(){return Ol(e),n(a)})}function Al(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:F,baseState:F,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vs,lastRenderedState:F},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Vs,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function Ol(e){Ul(e,Al(e).next.queue,{},Rc())}function Ml(){return Ta(Xd)}function Rl(){return Us().memoizedState}function Dl(){return Us().memoizedState}function Fl(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Rc(),r=is(t,e=ls(n),n);return null!==r&&(Fc(r,t,n),os(r,t,n)),t={cache:Ma()},void(e.payload=t)}t=t.return}}function Bl(e,t,n){var r=Rc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},ql(e)?Hl(t,n):null!==(n=zr(e,t,n,r))&&(Fc(n,e,r),Vl(n,t,r))}function Il(e,t,n){Ul(e,t,n,Rc())}function Ul(e,t,n,r){var a={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(ql(e))Hl(t,a);else{var s=e.alternate;if(0===e.lanes&&(null===s||0===s.lanes)&&null!==(s=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=s(l,n);if(a.hasEagerState=!0,a.eagerState=i,Zn(i,l))return Pr(e,t,a,0),null===sc&&_r(),!1}catch(o){}if(null!==(n=zr(e,t,a,r)))return Fc(n,e,r),Vl(n,t,r),!0}return!1}function Wl(e,t,n,a){if(a={lane:2,revertLane:Pu(),action:a,hasEagerState:!1,eagerState:null,next:null},ql(e)){if(t)throw Error(r(479))}else null!==(t=zr(e,n,a,2))&&Fc(t,e,2)}function ql(e){var t=e.alternate;return e===ws||null!==t&&t===ws}function Hl(e,t){Ss=Ns=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Vl(e,t,n){if(4194048&n){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Ee(e,n)}}var $l={readContext:Ta,use:qs,useCallback:Ps,useContext:Ps,useEffect:Ps,useImperativeHandle:Ps,useLayoutEffect:Ps,useInsertionEffect:Ps,useMemo:Ps,useReducer:Ps,useRef:Ps,useState:Ps,useDebugValue:Ps,useDeferredValue:Ps,useTransition:Ps,useSyncExternalStore:Ps,useId:Ps,useHostTransitionStatus:Ps,useFormState:Ps,useActionState:Ps,useOptimistic:Ps,useMemoCache:Ps,useCacheRefresh:Ps},Ql={readContext:Ta,use:qs,useCallback:function(e,t){return Is().memoizedState=[e,void 0===t?null:t],e},useContext:Ta,useEffect:bl,useImperativeHandle:function(e,t,n){n=null!=n?n.concat([e]):null,xl(4194308,4,kl.bind(null,t,e),n)},useLayoutEffect:function(e,t){return xl(4194308,4,e,t)},useInsertionEffect:function(e,t){xl(4,2,e,t)},useMemo:function(e,t){var n=Is();t=void 0===t?null:t;var r=e();if(Cs){fe(!0);try{e()}finally{fe(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=Is();if(void 0!==n){var a=n(t);if(Cs){fe(!0);try{n(t)}finally{fe(!1)}}}else a=t;return r.memoizedState=r.baseState=a,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:a},r.queue=e,e=e.dispatch=Bl.bind(null,ws,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},Is().memoizedState=e},useState:function(e){var t=(e=tl(e)).queue,n=Il.bind(null,ws,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:Sl,useDeferredValue:function(e,t){return Tl(Is(),e,t)},useTransition:function(){var e=tl(!1);return e=_l.bind(null,ws,e.queue,!0,!1),Is().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var a=ws,s=Is();if(ia){if(void 0===n)throw Error(r(407));n=n()}else{if(n=t(),null===sc)throw Error(r(349));124&ic||Xs(a,t,n)}s.memoizedState=n;var l={value:n,getSnapshot:t};return s.queue=l,bl(Gs.bind(null,a,l,e),[e]),a.flags|=2048,pl(9,{destroy:void 0,resource:void 0},Zs.bind(null,a,l,n,t),null),n},useId:function(){var e=Is(),t=sc.identifierPrefix;if(ia){var n=ea;t="«"+t+"R"+(n=(Jr&~(1<<32-pe(Jr)-1)).toString(32)+n),0<(n=Ls++)&&(t+="H"+n.toString(32)),t+="»"}else t="«"+t+"r"+(n=_s++).toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Ml,useFormState:ul,useActionState:ul,useOptimistic:function(e){var t=Is();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=Wl.bind(null,ws,!0,n),n.dispatch=t,[e,t]},useMemoCache:Hs,useCacheRefresh:function(){return Is().memoizedState=Fl.bind(null,ws)}},Kl={readContext:Ta,use:qs,useCallback:Cl,useContext:Ta,useEffect:vl,useImperativeHandle:Nl,useInsertionEffect:wl,useLayoutEffect:jl,useMemo:Ll,useReducer:$s,useRef:gl,useState:function(){return $s(Vs)},useDebugValue:Sl,useDeferredValue:function(e,t){return El(Us(),js.memoizedState,e,t)},useTransition:function(){var e=$s(Vs)[0],t=Us().memoizedState;return["boolean"==typeof e?e:Ws(e),t]},useSyncExternalStore:Ys,useId:Rl,useHostTransitionStatus:Ml,useFormState:dl,useActionState:dl,useOptimistic:function(e,t){return nl(Us(),0,e,t)},useMemoCache:Hs,useCacheRefresh:Dl},Yl={readContext:Ta,use:qs,useCallback:Cl,useContext:Ta,useEffect:vl,useImperativeHandle:Nl,useInsertionEffect:wl,useLayoutEffect:jl,useMemo:Ll,useReducer:Ks,useRef:gl,useState:function(){return Ks(Vs)},useDebugValue:Sl,useDeferredValue:function(e,t){var n=Us();return null===js?Tl(n,e,t):El(n,js.memoizedState,e,t)},useTransition:function(){var e=Ks(Vs)[0],t=Us().memoizedState;return["boolean"==typeof e?e:Ws(e),t]},useSyncExternalStore:Ys,useId:Rl,useHostTransitionStatus:Ml,useFormState:fl,useActionState:fl,useOptimistic:function(e,t){var n=Us();return null!==js?nl(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])},useMemoCache:Hs,useCacheRefresh:Dl},Xl=null,Zl=0;function Gl(e){var t=Zl;return Zl+=1,null===Xl&&(Xl=[]),Ja(Xl,e,t)}function Jl(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function ei(e,t){if(t.$$typeof===m)throw Error(r(525));throw e=Object.prototype.toString.call(t),Error(r(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ti(e){return(0,e._init)(e._payload)}function ni(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function a(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function s(e,t){return(e=Ir(e,t)).index=0,e.sibling=null,e}function l(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=67108866,n):r:(t.flags|=67108866,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=67108866),t}function o(e,t,n,r){return null===t||6!==t.tag?((t=Hr(n,e.mode,r)).return=e,t):((t=s(t,n)).return=e,t)}function c(e,t,n,r){var a=n.type;return a===p?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"==typeof a&&null!==a&&a.$$typeof===C&&ti(a)===t.type)?(Jl(t=s(t,n.props),n),t.return=e,t):(Jl(t=Wr(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function u(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vr(n,e.mode,r)).return=e,t):((t=s(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=qr(n,e.mode,r,a)).return=e,t):((t=s(t,n)).return=e,t)}function m(e,t,n){if("string"==typeof t&&""!==t||"number"==typeof t||"bigint"==typeof t)return(t=Hr(""+t,e.mode,n)).return=e,t;if("object"==typeof t&&null!==t){switch(t.$$typeof){case h:return Jl(n=Wr(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case f:return(t=Vr(t,e.mode,n)).return=e,t;case C:return m(e,t=(0,t._init)(t._payload),n)}if(M(t)||z(t))return(t=qr(t,e.mode,n,null)).return=e,t;if("function"==typeof t.then)return m(e,Gl(t),n);if(t.$$typeof===v)return m(e,Ea(e,t),n);ei(e,t)}return null}function g(e,t,n,r){var a=null!==t?t.key:null;if("string"==typeof n&&""!==n||"number"==typeof n||"bigint"==typeof n)return null!==a?null:o(e,t,""+n,r);if("object"==typeof n&&null!==n){switch(n.$$typeof){case h:return n.key===a?c(e,t,n,r):null;case f:return n.key===a?u(e,t,n,r):null;case C:return g(e,t,n=(a=n._init)(n._payload),r)}if(M(n)||z(n))return null!==a?null:d(e,t,n,r,null);if("function"==typeof n.then)return g(e,t,Gl(n),r);if(n.$$typeof===v)return g(e,t,Ea(e,n),r);ei(e,n)}return null}function x(e,t,n,r,a){if("string"==typeof r&&""!==r||"number"==typeof r||"bigint"==typeof r)return o(t,e=e.get(n)||null,""+r,a);if("object"==typeof r&&null!==r){switch(r.$$typeof){case h:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case f:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case C:return x(e,t,n,r=(0,r._init)(r._payload),a)}if(M(r)||z(r))return d(t,e=e.get(n)||null,r,a,null);if("function"==typeof r.then)return x(e,t,n,Gl(r),a);if(r.$$typeof===v)return x(e,t,n,Ea(t,r),a);ei(t,r)}return null}function y(o,c,u,d){if("object"==typeof u&&null!==u&&u.type===p&&null===u.key&&(u=u.props.children),"object"==typeof u&&null!==u){switch(u.$$typeof){case h:e:{for(var b=u.key;null!==c;){if(c.key===b){if((b=u.type)===p){if(7===c.tag){n(o,c.sibling),(d=s(c,u.props.children)).return=o,o=d;break e}}else if(c.elementType===b||"object"==typeof b&&null!==b&&b.$$typeof===C&&ti(b)===c.type){n(o,c.sibling),Jl(d=s(c,u.props),u),d.return=o,o=d;break e}n(o,c);break}t(o,c),c=c.sibling}u.type===p?((d=qr(u.props.children,o.mode,d,u.key)).return=o,o=d):(Jl(d=Wr(u.type,u.key,u.props,null,o.mode,d),u),d.return=o,o=d)}return i(o);case f:e:{for(b=u.key;null!==c;){if(c.key===b){if(4===c.tag&&c.stateNode.containerInfo===u.containerInfo&&c.stateNode.implementation===u.implementation){n(o,c.sibling),(d=s(c,u.children||[])).return=o,o=d;break e}n(o,c);break}t(o,c),c=c.sibling}(d=Vr(u,o.mode,d)).return=o,o=d}return i(o);case C:return y(o,c,u=(b=u._init)(u._payload),d)}if(M(u))return function(r,s,i,o){for(var c=null,u=null,d=s,h=s=0,f=null;null!==d&&h<i.length;h++){d.index>h?(f=d,d=null):f=d.sibling;var p=g(r,d,i[h],o);if(null===p){null===d&&(d=f);break}e&&d&&null===p.alternate&&t(r,d),s=l(p,s,h),null===u?c=p:u.sibling=p,u=p,d=f}if(h===i.length)return n(r,d),ia&&ta(r,h),c;if(null===d){for(;h<i.length;h++)null!==(d=m(r,i[h],o))&&(s=l(d,s,h),null===u?c=d:u.sibling=d,u=d);return ia&&ta(r,h),c}for(d=a(d);h<i.length;h++)null!==(f=x(d,r,h,i[h],o))&&(e&&null!==f.alternate&&d.delete(null===f.key?h:f.key),s=l(f,s,h),null===u?c=f:u.sibling=f,u=f);return e&&d.forEach(function(e){return t(r,e)}),ia&&ta(r,h),c}(o,c,u,d);if(z(u)){if("function"!=typeof(b=z(u)))throw Error(r(150));return function(s,i,o,c){if(null==o)throw Error(r(151));for(var u=null,d=null,h=i,f=i=0,p=null,y=o.next();null!==h&&!y.done;f++,y=o.next()){h.index>f?(p=h,h=null):p=h.sibling;var b=g(s,h,y.value,c);if(null===b){null===h&&(h=p);break}e&&h&&null===b.alternate&&t(s,h),i=l(b,i,f),null===d?u=b:d.sibling=b,d=b,h=p}if(y.done)return n(s,h),ia&&ta(s,f),u;if(null===h){for(;!y.done;f++,y=o.next())null!==(y=m(s,y.value,c))&&(i=l(y,i,f),null===d?u=y:d.sibling=y,d=y);return ia&&ta(s,f),u}for(h=a(h);!y.done;f++,y=o.next())null!==(y=x(h,s,f,y.value,c))&&(e&&null!==y.alternate&&h.delete(null===y.key?f:y.key),i=l(y,i,f),null===d?u=y:d.sibling=y,d=y);return e&&h.forEach(function(e){return t(s,e)}),ia&&ta(s,f),u}(o,c,u=b.call(u),d)}if("function"==typeof u.then)return y(o,c,Gl(u),d);if(u.$$typeof===v)return y(o,c,Ea(o,u),d);ei(o,u)}return"string"==typeof u&&""!==u||"number"==typeof u||"bigint"==typeof u?(u=""+u,null!==c&&6===c.tag?(n(o,c.sibling),(d=s(c,u)).return=o,o=d):(n(o,c),(d=Hr(u,o.mode,d)).return=o,o=d),i(o)):n(o,c)}return function(e,t,n,r){try{Zl=0;var a=y(e,t,n,r);return Xl=null,a}catch(l){if(l===Qa||l===Ya)throw l;var s=Fr(29,l,null,e.mode);return s.lanes=r,s.return=e,s}}}var ri=ni(!0),ai=ni(!1),si=U(null),li=null;function ii(e){var t=e.alternate;q(di,1&di.current),q(si,e),null===li&&(null===t||null!==ps.current||null!==t.memoizedState)&&(li=e)}function oi(e){if(22===e.tag){if(q(di,di.current),q(si,e),null===li){var t=e.alternate;null!==t&&null!==t.memoizedState&&(li=e)}}else ci()}function ci(){q(di,di.current),q(si,si.current)}function ui(e){W(si),li===e&&(li=null),W(di)}var di=U(0);function mi(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||yd(n)))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(128&t.flags)return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function hi(e,t,n,r){n=null==(n=n(r,t=e.memoizedState))?t:d({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var fi={enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Rc(),a=ls(r);a.payload=t,null!=n&&(a.callback=n),null!==(t=is(e,a,r))&&(Fc(t,e,r),os(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Rc(),a=ls(r);a.tag=1,a.payload=t,null!=n&&(a.callback=n),null!==(t=is(e,a,r))&&(Fc(t,e,r),os(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Rc(),r=ls(n);r.tag=2,null!=t&&(r.callback=t),null!==(t=is(e,r,n))&&(Fc(t,e,n),os(t,e,n))}};function pi(e,t,n,r,a,s,l){return"function"==typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,s,l):!t.prototype||!t.prototype.isPureReactComponent||(!Gn(n,r)||!Gn(a,s))}function gi(e,t,n,r){e=t.state,"function"==typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"==typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&fi.enqueueReplaceState(t,t.state,null)}function xi(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var a in n===t&&(n=d({},n)),e)void 0===n[a]&&(n[a]=e[a]);return n}var yi="function"==typeof reportError?reportError:function(e){if("object"==typeof window&&"function"==typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"==typeof e&&null!==e&&"string"==typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"==typeof process&&"function"==typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function bi(e){yi(e)}function vi(e){console.error(e)}function wi(e){yi(e)}function ji(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout(function(){throw n})}}function ki(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout(function(){throw r})}}function Ni(e,t,n){return(n=ls(n)).tag=3,n.payload={element:null},n.callback=function(){ji(e,t)},n}function Si(e){return(e=ls(e)).tag=3,e}function Ci(e,t,n,r){var a=n.type.getDerivedStateFromError;if("function"==typeof a){var s=r.value;e.payload=function(){return a(s)},e.callback=function(){ki(t,n,r)}}var l=n.stateNode;null!==l&&"function"==typeof l.componentDidCatch&&(e.callback=function(){ki(t,n,r),"function"!=typeof a&&(null===Cc?Cc=new Set([this]):Cc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Li=Error(r(461)),Ti=!1;function Ei(e,t,n,r){t.child=null===e?ai(t,null,n,r):ri(t,e.child,n,r)}function _i(e,t,n,r,a){n=n.render;var s=t.ref;if("ref"in r){var l={};for(var i in r)"ref"!==i&&(l[i]=r[i])}else l=r;return La(t),r=As(e,t,n,l,s,a),i=Ds(),null===e||Ti?(ia&&i&&ra(t),t.flags|=1,Ei(e,t,r,a),t.child):(Fs(e,t,a),Xi(e,t,a))}function Pi(e,t,n,r,a){if(null===e){var s=n.type;return"function"!=typeof s||Br(s)||void 0!==s.defaultProps||null!==n.compare?((e=Wr(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=s,zi(e,t,s,r,a))}if(s=e.child,!Zi(e,a)){var l=s.memoizedProps;if((n=null!==(n=n.compare)?n:Gn)(l,r)&&e.ref===t.ref)return Xi(e,t,a)}return t.flags|=1,(e=Ir(s,r)).ref=t.ref,e.return=t,t.child=e}function zi(e,t,n,r,a){if(null!==e){var s=e.memoizedProps;if(Gn(s,r)&&e.ref===t.ref){if(Ti=!1,t.pendingProps=r=s,!Zi(e,a))return t.lanes=e.lanes,Xi(e,t,a);131072&e.flags&&(Ti=!0)}}return Ri(e,t,n,r,a)}function Ai(e,t,n){var r=t.pendingProps,a=r.children,s=null!==e?e.memoizedState:null;if("hidden"===r.mode){if(128&t.flags){if(r=null!==s?s.baseLanes|n:n,null!==e){for(a=t.child=e.child,s=0;null!==a;)s=s|a.lanes|a.childLanes,a=a.sibling;t.childLanes=s&~r}else t.childLanes=0,t.child=null;return Oi(e,t,r,n)}if(!(536870912&n))return t.lanes=t.childLanes=536870912,Oi(e,t,null!==s?s.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Va(0,null!==s?s.cachePool:null),null!==s?xs(t,s):ys(),oi(t)}else null!==s?(Va(0,s.cachePool),xs(t,s),ci(),t.memoizedState=null):(null!==e&&Va(0,null),ys(),ci());return Ei(e,t,a,n),t.child}function Oi(e,t,n,r){var a=Ha();return a=null===a?null:{parent:Oa._currentValue,pool:a},t.memoizedState={baseLanes:n,cachePool:a},null!==e&&Va(0,null),ys(),oi(t),null!==e&&Sa(e,t,r,!0),null}function Mi(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=4194816);else{if("function"!=typeof n&&"object"!=typeof n)throw Error(r(284));null!==e&&e.ref===n||(t.flags|=4194816)}}function Ri(e,t,n,r,a){return La(t),n=As(e,t,n,r,void 0,a),r=Ds(),null===e||Ti?(ia&&r&&ra(t),t.flags|=1,Ei(e,t,n,a),t.child):(Fs(e,t,a),Xi(e,t,a))}function Di(e,t,n,r,a,s){return La(t),t.updateQueue=null,n=Ms(t,r,n,a),Os(e),r=Ds(),null===e||Ti?(ia&&r&&ra(t),t.flags|=1,Ei(e,t,n,s),t.child):(Fs(e,t,s),Xi(e,t,s))}function Fi(e,t,n,r,a){if(La(t),null===t.stateNode){var s=Rr,l=n.contextType;"object"==typeof l&&null!==l&&(s=Ta(l)),s=new n(r,s),t.memoizedState=null!==s.state&&void 0!==s.state?s.state:null,s.updater=fi,t.stateNode=s,s._reactInternals=t,(s=t.stateNode).props=r,s.state=t.memoizedState,s.refs={},as(t),l=n.contextType,s.context="object"==typeof l&&null!==l?Ta(l):Rr,s.state=t.memoizedState,"function"==typeof(l=n.getDerivedStateFromProps)&&(hi(t,n,l,r),s.state=t.memoizedState),"function"==typeof n.getDerivedStateFromProps||"function"==typeof s.getSnapshotBeforeUpdate||"function"!=typeof s.UNSAFE_componentWillMount&&"function"!=typeof s.componentWillMount||(l=s.state,"function"==typeof s.componentWillMount&&s.componentWillMount(),"function"==typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount(),l!==s.state&&fi.enqueueReplaceState(s,s.state,null),ms(t,r,s,a),ds(),s.state=t.memoizedState),"function"==typeof s.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){s=t.stateNode;var i=t.memoizedProps,o=xi(n,i);s.props=o;var c=s.context,u=n.contextType;l=Rr,"object"==typeof u&&null!==u&&(l=Ta(u));var d=n.getDerivedStateFromProps;u="function"==typeof d||"function"==typeof s.getSnapshotBeforeUpdate,i=t.pendingProps!==i,u||"function"!=typeof s.UNSAFE_componentWillReceiveProps&&"function"!=typeof s.componentWillReceiveProps||(i||c!==l)&&gi(t,s,r,l),rs=!1;var m=t.memoizedState;s.state=m,ms(t,r,s,a),ds(),c=t.memoizedState,i||m!==c||rs?("function"==typeof d&&(hi(t,n,d,r),c=t.memoizedState),(o=rs||pi(t,n,o,r,m,c,l))?(u||"function"!=typeof s.UNSAFE_componentWillMount&&"function"!=typeof s.componentWillMount||("function"==typeof s.componentWillMount&&s.componentWillMount(),"function"==typeof s.UNSAFE_componentWillMount&&s.UNSAFE_componentWillMount()),"function"==typeof s.componentDidMount&&(t.flags|=4194308)):("function"==typeof s.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),s.props=r,s.state=c,s.context=l,r=o):("function"==typeof s.componentDidMount&&(t.flags|=4194308),r=!1)}else{s=t.stateNode,ss(e,t),u=xi(n,l=t.memoizedProps),s.props=u,d=t.pendingProps,m=s.context,c=n.contextType,o=Rr,"object"==typeof c&&null!==c&&(o=Ta(c)),(c="function"==typeof(i=n.getDerivedStateFromProps)||"function"==typeof s.getSnapshotBeforeUpdate)||"function"!=typeof s.UNSAFE_componentWillReceiveProps&&"function"!=typeof s.componentWillReceiveProps||(l!==d||m!==o)&&gi(t,s,r,o),rs=!1,m=t.memoizedState,s.state=m,ms(t,r,s,a),ds();var h=t.memoizedState;l!==d||m!==h||rs||null!==e&&null!==e.dependencies&&Ca(e.dependencies)?("function"==typeof i&&(hi(t,n,i,r),h=t.memoizedState),(u=rs||pi(t,n,u,r,m,h,o)||null!==e&&null!==e.dependencies&&Ca(e.dependencies))?(c||"function"!=typeof s.UNSAFE_componentWillUpdate&&"function"!=typeof s.componentWillUpdate||("function"==typeof s.componentWillUpdate&&s.componentWillUpdate(r,h,o),"function"==typeof s.UNSAFE_componentWillUpdate&&s.UNSAFE_componentWillUpdate(r,h,o)),"function"==typeof s.componentDidUpdate&&(t.flags|=4),"function"==typeof s.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!=typeof s.componentDidUpdate||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),"function"!=typeof s.getSnapshotBeforeUpdate||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),s.props=r,s.state=h,s.context=o,r=u):("function"!=typeof s.componentDidUpdate||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=4),"function"!=typeof s.getSnapshotBeforeUpdate||l===e.memoizedProps&&m===e.memoizedState||(t.flags|=1024),r=!1)}return s=r,Mi(e,t),r=!!(128&t.flags),s||r?(s=t.stateNode,n=r&&"function"!=typeof n.getDerivedStateFromError?null:s.render(),t.flags|=1,null!==e&&r?(t.child=ri(t,e.child,null,a),t.child=ri(t,null,n,a)):Ei(e,t,n,a),t.memoizedState=s.state,e=t.child):e=Xi(e,t,a),e}function Bi(e,t,n,r){return pa(),t.flags|=256,Ei(e,t,n,r),t.child}var Ii={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ui(e){return{baseLanes:e,cachePool:$a()}}function Wi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=yc),e}function qi(e,t,n){var a,s=t.pendingProps,l=!1,i=!!(128&t.flags);if((a=i)||(a=(null===e||null!==e.memoizedState)&&!!(2&di.current)),a&&(l=!0,t.flags&=-129),a=!!(32&t.flags),t.flags&=-33,null===e){if(ia){if(l?ii(t):ci(),ia){var o,c=la;if(o=c){e:{for(o=c,c=ca;8!==o.nodeType;){if(!c){c=null;break e}if(null===(o=bd(o.nextSibling))){c=null;break e}}c=o}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Gr?{id:Jr,overflow:ea}:null,retryLane:536870912,hydrationErrors:null},(o=Fr(18,null,null,0)).stateNode=c,o.return=t,t.child=o,sa=t,la=null,o=!0):o=!1}o||da(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return yd(c)?t.lanes=32:t.lanes=536870912,null;ui(t)}return c=s.children,s=s.fallback,l?(ci(),c=Vi({mode:"hidden",children:c},l=t.mode),s=qr(s,l,n,null),c.return=t,s.return=t,c.sibling=s,t.child=c,(l=t.child).memoizedState=Ui(n),l.childLanes=Wi(e,a,n),t.memoizedState=Ii,s):(ii(t),Hi(t,c))}if(null!==(o=e.memoizedState)&&null!==(c=o.dehydrated)){if(i)256&t.flags?(ii(t),t.flags&=-257,t=$i(e,t,n)):null!==t.memoizedState?(ci(),t.child=e.child,t.flags|=128,t=null):(ci(),l=s.fallback,c=t.mode,s=Vi({mode:"visible",children:s.children},c),(l=qr(l,c,n,null)).flags|=2,s.return=t,l.return=t,s.sibling=l,t.child=s,ri(t,e.child,null,n),(s=t.child).memoizedState=Ui(n),s.childLanes=Wi(e,a,n),t.memoizedState=Ii,t=l);else if(ii(t),yd(c)){if(a=c.nextSibling&&c.nextSibling.dataset)var u=a.dgst;a=u,(s=Error(r(419))).stack="",s.digest=a,xa({value:s,source:null,stack:null}),t=$i(e,t,n)}else if(Ti||Sa(e,t,n,!1),a=0!==(n&e.childLanes),Ti||a){if(null!==(a=sc)&&(0!==(s=0!==((s=42&(s=n&-n)?1:_e(s))&(a.suspendedLanes|n))?0:s)&&s!==o.retryLane))throw o.retryLane=s,Ar(e,s),Fc(a,e,s),Li;"$?"===c.data||Yc(),t=$i(e,t,n)}else"$?"===c.data?(t.flags|=192,t.child=e.child,t=null):(e=o.treeContext,la=bd(c.nextSibling),sa=t,ia=!0,oa=null,ca=!1,null!==e&&(Xr[Zr++]=Jr,Xr[Zr++]=ea,Xr[Zr++]=Gr,Jr=e.id,ea=e.overflow,Gr=t),(t=Hi(t,s.children)).flags|=4096);return t}return l?(ci(),l=s.fallback,c=t.mode,u=(o=e.child).sibling,(s=Ir(o,{mode:"hidden",children:s.children})).subtreeFlags=65011712&o.subtreeFlags,null!==u?l=Ir(u,l):(l=qr(l,c,n,null)).flags|=2,l.return=t,s.return=t,s.sibling=l,t.child=s,s=l,l=t.child,null===(c=e.child.memoizedState)?c=Ui(n):(null!==(o=c.cachePool)?(u=Oa._currentValue,o=o.parent!==u?{parent:u,pool:u}:o):o=$a(),c={baseLanes:c.baseLanes|n,cachePool:o}),l.memoizedState=c,l.childLanes=Wi(e,a,n),t.memoizedState=Ii,s):(ii(t),e=(n=e.child).sibling,(n=Ir(n,{mode:"visible",children:s.children})).return=t,n.sibling=null,null!==e&&(null===(a=t.deletions)?(t.deletions=[e],t.flags|=16):a.push(e)),t.child=n,t.memoizedState=null,n)}function Hi(e,t){return(t=Vi({mode:"visible",children:t},e.mode)).return=e,e.child=t}function Vi(e,t){return(e=Fr(22,e,null,t)).lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function $i(e,t,n){return ri(t,e.child,null,n),(e=Hi(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Qi(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),ka(e.return,t,n)}function Ki(e,t,n,r,a){var s=e.memoizedState;null===s?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=a)}function Yi(e,t,n){var r=t.pendingProps,a=r.revealOrder,s=r.tail;if(Ei(e,t,r.children,n),2&(r=di.current))r=1&r|2,t.flags|=128;else{if(null!==e&&128&e.flags)e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Qi(e,n,t);else if(19===e.tag)Qi(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(q(di,r),a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===mi(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Ki(t,!1,a,n,s);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===mi(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Ki(t,!0,n,null,s);break;case"together":Ki(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Xi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),pc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(Sa(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(r(153));if(null!==t.child){for(n=Ir(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ir(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function Zi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Ca(e))}function Gi(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Ti=!0;else{if(!(Zi(e,n)||128&t.flags))return Ti=!1,function(e,t,n){switch(t.tag){case 3:K(t,t.stateNode.containerInfo),wa(0,Oa,e.memoizedState.cache),pa();break;case 27:case 5:X(t);break;case 4:K(t,t.stateNode.containerInfo);break;case 10:wa(0,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(ii(t),t.flags|=128,null):0!==(n&t.child.childLanes)?qi(e,t,n):(ii(t),null!==(e=Xi(e,t,n))?e.sibling:null);ii(t);break;case 19:var a=!!(128&e.flags);if((r=0!==(n&t.childLanes))||(Sa(e,t,n,!1),r=0!==(n&t.childLanes)),a){if(r)return Yi(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),q(di,di.current),r)break;return null;case 22:case 23:return t.lanes=0,Ai(e,t,n);case 24:wa(0,Oa,e.memoizedState.cache)}return Xi(e,t,n)}(e,t,n);Ti=!!(131072&e.flags)}else Ti=!1,ia&&1048576&t.flags&&na(t,Yr,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,s=a._init;if(a=s(a._payload),t.type=a,"function"!=typeof a){if(null!=a){if((s=a.$$typeof)===w){t.tag=11,t=_i(null,t,a,e,n);break e}if(s===S){t.tag=14,t=Pi(null,t,a,e,n);break e}}throw t=O(a)||a,Error(r(306,t,""))}Br(a)?(e=xi(a,e),t.tag=1,t=Fi(null,t,a,e,n)):(t.tag=0,t=Ri(null,t,a,e,n))}return t;case 0:return Ri(e,t,t.type,t.pendingProps,n);case 1:return Fi(e,t,a=t.type,s=xi(a,t.pendingProps),n);case 3:e:{if(K(t,t.stateNode.containerInfo),null===e)throw Error(r(387));a=t.pendingProps;var l=t.memoizedState;s=l.element,ss(e,t),ms(t,a,null,n);var i=t.memoizedState;if(a=i.cache,wa(0,Oa,a),a!==l.cache&&Na(t,[Oa],n,!0),ds(),a=i.element,l.isDehydrated){if(l={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=l,t.memoizedState=l,256&t.flags){t=Bi(e,t,a,n);break e}if(a!==s){xa(s=Cr(Error(r(424)),t)),t=Bi(e,t,a,n);break e}if(9===(e=t.stateNode.containerInfo).nodeType)e=e.body;else e="HTML"===e.nodeName?e.ownerDocument.body:e;for(la=bd(e.firstChild),sa=t,ia=!0,oa=null,ca=!0,n=ai(t,null,a,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(pa(),a===s){t=Xi(e,t,n);break e}Ei(e,t,a,n)}t=t.child}return t;case 26:return Mi(e,t),null===e?(n=_d(t.type,null,t.pendingProps,null))?t.memoizedState=n:ia||(n=t.type,e=t.pendingProps,(a=sd($.current).createElement(n))[Oe]=t,a[Me]=e,nd(a,n,e),Qe(a),t.stateNode=a):t.memoizedState=_d(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return X(t),null===e&&ia&&(a=t.stateNode=jd(t.type,t.pendingProps,$.current),sa=t,ca=!0,s=la,pd(t.type)?(vd=s,la=bd(a.firstChild)):la=s),Ei(e,t,t.pendingProps.children,n),Mi(e,t),null===e&&(t.flags|=4194304),t.child;case 5:return null===e&&ia&&((s=a=la)&&(null!==(a=function(e,t,n,r){for(;1===e.nodeType;){var a=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[Ue])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(s=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(s!==a.rel||e.getAttribute("href")!==(null==a.href||""===a.href?null:a.href)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin)||e.getAttribute("title")!==(null==a.title?null:a.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((s=e.getAttribute("src"))!==(null==a.src?null:a.src)||e.getAttribute("type")!==(null==a.type?null:a.type)||e.getAttribute("crossorigin")!==(null==a.crossOrigin?null:a.crossOrigin))&&s&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var s=null==a.name?null:""+a.name;if("hidden"===a.type&&e.getAttribute("name")===s)return e}if(null===(e=bd(e.nextSibling)))break}return null}(a,t.type,t.pendingProps,ca))?(t.stateNode=a,sa=t,la=bd(a.firstChild),ca=!1,s=!0):s=!1),s||da(t)),X(t),s=t.type,l=t.pendingProps,i=null!==e?e.memoizedProps:null,a=l.children,od(s,l)?a=null:null!==i&&od(s,i)&&(t.flags|=32),null!==t.memoizedState&&(s=As(e,t,Rs,null,null,n),Xd._currentValue=s),Mi(e,t),Ei(e,t,a,n),t.child;case 6:return null===e&&ia&&((e=n=la)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=bd(e.nextSibling)))return null}return e}(n,t.pendingProps,ca))?(t.stateNode=n,sa=t,la=null,e=!0):e=!1),e||da(t)),null;case 13:return qi(e,t,n);case 4:return K(t,t.stateNode.containerInfo),a=t.pendingProps,null===e?t.child=ri(t,null,a,n):Ei(e,t,a,n),t.child;case 11:return _i(e,t,t.type,t.pendingProps,n);case 7:return Ei(e,t,t.pendingProps,n),t.child;case 8:case 12:return Ei(e,t,t.pendingProps.children,n),t.child;case 10:return a=t.pendingProps,wa(0,t.type,a.value),Ei(e,t,a.children,n),t.child;case 9:return s=t.type._context,a=t.pendingProps.children,La(t),a=a(s=Ta(s)),t.flags|=1,Ei(e,t,a,n),t.child;case 14:return Pi(e,t,t.type,t.pendingProps,n);case 15:return zi(e,t,t.type,t.pendingProps,n);case 19:return Yi(e,t,n);case 31:return a=t.pendingProps,n=t.mode,a={mode:a.mode,children:a.children},null===e?((n=Vi(a,n)).ref=t.ref,t.child=n,n.return=t,t=n):((n=Ir(e.child,a)).ref=t.ref,t.child=n,n.return=t,t=n),t;case 22:return Ai(e,t,n);case 24:return La(t),a=Ta(Oa),null===e?(null===(s=Ha())&&(s=sc,l=Ma(),s.pooledCache=l,l.refCount++,null!==l&&(s.pooledCacheLanes|=n),s=l),t.memoizedState={parent:a,cache:s},as(t),wa(0,Oa,s)):(0!==(e.lanes&n)&&(ss(e,t),ms(t,null,null,n),ds()),s=e.memoizedState,l=t.memoizedState,s.parent!==a?(s={parent:a,cache:a},t.memoizedState=s,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=s),wa(0,Oa,a)):(a=l.cache,wa(0,Oa,a),a!==s.cache&&Na(t,[Oa],n,!0))),Ei(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(r(156,t.tag))}function Ji(e){e.flags|=4}function eo(e,t){if("stylesheet"!==t.type||4&t.state.loading)e.flags&=-16777217;else if(e.flags|=16777216,!qd(t)){if(null!==(t=si.current)&&((4194048&ic)===ic?null!==li:(62914560&ic)!==ic&&!(536870912&ic)||t!==li))throw es=Xa,Ka;e.flags|=8192}}function to(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Se():536870912,e.lanes|=t,bc|=t)}function no(e,t){if(!ia)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function ro(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=65011712&a.subtreeFlags,r|=65011712&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function ao(e,t,n){var a=t.pendingProps;switch(aa(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return ro(t),null;case 3:return n=t.stateNode,a=null,null!==e&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),ja(Oa),Y(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(fa(t)?Ji(t):null===e||e.memoizedState.isDehydrated&&!(256&t.flags)||(t.flags|=1024,ga())),ro(t),null;case 26:return n=t.memoizedState,null===e?(Ji(t),null!==n?(ro(t),eo(t,n)):(ro(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Ji(t),ro(t),eo(t,n)):(ro(t),t.flags&=-16777217):(e.memoizedProps!==a&&Ji(t),ro(t),t.flags&=-16777217),null;case 27:Z(t),n=$.current;var s=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==a&&Ji(t);else{if(!a){if(null===t.stateNode)throw Error(r(166));return ro(t),null}e=H.current,fa(t)?ma(t):(e=jd(s,a,n),t.stateNode=e,Ji(t))}return ro(t),null;case 5:if(Z(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==a&&Ji(t);else{if(!a){if(null===t.stateNode)throw Error(r(166));return ro(t),null}if(e=H.current,fa(t))ma(t);else{switch(s=sd($.current),e){case 1:e=s.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=s.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=s.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=s.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"==typeof a.is?s.createElement("select",{is:a.is}):s.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e="string"==typeof a.is?s.createElement(n,{is:a.is}):s.createElement(n)}}e[Oe]=t,e[Me]=a;e:for(s=t.child;null!==s;){if(5===s.tag||6===s.tag)e.appendChild(s.stateNode);else if(4!==s.tag&&27!==s.tag&&null!==s.child){s.child.return=s,s=s.child;continue}if(s===t)break e;for(;null===s.sibling;){if(null===s.return||s.return===t)break e;s=s.return}s.sibling.return=s.return,s=s.sibling}t.stateNode=e;e:switch(nd(e,n,a),n){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ji(t)}}return ro(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==a&&Ji(t);else{if("string"!=typeof a&&null===t.stateNode)throw Error(r(166));if(e=$.current,fa(t)){if(e=t.stateNode,n=t.memoizedProps,a=null,null!==(s=sa))switch(s.tag){case 27:case 5:a=s.memoizedProps}e[Oe]=t,(e=!!(e.nodeValue===n||null!==a&&!0===a.suppressHydrationWarning||Gu(e.nodeValue,n)))||da(t)}else(e=sd(e).createTextNode(a))[Oe]=t,t.stateNode=e}return ro(t),null;case 13:if(a=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(s=fa(t),null!==a&&null!==a.dehydrated){if(null===e){if(!s)throw Error(r(318));if(!(s=null!==(s=t.memoizedState)?s.dehydrated:null))throw Error(r(317));s[Oe]=t}else pa(),!(128&t.flags)&&(t.memoizedState=null),t.flags|=4;ro(t),s=!1}else s=ga(),null!==e&&null!==e.memoizedState&&(e.memoizedState.hydrationErrors=s),s=!0;if(!s)return 256&t.flags?(ui(t),t):(ui(t),null)}if(ui(t),128&t.flags)return t.lanes=n,t;if(n=null!==a,e=null!==e&&null!==e.memoizedState,n){s=null,null!==(a=t.child).alternate&&null!==a.alternate.memoizedState&&null!==a.alternate.memoizedState.cachePool&&(s=a.alternate.memoizedState.cachePool.pool);var l=null;null!==a.memoizedState&&null!==a.memoizedState.cachePool&&(l=a.memoizedState.cachePool.pool),l!==s&&(a.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),to(t,t.updateQueue),ro(t),null;case 4:return Y(),null===e&&Wu(t.stateNode.containerInfo),ro(t),null;case 10:return ja(t.type),ro(t),null;case 19:if(W(di),null===(s=t.memoizedState))return ro(t),null;if(a=!!(128&t.flags),null===(l=s.rendering))if(a)no(s,!1);else{if(0!==fc||null!==e&&128&e.flags)for(e=t.child;null!==e;){if(null!==(l=mi(e))){for(t.flags|=128,no(s,!1),e=l.updateQueue,t.updateQueue=e,to(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Ur(n,e),n=n.sibling;return q(di,1&di.current|2),t.child}e=e.sibling}null!==s.tail&&re()>Nc&&(t.flags|=128,a=!0,no(s,!1),t.lanes=4194304)}else{if(!a)if(null!==(e=mi(l))){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,to(t,e),no(s,!0),null===s.tail&&"hidden"===s.tailMode&&!l.alternate&&!ia)return ro(t),null}else 2*re()-s.renderingStartTime>Nc&&536870912!==n&&(t.flags|=128,a=!0,no(s,!1),t.lanes=4194304);s.isBackwards?(l.sibling=t.child,t.child=l):(null!==(e=s.last)?e.sibling=l:t.child=l,s.last=l)}return null!==s.tail?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=re(),t.sibling=null,e=di.current,q(di,a?1&e|2:1&e),t):(ro(t),null);case 22:case 23:return ui(t),bs(),a=null!==t.memoizedState,null!==e?null!==e.memoizedState!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?!!(536870912&n)&&!(128&t.flags)&&(ro(t),6&t.subtreeFlags&&(t.flags|=8192)):ro(t),null!==(n=t.updateQueue)&&to(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),a=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(a=t.memoizedState.cachePool.pool),a!==n&&(t.flags|=2048),null!==e&&W(qa),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),ja(Oa),ro(t),null;case 25:case 30:return null}throw Error(r(156,t.tag))}function so(e,t){switch(aa(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return ja(Oa),Y(),65536&(e=t.flags)&&!(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return Z(t),null;case 13:if(ui(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(r(340));pa()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return W(di),null;case 4:return Y(),null;case 10:return ja(t.type),null;case 22:case 23:return ui(t),bs(),null!==e&&W(qa),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return ja(Oa),null;default:return null}}function lo(e,t){switch(aa(t),t.tag){case 3:ja(Oa),Y();break;case 26:case 27:case 5:Z(t);break;case 4:Y();break;case 13:ui(t);break;case 19:W(di);break;case 10:ja(t.type);break;case 22:case 23:ui(t),bs(),null!==e&&W(qa);break;case 24:ja(Oa)}}function io(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var a=r.next;n=a;do{if((n.tag&e)===e){r=void 0;var s=n.create,l=n.inst;r=s(),l.destroy=r}n=n.next}while(n!==a)}}catch(i){mu(t,t.return,i)}}function oo(e,t,n){try{var r=t.updateQueue,a=null!==r?r.lastEffect:null;if(null!==a){var s=a.next;r=s;do{if((r.tag&e)===e){var l=r.inst,i=l.destroy;if(void 0!==i){l.destroy=void 0,a=t;var o=n,c=i;try{c()}catch(u){mu(a,o,u)}}}r=r.next}while(r!==s)}}catch(u){mu(t,t.return,u)}}function co(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{fs(t,n)}catch(r){mu(e,e.return,r)}}}function uo(e,t,n){n.props=xi(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){mu(e,t,r)}}function mo(e,t){try{var n=e.ref;if(null!==n){switch(e.tag){case 26:case 27:case 5:var r=e.stateNode;break;default:r=e.stateNode}"function"==typeof n?e.refCleanup=n(r):n.current=r}}catch(a){mu(e,t,a)}}function ho(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"==typeof r)try{r()}catch(a){mu(e,t,a)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"==typeof n)try{n(null)}catch(s){mu(e,t,s)}else n.current=null}function fo(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(a){mu(e,e.return,a)}}function po(e,t,n){try{var a=e.stateNode;!function(e,t,n,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var s=null,l=null,i=null,o=null,c=null,u=null,d=null;for(f in n){var m=n[f];if(n.hasOwnProperty(f)&&null!=m)switch(f){case"checked":case"value":break;case"defaultValue":c=m;default:a.hasOwnProperty(f)||ed(e,t,f,null,a,m)}}for(var h in a){var f=a[h];if(m=n[h],a.hasOwnProperty(h)&&(null!=f||null!=m))switch(h){case"type":l=f;break;case"name":s=f;break;case"checked":u=f;break;case"defaultChecked":d=f;break;case"value":i=f;break;case"defaultValue":o=f;break;case"children":case"dangerouslySetInnerHTML":if(null!=f)throw Error(r(137,t));break;default:f!==m&&ed(e,t,h,f,a,m)}}return void yt(e,i,o,c,u,d,l,s);case"select":for(l in f=i=o=h=null,n)if(c=n[l],n.hasOwnProperty(l)&&null!=c)switch(l){case"value":break;case"multiple":f=c;default:a.hasOwnProperty(l)||ed(e,t,l,null,a,c)}for(s in a)if(l=a[s],c=n[s],a.hasOwnProperty(s)&&(null!=l||null!=c))switch(s){case"value":h=l;break;case"defaultValue":o=l;break;case"multiple":i=l;default:l!==c&&ed(e,t,s,l,a,c)}return t=o,n=i,a=f,void(null!=h?wt(e,!!n,h,!1):!!a!=!!n&&(null!=t?wt(e,!!n,t,!0):wt(e,!!n,n?[]:"",!1)));case"textarea":for(o in f=h=null,n)if(s=n[o],n.hasOwnProperty(o)&&null!=s&&!a.hasOwnProperty(o))switch(o){case"value":case"children":break;default:ed(e,t,o,null,a,s)}for(i in a)if(s=a[i],l=n[i],a.hasOwnProperty(i)&&(null!=s||null!=l))switch(i){case"value":h=s;break;case"defaultValue":f=s;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(r(91));break;default:s!==l&&ed(e,t,i,s,a,l)}return void jt(e,h,f);case"option":for(var p in n)if(h=n[p],n.hasOwnProperty(p)&&null!=h&&!a.hasOwnProperty(p))if("selected"===p)e.selected=!1;else ed(e,t,p,null,a,h);for(c in a)if(h=a[c],f=n[c],a.hasOwnProperty(c)&&h!==f&&(null!=h||null!=f))if("selected"===c)e.selected=h&&"function"!=typeof h&&"symbol"!=typeof h;else ed(e,t,c,h,a,f);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)h=n[g],n.hasOwnProperty(g)&&null!=h&&!a.hasOwnProperty(g)&&ed(e,t,g,null,a,h);for(u in a)if(h=a[u],f=n[u],a.hasOwnProperty(u)&&h!==f&&(null!=h||null!=f))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=h)throw Error(r(137,t));break;default:ed(e,t,u,h,a,f)}return;default:if(Tt(t)){for(var x in n)h=n[x],n.hasOwnProperty(x)&&void 0!==h&&!a.hasOwnProperty(x)&&td(e,t,x,void 0,a,h);for(d in a)h=a[d],f=n[d],!a.hasOwnProperty(d)||h===f||void 0===h&&void 0===f||td(e,t,d,h,a,f);return}}for(var y in n)h=n[y],n.hasOwnProperty(y)&&null!=h&&!a.hasOwnProperty(y)&&ed(e,t,y,null,a,h);for(m in a)h=a[m],f=n[m],!a.hasOwnProperty(m)||h===f||null==h&&null==f||ed(e,t,m,h,a,f)}(a,e.type,n,t),a[Me]=t}catch(s){mu(e,e.return,s)}}function go(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag&&pd(e.type)||4===e.tag}function xo(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||go(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(27===e.tag&&pd(e.type))continue e;if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function yo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?(9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).insertBefore(e,t):((t=9===n.nodeType?n.body:"HTML"===n.nodeName?n.ownerDocument.body:n).appendChild(e),null!=(n=n._reactRootContainer)||null!==t.onclick||(t.onclick=Ju));else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode,t=null),null!==(e=e.child)))for(yo(e,t,n),e=e.sibling;null!==e;)yo(e,t,n),e=e.sibling}function bo(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&(27===r&&pd(e.type)&&(n=e.stateNode),null!==(e=e.child)))for(bo(e,t,n),e=e.sibling;null!==e;)bo(e,t,n),e=e.sibling}function vo(e){var t=e.stateNode,n=e.memoizedProps;try{for(var r=e.type,a=t.attributes;a.length;)t.removeAttributeNode(a[0]);nd(t,r,n),t[Oe]=e,t[Me]=n}catch(s){mu(e,e.return,s)}}var wo=!1,jo=!1,ko=!1,No="function"==typeof WeakSet?WeakSet:Set,So=null;function Co(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:Bo(e,n),4&r&&io(5,n);break;case 1:if(Bo(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(l){mu(n,n.return,l)}else{var a=xi(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(a,t,e.__reactInternalSnapshotBeforeUpdate)}catch(i){mu(n,n.return,i)}}64&r&&co(n),512&r&&mo(n,n.return);break;case 3:if(Bo(e,n),64&r&&null!==(e=n.updateQueue)){if(t=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:t=n.child.stateNode}try{fs(e,t)}catch(l){mu(n,n.return,l)}}break;case 27:null===t&&4&r&&vo(n);case 26:case 5:Bo(e,n),null===t&&4&r&&fo(n),512&r&&mo(n,n.return);break;case 12:Bo(e,n);break;case 13:Bo(e,n),4&r&&zo(e,n),64&r&&(null!==(e=n.memoizedState)&&(null!==(e=e.dehydrated)&&function(e,t){var n=e.ownerDocument;if("$?"!==e.data||"complete"===n.readyState)t();else{var r=function(){t(),n.removeEventListener("DOMContentLoaded",r)};n.addEventListener("DOMContentLoaded",r),e._reactRetry=r}}(e,n=gu.bind(null,n))));break;case 22:if(!(r=null!==n.memoizedState||wo)){t=null!==t&&null!==t.memoizedState||jo,a=wo;var s=jo;wo=r,(jo=t)&&!s?Uo(e,n,!!(8772&n.subtreeFlags)):Bo(e,n),wo=a,jo=s}break;case 30:break;default:Bo(e,n)}}function Lo(e){var t=e.alternate;null!==t&&(e.alternate=null,Lo(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&We(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var To=null,Eo=!1;function _o(e,t,n){for(n=n.child;null!==n;)Po(e,t,n),n=n.sibling}function Po(e,t,n){if(he&&"function"==typeof he.onCommitFiberUnmount)try{he.onCommitFiberUnmount(me,n)}catch(s){}switch(n.tag){case 26:jo||ho(n,t),_o(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:jo||ho(n,t);var r=To,a=Eo;pd(n.type)&&(To=n.stateNode,Eo=!1),_o(e,t,n),kd(n.stateNode),To=r,Eo=a;break;case 5:jo||ho(n,t);case 6:if(r=To,a=Eo,To=null,_o(e,t,n),Eo=a,null!==(To=r))if(Eo)try{(9===To.nodeType?To.body:"HTML"===To.nodeName?To.ownerDocument.body:To).removeChild(n.stateNode)}catch(l){mu(n,t,l)}else try{To.removeChild(n.stateNode)}catch(l){mu(n,t,l)}break;case 18:null!==To&&(Eo?(gd(9===(e=To).nodeType?e.body:"HTML"===e.nodeName?e.ownerDocument.body:e,n.stateNode),Em(e)):gd(To,n.stateNode));break;case 4:r=To,a=Eo,To=n.stateNode.containerInfo,Eo=!0,_o(e,t,n),To=r,Eo=a;break;case 0:case 11:case 14:case 15:jo||oo(2,n,t),jo||oo(4,n,t),_o(e,t,n);break;case 1:jo||(ho(n,t),"function"==typeof(r=n.stateNode).componentWillUnmount&&uo(n,t,r)),_o(e,t,n);break;case 21:_o(e,t,n);break;case 22:jo=(r=jo)||null!==n.memoizedState,_o(e,t,n),jo=r;break;default:_o(e,t,n)}}function zo(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{Em(e)}catch(n){mu(t,t.return,n)}}function Ao(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new No),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new No),t;default:throw Error(r(435,e.tag))}}(e);t.forEach(function(t){var r=xu.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))})}function Oo(e,t){var n=t.deletions;if(null!==n)for(var a=0;a<n.length;a++){var s=n[a],l=e,i=t,o=i;e:for(;null!==o;){switch(o.tag){case 27:if(pd(o.type)){To=o.stateNode,Eo=!1;break e}break;case 5:To=o.stateNode,Eo=!1;break e;case 3:case 4:To=o.stateNode.containerInfo,Eo=!0;break e}o=o.return}if(null===To)throw Error(r(160));Po(l,i,s),To=null,Eo=!1,null!==(l=s.alternate)&&(l.return=null),s.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)Ro(t,e),t=t.sibling}var Mo=null;function Ro(e,t){var n=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Oo(t,e),Do(e),4&a&&(oo(3,e,e.return),io(3,e),oo(5,e,e.return));break;case 1:Oo(t,e),Do(e),512&a&&(jo||null===n||ho(n,n.return)),64&a&&wo&&(null!==(e=e.updateQueue)&&(null!==(a=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?a:n.concat(a))));break;case 26:var s=Mo;if(Oo(t,e),Do(e),512&a&&(jo||null===n||ho(n,n.return)),4&a){var l=null!==n?n.memoizedState:null;if(a=e.memoizedState,null===n)if(null===a)if(null===e.stateNode){e:{a=e.type,n=e.memoizedProps,s=s.ownerDocument||s;t:switch(a){case"title":(!(l=s.getElementsByTagName("title")[0])||l[Ue]||l[Oe]||"http://www.w3.org/2000/svg"===l.namespaceURI||l.hasAttribute("itemprop"))&&(l=s.createElement(a),s.head.insertBefore(l,s.querySelector("head > title"))),nd(l,a,n),l[Oe]=e,Qe(l),a=l;break e;case"link":var i=Ud("link","href",s).get(a+(n.href||""));if(i)for(var o=0;o<i.length;o++)if((l=i[o]).getAttribute("href")===(null==n.href||""===n.href?null:n.href)&&l.getAttribute("rel")===(null==n.rel?null:n.rel)&&l.getAttribute("title")===(null==n.title?null:n.title)&&l.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(o,1);break t}nd(l=s.createElement(a),a,n),s.head.appendChild(l);break;case"meta":if(i=Ud("meta","content",s).get(a+(n.content||"")))for(o=0;o<i.length;o++)if((l=i[o]).getAttribute("content")===(null==n.content?null:""+n.content)&&l.getAttribute("name")===(null==n.name?null:n.name)&&l.getAttribute("property")===(null==n.property?null:n.property)&&l.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&l.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(o,1);break t}nd(l=s.createElement(a),a,n),s.head.appendChild(l);break;default:throw Error(r(468,a))}l[Oe]=e,Qe(l),a=l}e.stateNode=a}else Wd(s,e.type,e.stateNode);else e.stateNode=Rd(s,a,e.memoizedProps);else l!==a?(null===l?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):l.count--,null===a?Wd(s,e.type,e.stateNode):Rd(s,a,e.memoizedProps)):null===a&&null!==e.stateNode&&po(e,e.memoizedProps,n.memoizedProps)}break;case 27:Oo(t,e),Do(e),512&a&&(jo||null===n||ho(n,n.return)),null!==n&&4&a&&po(e,e.memoizedProps,n.memoizedProps);break;case 5:if(Oo(t,e),Do(e),512&a&&(jo||null===n||ho(n,n.return)),32&e.flags){s=e.stateNode;try{Nt(s,"")}catch(f){mu(e,e.return,f)}}4&a&&null!=e.stateNode&&po(e,s=e.memoizedProps,null!==n?n.memoizedProps:s),1024&a&&(ko=!0);break;case 6:if(Oo(t,e),Do(e),4&a){if(null===e.stateNode)throw Error(r(162));a=e.memoizedProps,n=e.stateNode;try{n.nodeValue=a}catch(f){mu(e,e.return,f)}}break;case 3:if(Id=null,s=Mo,Mo=Cd(t.containerInfo),Oo(t,e),Mo=s,Do(e),4&a&&null!==n&&n.memoizedState.isDehydrated)try{Em(t.containerInfo)}catch(f){mu(e,e.return,f)}ko&&(ko=!1,Fo(e));break;case 4:a=Mo,Mo=Cd(e.stateNode.containerInfo),Oo(t,e),Do(e),Mo=a;break;case 12:default:Oo(t,e),Do(e);break;case 13:Oo(t,e),Do(e),8192&e.child.flags&&null!==e.memoizedState!=(null!==n&&null!==n.memoizedState)&&(kc=re()),4&a&&(null!==(a=e.updateQueue)&&(e.updateQueue=null,Ao(e,a)));break;case 22:s=null!==e.memoizedState;var c=null!==n&&null!==n.memoizedState,u=wo,d=jo;if(wo=u||s,jo=d||c,Oo(t,e),jo=d,wo=u,Do(e),8192&a)e:for(t=e.stateNode,t._visibility=s?-2&t._visibility:1|t._visibility,s&&(null===n||c||wo||jo||Io(e)),n=null,t=e;;){if(5===t.tag||26===t.tag){if(null===n){c=n=t;try{if(l=c.stateNode,s)"function"==typeof(i=l.style).setProperty?i.setProperty("display","none","important"):i.display="none";else{o=c.stateNode;var m=c.memoizedProps.style,h=null!=m&&m.hasOwnProperty("display")?m.display:null;o.style.display=null==h||"boolean"==typeof h?"":(""+h).trim()}}catch(f){mu(c,c.return,f)}}}else if(6===t.tag){if(null===n){c=t;try{c.stateNode.nodeValue=s?"":c.memoizedProps}catch(f){mu(c,c.return,f)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&a&&(null!==(a=e.updateQueue)&&(null!==(n=a.retryQueue)&&(a.retryQueue=null,Ao(e,n))));break;case 19:Oo(t,e),Do(e),4&a&&(null!==(a=e.updateQueue)&&(e.updateQueue=null,Ao(e,a)));case 30:case 21:}}function Do(e){var t=e.flags;if(2&t){try{for(var n,a=e.return;null!==a;){if(go(a)){n=a;break}a=a.return}if(null==n)throw Error(r(160));switch(n.tag){case 27:var s=n.stateNode;bo(e,xo(e),s);break;case 5:var l=n.stateNode;32&n.flags&&(Nt(l,""),n.flags&=-33),bo(e,xo(e),l);break;case 3:case 4:var i=n.stateNode.containerInfo;yo(e,xo(e),i);break;default:throw Error(r(161))}}catch(o){mu(e,e.return,o)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function Fo(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;Fo(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function Bo(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)Co(e,t.alternate,t),t=t.sibling}function Io(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:oo(4,t,t.return),Io(t);break;case 1:ho(t,t.return);var n=t.stateNode;"function"==typeof n.componentWillUnmount&&uo(t,t.return,n),Io(t);break;case 27:kd(t.stateNode);case 26:case 5:ho(t,t.return),Io(t);break;case 22:null===t.memoizedState&&Io(t);break;default:Io(t)}e=e.sibling}}function Uo(e,t,n){for(n=n&&!!(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,a=e,s=t,l=s.flags;switch(s.tag){case 0:case 11:case 15:Uo(a,s,n),io(4,s);break;case 1:if(Uo(a,s,n),"function"==typeof(a=(r=s).stateNode).componentDidMount)try{a.componentDidMount()}catch(c){mu(r,r.return,c)}if(null!==(a=(r=s).updateQueue)){var i=r.stateNode;try{var o=a.shared.hiddenCallbacks;if(null!==o)for(a.shared.hiddenCallbacks=null,a=0;a<o.length;a++)hs(o[a],i)}catch(c){mu(r,r.return,c)}}n&&64&l&&co(s),mo(s,s.return);break;case 27:vo(s);case 26:case 5:Uo(a,s,n),n&&null===r&&4&l&&fo(s),mo(s,s.return);break;case 12:Uo(a,s,n);break;case 13:Uo(a,s,n),n&&4&l&&zo(a,s);break;case 22:null===s.memoizedState&&Uo(a,s,n),mo(s,s.return);break;case 30:break;default:Uo(a,s,n)}t=t.sibling}}function Wo(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Ra(n))}function qo(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ra(e))}function Ho(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Vo(e,t,n,r),t=t.sibling}function Vo(e,t,n,r){var a=t.flags;switch(t.tag){case 0:case 11:case 15:Ho(e,t,n,r),2048&a&&io(9,t);break;case 1:case 13:default:Ho(e,t,n,r);break;case 3:Ho(e,t,n,r),2048&a&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Ra(e)));break;case 12:if(2048&a){Ho(e,t,n,r),e=t.stateNode;try{var s=t.memoizedProps,l=s.id,i=s.onPostCommit;"function"==typeof i&&i(l,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(o){mu(t,t.return,o)}}else Ho(e,t,n,r);break;case 23:break;case 22:s=t.stateNode,l=t.alternate,null!==t.memoizedState?2&s._visibility?Ho(e,t,n,r):Qo(e,t):2&s._visibility?Ho(e,t,n,r):(s._visibility|=2,$o(e,t,n,r,!!(10256&t.subtreeFlags))),2048&a&&Wo(l,t);break;case 24:Ho(e,t,n,r),2048&a&&qo(t.alternate,t)}}function $o(e,t,n,r,a){for(a=a&&!!(10256&t.subtreeFlags),t=t.child;null!==t;){var s=e,l=t,i=n,o=r,c=l.flags;switch(l.tag){case 0:case 11:case 15:$o(s,l,i,o,a),io(8,l);break;case 23:break;case 22:var u=l.stateNode;null!==l.memoizedState?2&u._visibility?$o(s,l,i,o,a):Qo(s,l):(u._visibility|=2,$o(s,l,i,o,a)),a&&2048&c&&Wo(l.alternate,l);break;case 24:$o(s,l,i,o,a),a&&2048&c&&qo(l.alternate,l);break;default:$o(s,l,i,o,a)}t=t.sibling}}function Qo(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,a=r.flags;switch(r.tag){case 22:Qo(n,r),2048&a&&Wo(r.alternate,r);break;case 24:Qo(n,r),2048&a&&qo(r.alternate,r);break;default:Qo(n,r)}t=t.sibling}}var Ko=8192;function Yo(e){if(e.subtreeFlags&Ko)for(e=e.child;null!==e;)Xo(e),e=e.sibling}function Xo(e){switch(e.tag){case 26:Yo(e),e.flags&Ko&&null!==e.memoizedState&&function(e,t,n){if(null===Hd)throw Error(r(475));var a=Hd;if(!("stylesheet"!==t.type||"string"==typeof n.media&&!1===matchMedia(n.media).matches||4&t.state.loading)){if(null===t.instance){var s=Pd(n.href),l=e.querySelector(zd(s));if(l)return null!==(e=l._p)&&"object"==typeof e&&"function"==typeof e.then&&(a.count++,a=$d.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=l,void Qe(l);l=e.ownerDocument||e,n=Ad(n),(s=Nd.get(s))&&Fd(n,s),Qe(l=l.createElement("link"));var i=l;i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),nd(l,"link",n),t.instance=l}null===a.stylesheets&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&!(3&t.state.loading)&&(a.count++,t=$d.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}(Mo,e.memoizedState,e.memoizedProps);break;case 5:default:Yo(e);break;case 3:case 4:var t=Mo;Mo=Cd(e.stateNode.containerInfo),Yo(e),Mo=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Ko,Ko=16777216,Yo(e),Ko=t):Yo(e))}}function Zo(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Go(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];So=r,tc(r,e)}Zo(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Jo(e),e=e.sibling}function Jo(e){switch(e.tag){case 0:case 11:case 15:Go(e),2048&e.flags&&oo(9,e,e.return);break;case 3:case 12:default:Go(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&2&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-3,ec(e)):Go(e)}}function ec(e){var t=e.deletions;if(16&e.flags){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];So=r,tc(r,e)}Zo(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:oo(8,t,t.return),ec(t);break;case 22:2&(n=t.stateNode)._visibility&&(n._visibility&=-3,ec(t));break;default:ec(t)}e=e.sibling}}function tc(e,t){for(;null!==So;){var n=So;switch(n.tag){case 0:case 11:case 15:oo(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Ra(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,So=r;else e:for(n=e;null!==So;){var a=(r=So).sibling,s=r.return;if(Lo(r),r===n){So=null;break e}if(null!==a){a.return=s,So=a;break e}So=s}}}var nc={getCacheForType:function(e){var t=Ta(Oa),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},rc="function"==typeof WeakMap?WeakMap:Map,ac=0,sc=null,lc=null,ic=0,oc=0,cc=null,uc=!1,dc=!1,mc=!1,hc=0,fc=0,pc=0,gc=0,xc=0,yc=0,bc=0,vc=null,wc=null,jc=!1,kc=0,Nc=1/0,Sc=null,Cc=null,Lc=0,Tc=null,Ec=null,_c=0,Pc=0,zc=null,Ac=null,Oc=0,Mc=null;function Rc(){if(2&ac&&0!==ic)return ic&-ic;if(null!==R.T){return 0!==Ba?Ba:Pu()}return ze()}function Dc(){0===yc&&(yc=536870912&ic&&!ia?536870912:Ne());var e=si.current;return null!==e&&(e.flags|=32),yc}function Fc(e,t,n){(e!==sc||2!==oc&&9!==oc)&&null===e.cancelPendingCommit||(Vc(e,0),Wc(e,ic,yc,!1)),Le(e,n),2&ac&&e===sc||(e===sc&&(!(2&ac)&&(gc|=n),4===fc&&Wc(e,ic,yc,!1)),Nu(e))}function Bc(e,t,n){if(6&ac)throw Error(r(327));for(var a=!n&&!(124&t)&&0===(t&e.expiredLanes)||je(e,t),s=a?function(e,t){var n=ac;ac|=2;var a=Qc(),s=Kc();sc!==e||ic!==t?(Sc=null,Nc=re()+500,Vc(e,t)):dc=je(e,t);e:for(;;)try{if(0!==oc&&null!==lc){t=lc;var l=cc;t:switch(oc){case 1:oc=0,cc=null,tu(e,t,l,1);break;case 2:case 9:if(Za(l)){oc=0,cc=null,eu(t);break}t=function(){2!==oc&&9!==oc||sc!==e||(oc=7),Nu(e)},l.then(t,t);break e;case 3:oc=7;break e;case 4:oc=5;break e;case 7:Za(l)?(oc=0,cc=null,eu(t)):(oc=0,cc=null,tu(e,t,l,7));break;case 5:var i=null;switch(lc.tag){case 26:i=lc.memoizedState;case 5:case 27:var o=lc;if(!i||qd(i)){oc=0,cc=null;var c=o.sibling;if(null!==c)lc=c;else{var u=o.return;null!==u?(lc=u,nu(u)):lc=null}break t}}oc=0,cc=null,tu(e,t,l,5);break;case 6:oc=0,cc=null,tu(e,t,l,6);break;case 8:Hc(),fc=6;break e;default:throw Error(r(462))}}Gc();break}catch(d){$c(e,d)}return va=ba=null,R.H=a,R.A=s,ac=n,null!==lc?0:(sc=null,ic=0,_r(),fc)}(e,t):Xc(e,t,!0),l=a;;){if(0===s){dc&&!a&&Wc(e,t,0,!1);break}if(n=e.current.alternate,!l||Uc(n)){if(2===s){if(l=t,e.errorRecoveryDisabledLanes&l)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var o=e;s=vc;var c=o.current.memoizedState.isDehydrated;if(c&&(Vc(o,i).flags|=256),2!==(i=Xc(o,i,!1))){if(mc&&!c){o.errorRecoveryDisabledLanes|=l,gc|=l,s=4;break e}l=wc,wc=s,null!==l&&(null===wc?wc=l:wc.push.apply(wc,l))}s=i}if(l=!1,2!==s)continue}}if(1===s){Vc(e,0),Wc(e,t,0,!0);break}e:{switch(a=e,l=s){case 0:case 1:throw Error(r(345));case 4:if((4194048&t)!==t)break;case 6:Wc(a,t,yc,!uc);break e;case 2:wc=null;break;case 3:case 5:break;default:throw Error(r(329))}if((62914560&t)===t&&10<(s=kc+300-re())){if(Wc(a,t,yc,!uc),0!==we(a,0,!0))break e;a.timeoutHandle=ud(Ic.bind(null,a,n,wc,Sc,jc,t,yc,gc,bc,uc,l,2,-0,0),s)}else Ic(a,n,wc,Sc,jc,t,yc,gc,bc,uc,l,0,-0,0)}break}s=Xc(e,t,!1),l=!1}Nu(e)}function Ic(e,t,n,a,s,l,i,o,c,u,d,m,h,f){if(e.timeoutHandle=-1,(8192&(m=t.subtreeFlags)||!(16785408&~m))&&(Hd={stylesheets:null,count:0,unsuspend:Vd},Xo(t),null!==(m=function(){if(null===Hd)throw Error(r(475));var e=Hd;return e.stylesheets&&0===e.count&&Kd(e,e.stylesheets),0<e.count?function(t){var n=setTimeout(function(){if(e.stylesheets&&Kd(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=m(au.bind(null,e,t,l,n,a,s,i,o,c,d,1,h,f)),void Wc(e,l,i,!u);au(e,t,l,n,a,s,i,o,c)}function Uc(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var a=n[r],s=a.getSnapshot;a=a.value;try{if(!Zn(s(),a))return!1}catch(l){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Wc(e,t,n,r){t&=~xc,t&=~gc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var a=t;0<a;){var s=31-pe(a),l=1<<s;r[s]=-1,a&=~l}0!==n&&Te(e,n,t)}function qc(){return!!(6&ac)||(Su(0),!1)}function Hc(){if(null!==lc){if(0===oc)var e=lc.return;else va=ba=null,Bs(e=lc),Xl=null,Zl=0,e=lc;for(;null!==e;)lo(e.alternate,e),e=e.return;lc=null}}function Vc(e,t){var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,dd(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Hc(),sc=e,lc=n=Ir(e.current,null),ic=t,oc=0,cc=null,uc=!1,dc=je(e,t),mc=!1,bc=yc=xc=gc=pc=fc=0,wc=vc=null,jc=!1,8&t&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var a=31-pe(r),s=1<<a;t|=e[a],r&=~s}return hc=t,_r(),n}function $c(e,t){ws=null,R.H=$l,t===Qa||t===Ya?(t=ts(),oc=3):t===Ka?(t=ts(),oc=4):oc=t===Li?8:null!==t&&"object"==typeof t&&"function"==typeof t.then?6:1,cc=t,null===lc&&(fc=1,ji(e,Cr(t,e.current)))}function Qc(){var e=R.H;return R.H=$l,null===e?$l:e}function Kc(){var e=R.A;return R.A=nc,e}function Yc(){fc=4,uc||(4194048&ic)!==ic&&null!==si.current||(dc=!0),!(134217727&pc)&&!(134217727&gc)||null===sc||Wc(sc,ic,yc,!1)}function Xc(e,t,n){var r=ac;ac|=2;var a=Qc(),s=Kc();sc===e&&ic===t||(Sc=null,Vc(e,t)),t=!1;var l=fc;e:for(;;)try{if(0!==oc&&null!==lc){var i=lc,o=cc;switch(oc){case 8:Hc(),l=6;break e;case 3:case 2:case 9:case 6:null===si.current&&(t=!0);var c=oc;if(oc=0,cc=null,tu(e,i,o,c),n&&dc){l=0;break e}break;default:c=oc,oc=0,cc=null,tu(e,i,o,c)}}Zc(),l=fc;break}catch(u){$c(e,u)}return t&&e.shellSuspendCounter++,va=ba=null,ac=r,R.H=a,R.A=s,null===lc&&(sc=null,ic=0,_r()),l}function Zc(){for(;null!==lc;)Jc(lc)}function Gc(){for(;null!==lc&&!te();)Jc(lc)}function Jc(e){var t=Gi(e.alternate,e,hc);e.memoizedProps=e.pendingProps,null===t?nu(e):lc=t}function eu(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Di(n,t,t.pendingProps,t.type,void 0,ic);break;case 11:t=Di(n,t,t.pendingProps,t.type.render,t.ref,ic);break;case 5:Bs(t);default:lo(n,t),t=Gi(n,t=lc=Ur(t,hc),hc)}e.memoizedProps=e.pendingProps,null===t?nu(e):lc=t}function tu(e,t,n,a){va=ba=null,Bs(t),Xl=null,Zl=0;var s=t.return;try{if(function(e,t,n,a,s){if(n.flags|=32768,null!==a&&"object"==typeof a&&"function"==typeof a.then){if(null!==(t=n.alternate)&&Sa(t,n,s,!0),null!==(n=si.current)){switch(n.tag){case 13:return null===li?Yc():null===n.alternate&&0===fc&&(fc=3),n.flags&=-257,n.flags|=65536,n.lanes=s,a===Xa?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([a]):t.add(a),hu(e,a,s)),!1;case 22:return n.flags|=65536,a===Xa?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([a]):n.add(a),hu(e,a,s)),!1}throw Error(r(435,n.tag))}return hu(e,a,s),Yc(),!1}if(ia)return null!==(t=si.current)?(!(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=s,a!==ua&&xa(Cr(e=Error(r(422),{cause:a}),n))):(a!==ua&&xa(Cr(t=Error(r(423),{cause:a}),n)),(e=e.current.alternate).flags|=65536,s&=-s,e.lanes|=s,a=Cr(a,n),cs(e,s=Ni(e.stateNode,a,s)),4!==fc&&(fc=2)),!1;var l=Error(r(520),{cause:a});if(l=Cr(l,n),null===vc?vc=[l]:vc.push(l),4!==fc&&(fc=2),null===t)return!0;a=Cr(a,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=s&-s,n.lanes|=e,cs(n,e=Ni(n.stateNode,a,e)),!1;case 1:if(t=n.type,l=n.stateNode,!(128&n.flags||"function"!=typeof t.getDerivedStateFromError&&(null===l||"function"!=typeof l.componentDidCatch||null!==Cc&&Cc.has(l))))return n.flags|=65536,s&=-s,n.lanes|=s,Ci(s=Si(s),e,n,a),cs(n,s),!1}n=n.return}while(null!==n);return!1}(e,s,t,n,ic))return fc=1,ji(e,Cr(n,e.current)),void(lc=null)}catch(l){if(null!==s)throw lc=s,l;return fc=1,ji(e,Cr(n,e.current)),void(lc=null)}32768&t.flags?(ia||1===a?e=!0:dc||536870912&ic?e=!1:(uc=e=!0,(2===a||9===a||3===a||6===a)&&(null!==(a=si.current)&&13===a.tag&&(a.flags|=16384))),ru(t,e)):nu(t)}function nu(e){var t=e;do{if(32768&t.flags)return void ru(t,uc);e=t.return;var n=ao(t.alternate,t,hc);if(null!==n)return void(lc=n);if(null!==(t=t.sibling))return void(lc=t);lc=t=e}while(null!==t);0===fc&&(fc=5)}function ru(e,t){do{var n=so(e.alternate,e);if(null!==n)return n.flags&=32767,void(lc=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(lc=e);lc=e=n}while(null!==e);fc=6,lc=null}function au(e,t,n,a,s,l,i,o,c){e.cancelPendingCommit=null;do{cu()}while(0!==Lc);if(6&ac)throw Error(r(327));if(null!==t){if(t===e.current)throw Error(r(177));if(l=t.lanes|t.childLanes,function(e,t,n,r,a,s){var l=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,o=e.expirationTimes,c=e.hiddenUpdates;for(n=l&~n;0<n;){var u=31-pe(n),d=1<<u;i[u]=0,o[u]=-1;var m=c[u];if(null!==m)for(c[u]=null,u=0;u<m.length;u++){var h=m[u];null!==h&&(h.lane&=-536870913)}n&=~d}0!==r&&Te(e,r,0),0!==s&&0===a&&0!==e.tag&&(e.suspendedLanes|=s&~(l&~t))}(e,n,l|=Er,i,o,c),e===sc&&(lc=sc=null,ic=0),Ec=t,Tc=e,_c=n,Pc=l,zc=s,Ac=a,10256&t.subtreeFlags||10256&t.flags?(e.callbackNode=null,e.callbackPriority=0,J(ie,function(){return uu(),null})):(e.callbackNode=null,e.callbackPriority=0),a=!!(13878&t.flags),13878&t.subtreeFlags||a){a=R.T,R.T=null,s=D.p,D.p=2,i=ac,ac|=4;try{!function(e,t){if(e=e.containerInfo,rd=am,rr(e=nr(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var a=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(a&&0!==a.rangeCount){n=a.anchorNode;var s=a.anchorOffset,l=a.focusNode;a=a.focusOffset;try{n.nodeType,l.nodeType}catch(g){n=null;break e}var i=0,o=-1,c=-1,u=0,d=0,m=e,h=null;t:for(;;){for(var f;m!==n||0!==s&&3!==m.nodeType||(o=i+s),m!==l||0!==a&&3!==m.nodeType||(c=i+a),3===m.nodeType&&(i+=m.nodeValue.length),null!==(f=m.firstChild);)h=m,m=f;for(;;){if(m===e)break t;if(h===n&&++u===s&&(o=i),h===l&&++d===a&&(c=i),null!==(f=m.nextSibling))break;h=(m=h).parentNode}m=f}n=-1===o||-1===c?null:{start:o,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(ad={focusedElem:e,selectionRange:n},am=!1,So=t;null!==So;)if(e=(t=So).child,1024&t.subtreeFlags&&null!==e)e.return=t,So=e;else for(;null!==So;){switch(l=(t=So).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(1024&e&&null!==l){e=void 0,n=t,s=l.memoizedProps,l=l.memoizedState,a=n.stateNode;try{var p=xi(n.type,s,(n.elementType,n.type));e=a.getSnapshotBeforeUpdate(p,l),a.__reactInternalSnapshotBeforeUpdate=e}catch(x){mu(n,n.return,x)}}break;case 3:if(1024&e)if(9===(n=(e=t.stateNode.containerInfo).nodeType))xd(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":xd(e);break;default:e.textContent=""}break;default:if(1024&e)throw Error(r(163))}if(null!==(e=t.sibling)){e.return=t.return,So=e;break}So=t.return}}(e,t)}finally{ac=i,D.p=s,R.T=a}}Lc=1,su(),lu(),iu()}}function su(){if(1===Lc){Lc=0;var e=Tc,t=Ec,n=!!(13878&t.flags);if(13878&t.subtreeFlags||n){n=R.T,R.T=null;var r=D.p;D.p=2;var a=ac;ac|=4;try{Ro(t,e);var s=ad,l=nr(e.containerInfo),i=s.focusedElem,o=s.selectionRange;if(l!==i&&i&&i.ownerDocument&&tr(i.ownerDocument.documentElement,i)){if(null!==o&&rr(i)){var c=o.start,u=o.end;if(void 0===u&&(u=c),"selectionStart"in i)i.selectionStart=c,i.selectionEnd=Math.min(u,i.value.length);else{var d=i.ownerDocument||document,m=d&&d.defaultView||window;if(m.getSelection){var h=m.getSelection(),f=i.textContent.length,p=Math.min(o.start,f),g=void 0===o.end?p:Math.min(o.end,f);!h.extend&&p>g&&(l=g,g=p,p=l);var x=er(i,p),y=er(i,g);if(x&&y&&(1!==h.rangeCount||h.anchorNode!==x.node||h.anchorOffset!==x.offset||h.focusNode!==y.node||h.focusOffset!==y.offset)){var b=d.createRange();b.setStart(x.node,x.offset),h.removeAllRanges(),p>g?(h.addRange(b),h.extend(y.node,y.offset)):(b.setEnd(y.node,y.offset),h.addRange(b))}}}}for(d=[],h=i;h=h.parentNode;)1===h.nodeType&&d.push({element:h,left:h.scrollLeft,top:h.scrollTop});for("function"==typeof i.focus&&i.focus(),i=0;i<d.length;i++){var v=d[i];v.element.scrollLeft=v.left,v.element.scrollTop=v.top}}am=!!rd,ad=rd=null}finally{ac=a,D.p=r,R.T=n}}e.current=t,Lc=2}}function lu(){if(2===Lc){Lc=0;var e=Tc,t=Ec,n=!!(8772&t.flags);if(8772&t.subtreeFlags||n){n=R.T,R.T=null;var r=D.p;D.p=2;var a=ac;ac|=4;try{Co(e,t.alternate,t)}finally{ac=a,D.p=r,R.T=n}}Lc=3}}function iu(){if(4===Lc||3===Lc){Lc=0,ne();var e=Tc,t=Ec,n=_c,r=Ac;10256&t.subtreeFlags||10256&t.flags?Lc=5:(Lc=0,Ec=Tc=null,ou(e,e.pendingLanes));var a=e.pendingLanes;if(0===a&&(Cc=null),Pe(n),t=t.stateNode,he&&"function"==typeof he.onCommitFiberRoot)try{he.onCommitFiberRoot(me,t,void 0,!(128&~t.current.flags))}catch(o){}if(null!==r){t=R.T,a=D.p,D.p=2,R.T=null;try{for(var s=e.onRecoverableError,l=0;l<r.length;l++){var i=r[l];s(i.value,{componentStack:i.stack})}}finally{R.T=t,D.p=a}}3&_c&&cu(),Nu(e),a=e.pendingLanes,4194090&n&&42&a?e===Mc?Oc++:(Oc=0,Mc=e):Oc=0,Su(0)}}function ou(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Ra(t)))}function cu(e){return su(),lu(),iu(),uu()}function uu(){if(5!==Lc)return!1;var e=Tc,t=Pc;Pc=0;var n=Pe(_c),a=R.T,s=D.p;try{D.p=32>n?32:n,R.T=null,n=zc,zc=null;var l=Tc,i=_c;if(Lc=0,Ec=Tc=null,_c=0,6&ac)throw Error(r(331));var o=ac;if(ac|=4,Jo(l.current),Vo(l,l.current,i,n),ac=o,Su(0,!1),he&&"function"==typeof he.onPostCommitFiberRoot)try{he.onPostCommitFiberRoot(me,l)}catch(c){}return!0}finally{D.p=s,R.T=a,ou(e,t)}}function du(e,t,n){t=Cr(n,t),null!==(e=is(e,t=Ni(e.stateNode,t,2),2))&&(Le(e,2),Nu(e))}function mu(e,t,n){if(3===e.tag)du(e,e,n);else for(;null!==t;){if(3===t.tag){du(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"==typeof t.type.getDerivedStateFromError||"function"==typeof r.componentDidCatch&&(null===Cc||!Cc.has(r))){e=Cr(n,e),null!==(r=is(t,n=Si(2),2))&&(Ci(n,r,t,e),Le(r,2),Nu(r));break}}t=t.return}}function hu(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new rc;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(mc=!0,a.add(n),e=fu.bind(null,e,t,n),t.then(e,e))}function fu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,sc===e&&(ic&n)===n&&(4===fc||3===fc&&(62914560&ic)===ic&&300>re()-kc?!(2&ac)&&Vc(e,0):xc|=n,bc===ic&&(bc=0)),Nu(e)}function pu(e,t){0===t&&(t=Se()),null!==(e=Ar(e,t))&&(Le(e,t),Nu(e))}function gu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),pu(e,n)}function xu(e,t){var n=0;switch(e.tag){case 13:var a=e.stateNode,s=e.memoizedState;null!==s&&(n=s.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(r(314))}null!==a&&a.delete(t),pu(e,n)}var yu=null,bu=null,vu=!1,wu=!1,ju=!1,ku=0;function Nu(e){e!==bu&&null===e.next&&(null===bu?yu=bu=e:bu=bu.next=e),wu=!0,vu||(vu=!0,hd(function(){6&ac?J(se,Cu):Lu()}))}function Su(e,t){if(!ju&&wu){ju=!0;do{for(var n=!1,r=yu;null!==r;){if(0!==e){var a=r.pendingLanes;if(0===a)var s=0;else{var l=r.suspendedLanes,i=r.pingedLanes;s=(1<<31-pe(42|e)+1)-1,s=201326741&(s&=a&~(l&~i))?201326741&s|1:s?2|s:0}0!==s&&(n=!0,_u(r,s))}else s=ic,!(3&(s=we(r,r===sc?s:0,null!==r.cancelPendingCommit||-1!==r.timeoutHandle)))||je(r,s)||(n=!0,_u(r,s));r=r.next}}while(n);ju=!1}}function Cu(){Lu()}function Lu(){wu=vu=!1;var e=0;0!==ku&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==cd&&(cd=e,!0);return cd=null,!1}()&&(e=ku),ku=0);for(var t=re(),n=null,r=yu;null!==r;){var a=r.next,s=Tu(r,t);0===s?(r.next=null,null===n?yu=a:n.next=a,null===a&&(bu=n)):(n=r,(0!==e||3&s)&&(wu=!0)),r=a}Su(e)}function Tu(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,s=-62914561&e.pendingLanes;0<s;){var l=31-pe(s),i=1<<l,o=a[l];-1===o?0!==(i&n)&&0===(i&r)||(a[l]=ke(i,t)):o<=t&&(e.expiredLanes|=i),s&=~i}if(n=ic,n=we(e,e===(t=sc)?n:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle),r=e.callbackNode,0===n||e===t&&(2===oc||9===oc)||null!==e.cancelPendingCommit)return null!==r&&null!==r&&ee(r),e.callbackNode=null,e.callbackPriority=0;if(!(3&n)||je(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&ee(r),Pe(n)){case 2:case 8:n=le;break;case 32:default:n=ie;break;case 268435456:n=ce}return r=Eu.bind(null,e),n=J(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&ee(r),e.callbackPriority=2,e.callbackNode=null,2}function Eu(e,t){if(0!==Lc&&5!==Lc)return e.callbackNode=null,e.callbackPriority=0,null;var n=e.callbackNode;if(cu()&&e.callbackNode!==n)return null;var r=ic;return 0===(r=we(e,e===sc?r:0,null!==e.cancelPendingCommit||-1!==e.timeoutHandle))?null:(Bc(e,r,t),Tu(e,re()),null!=e.callbackNode&&e.callbackNode===n?Eu.bind(null,e):null)}function _u(e,t){if(cu())return null;Bc(e,t,!0)}function Pu(){return 0===ku&&(ku=Ne()),ku}function zu(e){return null==e||"symbol"==typeof e||"boolean"==typeof e?null:"function"==typeof e?e:Pt(""+e)}function Au(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Ou=0;Ou<kr.length;Ou++){var Mu=kr[Ou];Nr(Mu.toLowerCase(),"on"+(Mu[0].toUpperCase()+Mu.slice(1)))}Nr(pr,"onAnimationEnd"),Nr(gr,"onAnimationIteration"),Nr(xr,"onAnimationStart"),Nr("dblclick","onDoubleClick"),Nr("focusin","onFocus"),Nr("focusout","onBlur"),Nr(yr,"onTransitionRun"),Nr(br,"onTransitionStart"),Nr(vr,"onTransitionCancel"),Nr(wr,"onTransitionEnd"),Ze("onMouseEnter",["mouseout","mouseover"]),Ze("onMouseLeave",["mouseout","mouseover"]),Ze("onPointerEnter",["pointerout","pointerover"]),Ze("onPointerLeave",["pointerout","pointerover"]),Xe("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Xe("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Xe("onBeforeInput",["compositionend","keypress","textInput","paste"]),Xe("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Xe("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Xe("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ru="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Du=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ru));function Fu(e,t){t=!!(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],o=i.instance,c=i.currentTarget;if(i=i.listener,o!==s&&a.isPropagationStopped())break e;s=i,a.currentTarget=c;try{s(a)}catch(u){yi(u)}a.currentTarget=null,s=o}else for(l=0;l<r.length;l++){if(o=(i=r[l]).instance,c=i.currentTarget,i=i.listener,o!==s&&a.isPropagationStopped())break e;s=i,a.currentTarget=c;try{s(a)}catch(u){yi(u)}a.currentTarget=null,s=o}}}}function Bu(e,t){var n=t[De];void 0===n&&(n=t[De]=new Set);var r=e+"__bubble";n.has(r)||(qu(t,e,2,!1),n.add(r))}function Iu(e,t,n){var r=0;t&&(r|=4),qu(n,e,r,t)}var Uu="_reactListening"+Math.random().toString(36).slice(2);function Wu(e){if(!e[Uu]){e[Uu]=!0,Ke.forEach(function(t){"selectionchange"!==t&&(Du.has(t)||Iu(t,!1,e),Iu(t,!0,e))});var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Uu]||(t[Uu]=!0,Iu("selectionchange",!1,t))}}function qu(e,t,n,r){switch(dm(t)){case 2:var a=sm;break;case 8:a=lm;break;default:a=im}n=a.bind(null,t,n,e),a=void 0,!Ut||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Hu(e,t,n,r,a){var l=r;if(!(1&t||2&t||null===r))e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var o=r.stateNode.containerInfo;if(o===a)break;if(4===i)for(i=r.return;null!==i;){var c=i.tag;if((3===c||4===c)&&i.stateNode.containerInfo===a)return;i=i.return}for(;null!==o;){if(null===(i=qe(o)))return;if(5===(c=i.tag)||6===c||26===c||27===c){r=l=i;continue e}o=o.parentNode}}r=r.return}Ft(function(){var r=l,a=At(n),i=[];e:{var o=jr.get(e);if(void 0!==o){var c=tn,u=e;switch(e){case"keypress":if(0===Qt(n))break e;case"keydown":case"keyup":c=xn;break;case"focusin":u="focus",c=on;break;case"focusout":u="blur",c=on;break;case"beforeblur":case"afterblur":c=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":c=sn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":c=ln;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":c=bn;break;case pr:case gr:case xr:c=cn;break;case wr:c=vn;break;case"scroll":case"scrollend":c=rn;break;case"wheel":c=wn;break;case"copy":case"cut":case"paste":c=un;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":c=yn;break;case"toggle":case"beforetoggle":c=jn}var d=!!(4&t),m=!d&&("scroll"===e||"scrollend"===e),h=d?null!==o?o+"Capture":null:o;d=[];for(var f,p=r;null!==p;){var g=p;if(f=g.stateNode,5!==(g=g.tag)&&26!==g&&27!==g||null===f||null===h||null!=(g=Bt(p,h))&&d.push(Vu(p,g,f)),m)break;p=p.return}0<d.length&&(o=new c(o,u,null,n,a),i.push({event:o,listeners:d}))}}if(!(7&t)){if(c="mouseout"===e||"pointerout"===e,(!(o="mouseover"===e||"pointerover"===e)||n===zt||!(u=n.relatedTarget||n.fromElement)||!qe(u)&&!u[Re])&&(c||o)&&(o=a.window===a?a:(o=a.ownerDocument)?o.defaultView||o.parentWindow:window,c?(c=r,null!==(u=(u=n.relatedTarget||n.toElement)?qe(u):null)&&(m=s(u),d=u.tag,u!==m||5!==d&&27!==d&&6!==d)&&(u=null)):(c=null,u=r),c!==u)){if(d=sn,g="onMouseLeave",h="onMouseEnter",p="mouse","pointerout"!==e&&"pointerover"!==e||(d=yn,g="onPointerLeave",h="onPointerEnter",p="pointer"),m=null==c?o:Ve(c),f=null==u?o:Ve(u),(o=new d(g,p+"leave",c,n,a)).target=m,o.relatedTarget=f,g=null,qe(a)===r&&((d=new d(h,p+"enter",u,n,a)).target=f,d.relatedTarget=m,g=d),m=g,c&&u)e:{for(h=u,p=0,f=d=c;f;f=Qu(f))p++;for(f=0,g=h;g;g=Qu(g))f++;for(;0<p-f;)d=Qu(d),p--;for(;0<f-p;)h=Qu(h),f--;for(;p--;){if(d===h||null!==h&&d===h.alternate)break e;d=Qu(d),h=Qu(h)}d=null}else d=null;null!==c&&Ku(i,o,c,d,!1),null!==u&&null!==m&&Ku(i,m,u,d,!0)}if("select"===(c=(o=r?Ve(r):window).nodeName&&o.nodeName.toLowerCase())||"input"===c&&"file"===o.type)var x=In;else if(On(o))if(Un)x=Xn;else{x=Kn;var y=Qn}else!(c=o.nodeName)||"input"!==c.toLowerCase()||"checkbox"!==o.type&&"radio"!==o.type?r&&Tt(r.elementType)&&(x=In):x=Yn;switch(x&&(x=x(e,r))?Mn(i,x,n,a):(y&&y(e,o,r),"focusout"===e&&r&&"number"===o.type&&null!=r.memoizedProps.value&&vt(o,"number",o.value)),y=r?Ve(r):window,e){case"focusin":(On(y)||"true"===y.contentEditable)&&(sr=y,lr=r,ir=null);break;case"focusout":ir=lr=sr=null;break;case"mousedown":or=!0;break;case"contextmenu":case"mouseup":case"dragend":or=!1,cr(i,n,a);break;case"selectionchange":if(ar)break;case"keydown":case"keyup":cr(i,n,a)}var b;if(Nn)e:{switch(e){case"compositionstart":var v="onCompositionStart";break e;case"compositionend":v="onCompositionEnd";break e;case"compositionupdate":v="onCompositionUpdate";break e}v=void 0}else zn?_n(e,n)&&(v="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(v="onCompositionStart");v&&(Ln&&"ko"!==n.locale&&(zn||"onCompositionStart"!==v?"onCompositionEnd"===v&&zn&&(b=$t()):(Ht="value"in(qt=a)?qt.value:qt.textContent,zn=!0)),0<(y=$u(r,v)).length&&(v=new dn(v,e,null,n,a),i.push({event:v,listeners:y}),b?v.data=b:null!==(b=Pn(n))&&(v.data=b))),(b=Cn?function(e,t){switch(e){case"compositionend":return Pn(t);case"keypress":return 32!==t.which?null:(En=!0,Tn);case"textInput":return(e=t.data)===Tn&&En?null:e;default:return null}}(e,n):function(e,t){if(zn)return"compositionend"===e||!Nn&&_n(e,t)?(e=$t(),Vt=Ht=qt=null,zn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Ln&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(v=$u(r,"onBeforeInput")).length&&(y=new dn("onBeforeInput","beforeinput",null,n,a),i.push({event:y,listeners:v}),y.data=b)),function(e,t,n,r,a){if("submit"===t&&n&&n.stateNode===a){var s=zu((a[Me]||null).action),l=r.submitter;l&&null!==(t=(t=l[Me]||null)?zu(t.formAction):l.getAttribute("formAction"))&&(s=t,l=null);var i=new tn("action","action",null,r,a);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==ku){var e=l?Au(a,l):new FormData(a);zl(n,{pending:!0,data:e,method:a.method,action:s},null,e)}}else"function"==typeof s&&(i.preventDefault(),e=l?Au(a,l):new FormData(a),zl(n,{pending:!0,data:e,method:a.method,action:s},s,e))},currentTarget:a}]})}}(i,e,r,n,a)}Fu(i,t)})}function Vu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function $u(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,s=a.stateNode;if(5!==(a=a.tag)&&26!==a&&27!==a||null===s||(null!=(a=Bt(e,n))&&r.unshift(Vu(e,a,s)),null!=(a=Bt(e,t))&&r.push(Vu(e,a,s))),3===e.tag)return r;e=e.return}return[]}function Qu(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Ku(e,t,n,r,a){for(var s=t._reactName,l=[];null!==n&&n!==r;){var i=n,o=i.alternate,c=i.stateNode;if(i=i.tag,null!==o&&o===r)break;5!==i&&26!==i&&27!==i||null===c||(o=c,a?null!=(c=Bt(n,s))&&l.unshift(Vu(n,c,o)):a||null!=(c=Bt(n,s))&&l.push(Vu(n,c,o))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Yu=/\r\n?/g,Xu=/\u0000|\uFFFD/g;function Zu(e){return("string"==typeof e?e:""+e).replace(Yu,"\n").replace(Xu,"")}function Gu(e,t){return t=Zu(t),Zu(e)===t}function Ju(){}function ed(e,t,n,a,s,l){switch(n){case"children":"string"==typeof a?"body"===t||"textarea"===t&&""===a||Nt(e,a):("number"==typeof a||"bigint"==typeof a)&&"body"!==t&&Nt(e,""+a);break;case"className":at(e,"class",a);break;case"tabIndex":at(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":at(e,n,a);break;case"style":Lt(e,a,l);break;case"data":if("object"!==t){at(e,"data",a);break}case"src":case"href":if(""===a&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==a||"function"==typeof a||"symbol"==typeof a||"boolean"==typeof a){e.removeAttribute(n);break}a=Pt(""+a),e.setAttribute(n,a);break;case"action":case"formAction":if("function"==typeof a){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"==typeof l&&("formAction"===n?("input"!==t&&ed(e,t,"name",s.name,s,null),ed(e,t,"formEncType",s.formEncType,s,null),ed(e,t,"formMethod",s.formMethod,s,null),ed(e,t,"formTarget",s.formTarget,s,null)):(ed(e,t,"encType",s.encType,s,null),ed(e,t,"method",s.method,s,null),ed(e,t,"target",s.target,s,null))),null==a||"symbol"==typeof a||"boolean"==typeof a){e.removeAttribute(n);break}a=Pt(""+a),e.setAttribute(n,a);break;case"onClick":null!=a&&(e.onclick=Ju);break;case"onScroll":null!=a&&Bu("scroll",e);break;case"onScrollEnd":null!=a&&Bu("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=a){if("object"!=typeof a||!("__html"in a))throw Error(r(61));if(null!=(n=a.__html)){if(null!=s.children)throw Error(r(60));e.innerHTML=n}}break;case"multiple":e.multiple=a&&"function"!=typeof a&&"symbol"!=typeof a;break;case"muted":e.muted=a&&"function"!=typeof a&&"symbol"!=typeof a;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==a||"function"==typeof a||"boolean"==typeof a||"symbol"==typeof a){e.removeAttribute("xlink:href");break}n=Pt(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,""+a):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===a?e.setAttribute(n,""):!1!==a&&null!=a&&"function"!=typeof a&&"symbol"!=typeof a?e.setAttribute(n,a):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=a&&"function"!=typeof a&&"symbol"!=typeof a&&!isNaN(a)&&1<=a?e.setAttribute(n,a):e.removeAttribute(n);break;case"rowSpan":case"start":null==a||"function"==typeof a||"symbol"==typeof a||isNaN(a)?e.removeAttribute(n):e.setAttribute(n,a);break;case"popover":Bu("beforetoggle",e),Bu("toggle",e),rt(e,"popover",a);break;case"xlinkActuate":st(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":st(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":st(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":st(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":st(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":st(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":st(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":st(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":st(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":rt(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&rt(e,n=Et.get(n)||n,a)}}function td(e,t,n,a,s,l){switch(n){case"style":Lt(e,a,l);break;case"dangerouslySetInnerHTML":if(null!=a){if("object"!=typeof a||!("__html"in a))throw Error(r(61));if(null!=(n=a.__html)){if(null!=s.children)throw Error(r(60));e.innerHTML=n}}break;case"children":"string"==typeof a?Nt(e,a):("number"==typeof a||"bigint"==typeof a)&&Nt(e,""+a);break;case"onScroll":null!=a&&Bu("scroll",e);break;case"onScrollEnd":null!=a&&Bu("scrollend",e);break;case"onClick":null!=a&&(e.onclick=Ju);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Ye.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(s=n.endsWith("Capture"),t=n.slice(2,s?n.length-7:void 0),"function"==typeof(l=null!=(l=e[Me]||null)?l[n]:null)&&e.removeEventListener(t,l,s),"function"!=typeof a)?n in e?e[n]=a:!0===a?e.setAttribute(n,""):rt(e,n,a):("function"!=typeof l&&null!==l&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,a,s)))}}function nd(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":Bu("error",e),Bu("load",e);var a,s=!1,l=!1;for(a in n)if(n.hasOwnProperty(a)){var i=n[a];if(null!=i)switch(a){case"src":s=!0;break;case"srcSet":l=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ed(e,t,a,i,n,null)}}return l&&ed(e,t,"srcSet",n.srcSet,n,null),void(s&&ed(e,t,"src",n.src,n,null));case"input":Bu("invalid",e);var o=a=i=l=null,c=null,u=null;for(s in n)if(n.hasOwnProperty(s)){var d=n[s];if(null!=d)switch(s){case"name":l=d;break;case"type":i=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":a=d;break;case"defaultValue":o=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(r(137,t));break;default:ed(e,t,s,d,n,null)}}return bt(e,a,o,c,u,i,l,!1),void ht(e);case"select":for(l in Bu("invalid",e),s=i=a=null,n)if(n.hasOwnProperty(l)&&null!=(o=n[l]))switch(l){case"value":a=o;break;case"defaultValue":i=o;break;case"multiple":s=o;default:ed(e,t,l,o,n,null)}return t=a,n=i,e.multiple=!!s,void(null!=t?wt(e,!!s,t,!1):null!=n&&wt(e,!!s,n,!0));case"textarea":for(i in Bu("invalid",e),a=l=s=null,n)if(n.hasOwnProperty(i)&&null!=(o=n[i]))switch(i){case"value":s=o;break;case"defaultValue":l=o;break;case"children":a=o;break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(r(91));break;default:ed(e,t,i,o,n,null)}return kt(e,s,l,a),void ht(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(s=n[c]))if("selected"===c)e.selected=s&&"function"!=typeof s&&"symbol"!=typeof s;else ed(e,t,c,s,n,null);return;case"dialog":Bu("beforetoggle",e),Bu("toggle",e),Bu("cancel",e),Bu("close",e);break;case"iframe":case"object":Bu("load",e);break;case"video":case"audio":for(s=0;s<Ru.length;s++)Bu(Ru[s],e);break;case"image":Bu("error",e),Bu("load",e);break;case"details":Bu("toggle",e);break;case"embed":case"source":case"link":Bu("error",e),Bu("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(s=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(r(137,t));default:ed(e,t,u,s,n,null)}return;default:if(Tt(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(s=n[d])&&td(e,t,d,s,n,void 0));return}}for(o in n)n.hasOwnProperty(o)&&(null!=(s=n[o])&&ed(e,t,o,s,n,null))}var rd=null,ad=null;function sd(e){return 9===e.nodeType?e:e.ownerDocument}function ld(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function id(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function od(e,t){return"textarea"===e||"noscript"===e||"string"==typeof t.children||"number"==typeof t.children||"bigint"==typeof t.children||"object"==typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var cd=null;var ud="function"==typeof setTimeout?setTimeout:void 0,dd="function"==typeof clearTimeout?clearTimeout:void 0,md="function"==typeof Promise?Promise:void 0,hd="function"==typeof queueMicrotask?queueMicrotask:void 0!==md?function(e){return md.resolve(null).then(e).catch(fd)}:ud;function fd(e){setTimeout(function(){throw e})}function pd(e){return"head"===e}function gd(e,t){var n=t,r=0,a=0;do{var s=n.nextSibling;if(e.removeChild(n),s&&8===s.nodeType)if("/$"===(n=s.data)){if(0<r&&8>r){n=r;var l=e.ownerDocument;if(1&n&&kd(l.documentElement),2&n&&kd(l.body),4&n)for(kd(n=l.head),l=n.firstChild;l;){var i=l.nextSibling,o=l.nodeName;l[Ue]||"SCRIPT"===o||"STYLE"===o||"LINK"===o&&"stylesheet"===l.rel.toLowerCase()||n.removeChild(l),l=i}}if(0===a)return e.removeChild(s),void Em(t);a--}else"$"===n||"$?"===n||"$!"===n?a++:r=n.charCodeAt(0)-48;else r=0;n=s}while(n);Em(t)}function xd(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":xd(n),We(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function yd(e){return"$!"===e.data||"$?"===e.data&&"complete"===e.ownerDocument.readyState}function bd(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}var vd=null;function wd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function jd(e,t,n){switch(t=sd(n),e){case"html":if(!(e=t.documentElement))throw Error(r(452));return e;case"head":if(!(e=t.head))throw Error(r(453));return e;case"body":if(!(e=t.body))throw Error(r(454));return e;default:throw Error(r(451))}}function kd(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);We(e)}var Nd=new Map,Sd=new Set;function Cd(e){return"function"==typeof e.getRootNode?e.getRootNode():9===e.nodeType?e:e.ownerDocument}var Ld=D.d;D.d={f:function(){var e=Ld.f(),t=qc();return e||t},r:function(e){var t=He(e);null!==t&&5===t.tag&&"form"===t.type?Ol(t):Ld.r(e)},D:function(e){Ld.D(e),Ed("dns-prefetch",e,null)},C:function(e,t){Ld.C(e,t),Ed("preconnect",e,t)},L:function(e,t,n){Ld.L(e,t,n);var r=Td;if(r&&e&&t){var a='link[rel="preload"][as="'+xt(t)+'"]';"image"===t&&n&&n.imageSrcSet?(a+='[imagesrcset="'+xt(n.imageSrcSet)+'"]',"string"==typeof n.imageSizes&&(a+='[imagesizes="'+xt(n.imageSizes)+'"]')):a+='[href="'+xt(e)+'"]';var s=a;switch(t){case"style":s=Pd(e);break;case"script":s=Od(e)}Nd.has(s)||(e=d({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),Nd.set(s,e),null!==r.querySelector(a)||"style"===t&&r.querySelector(zd(s))||"script"===t&&r.querySelector(Md(s))||(nd(t=r.createElement("link"),"link",e),Qe(t),r.head.appendChild(t)))}},m:function(e,t){Ld.m(e,t);var n=Td;if(n&&e){var r=t&&"string"==typeof t.as?t.as:"script",a='link[rel="modulepreload"][as="'+xt(r)+'"][href="'+xt(e)+'"]',s=a;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":s=Od(e)}if(!Nd.has(s)&&(e=d({rel:"modulepreload",href:e},t),Nd.set(s,e),null===n.querySelector(a))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(Md(s)))return}nd(r=n.createElement("link"),"link",e),Qe(r),n.head.appendChild(r)}}},X:function(e,t){Ld.X(e,t);var n=Td;if(n&&e){var r=$e(n).hoistableScripts,a=Od(e),s=r.get(a);s||((s=n.querySelector(Md(a)))||(e=d({src:e,async:!0},t),(t=Nd.get(a))&&Bd(e,t),Qe(s=n.createElement("script")),nd(s,"link",e),n.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},r.set(a,s))}},S:function(e,t,n){Ld.S(e,t,n);var r=Td;if(r&&e){var a=$e(r).hoistableStyles,s=Pd(e);t=t||"default";var l=a.get(s);if(!l){var i={loading:0,preload:null};if(l=r.querySelector(zd(s)))i.loading=5;else{e=d({rel:"stylesheet",href:e,"data-precedence":t},n),(n=Nd.get(s))&&Fd(e,n);var o=l=r.createElement("link");Qe(o),nd(o,"link",e),o._p=new Promise(function(e,t){o.onload=e,o.onerror=t}),o.addEventListener("load",function(){i.loading|=1}),o.addEventListener("error",function(){i.loading|=2}),i.loading|=4,Dd(l,t,r)}l={type:"stylesheet",instance:l,count:1,state:i},a.set(s,l)}}},M:function(e,t){Ld.M(e,t);var n=Td;if(n&&e){var r=$e(n).hoistableScripts,a=Od(e),s=r.get(a);s||((s=n.querySelector(Md(a)))||(e=d({src:e,async:!0,type:"module"},t),(t=Nd.get(a))&&Bd(e,t),Qe(s=n.createElement("script")),nd(s,"link",e),n.head.appendChild(s)),s={type:"script",instance:s,count:1,state:null},r.set(a,s))}}};var Td="undefined"==typeof document?null:document;function Ed(e,t,n){var r=Td;if(r&&"string"==typeof t&&t){var a=xt(t);a='link[rel="'+e+'"][href="'+a+'"]',"string"==typeof n&&(a+='[crossorigin="'+n+'"]'),Sd.has(a)||(Sd.add(a),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(a)&&(nd(t=r.createElement("link"),"link",e),Qe(t),r.head.appendChild(t)))}}function _d(e,t,n,a){var s,l,i,o,c=(c=$.current)?Cd(c):null;if(!c)throw Error(r(446));switch(e){case"meta":case"title":return null;case"style":return"string"==typeof n.precedence&&"string"==typeof n.href?(t=Pd(n.href),(a=(n=$e(c).hoistableStyles).get(t))||(a={type:"style",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"==typeof n.href&&"string"==typeof n.precedence){e=Pd(n.href);var u=$e(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(zd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),Nd.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},Nd.set(e,n),u||(s=c,l=e,i=n,o=d.state,s.querySelector('link[rel="preload"][as="style"]['+l+"]")?o.loading=1:(l=s.createElement("link"),o.preload=l,l.addEventListener("load",function(){return o.loading|=1}),l.addEventListener("error",function(){return o.loading|=2}),nd(l,"link",i),Qe(l),s.head.appendChild(l))))),t&&null===a)throw Error(r(528,""));return d}if(t&&null!==a)throw Error(r(529,""));return null;case"script":return t=n.async,"string"==typeof(n=n.src)&&t&&"function"!=typeof t&&"symbol"!=typeof t?(t=Od(n),(a=(n=$e(c).hoistableScripts).get(t))||(a={type:"script",instance:null,count:0,state:null},n.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(r(444,e))}}function Pd(e){return'href="'+xt(e)+'"'}function zd(e){return'link[rel="stylesheet"]['+e+"]"}function Ad(e){return d({},e,{"data-precedence":e.precedence,precedence:null})}function Od(e){return'[src="'+xt(e)+'"]'}function Md(e){return"script[async]"+e}function Rd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+xt(n.href)+'"]');if(a)return t.instance=a,Qe(a),a;var s=d({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Qe(a=(e.ownerDocument||e).createElement("style")),nd(a,"style",s),Dd(a,n.precedence,e),t.instance=a;case"stylesheet":s=Pd(n.href);var l=e.querySelector(zd(s));if(l)return t.state.loading|=4,t.instance=l,Qe(l),l;a=Ad(n),(s=Nd.get(s))&&Fd(a,s),Qe(l=(e.ownerDocument||e).createElement("link"));var i=l;return i._p=new Promise(function(e,t){i.onload=e,i.onerror=t}),nd(l,"link",a),t.state.loading|=4,Dd(l,n.precedence,e),t.instance=l;case"script":return l=Od(n.src),(s=e.querySelector(Md(l)))?(t.instance=s,Qe(s),s):(a=n,(s=Nd.get(l))&&Bd(a=d({},n),s),Qe(s=(e=e.ownerDocument||e).createElement("script")),nd(s,"link",a),e.head.appendChild(s),t.instance=s);case"void":return null;default:throw Error(r(443,t.type))}else"stylesheet"===t.type&&!(4&t.state.loading)&&(a=t.instance,t.state.loading|=4,Dd(a,n.precedence,e));return t.instance}function Dd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=r.length?r[r.length-1]:null,s=a,l=0;l<r.length;l++){var i=r[l];if(i.dataset.precedence===t)s=i;else if(s!==a)break}s?s.parentNode.insertBefore(e,s.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Fd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Bd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Id=null;function Ud(e,t,n){if(null===Id){var r=new Map,a=Id=new Map;a.set(n,r)}else(r=(a=Id).get(n))||(r=new Map,a.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),a=0;a<n.length;a++){var s=n[a];if(!(s[Ue]||s[Oe]||"link"===e&&"stylesheet"===s.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==s.namespaceURI){var l=s.getAttribute(t)||"";l=e+l;var i=r.get(l);i?i.push(s):r.set(l,[s])}}return r}function Wd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function qd(e){return!!("stylesheet"!==e.type||3&e.state.loading)}var Hd=null;function Vd(){}function $d(){if(this.count--,0===this.count)if(this.stylesheets)Kd(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var Qd=null;function Kd(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,Qd=new Map,t.forEach(Yd,e),Qd=null,$d.call(e))}function Yd(e,t){if(!(4&t.state.loading)){var n=Qd.get(e);if(n)var r=n.get(null);else{n=new Map,Qd.set(e,n);for(var a=e.querySelectorAll("link[data-precedence],style[data-precedence]"),s=0;s<a.length;s++){var l=a[s];"LINK"!==l.nodeName&&"not all"===l.getAttribute("media")||(n.set(l.dataset.precedence,l),r=l)}r&&n.set(null,r)}l=(a=t.instance).getAttribute("data-precedence"),(s=n.get(l)||r)===r&&n.set(null,a),n.set(l,a),this.count++,r=$d.bind(this),a.addEventListener("load",r),a.addEventListener("error",r),s?s.parentNode.insertBefore(a,s.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(a,e.firstChild),t.state.loading|=4}}var Xd={$$typeof:v,Provider:null,Consumer:null,_currentValue:F,_currentValue2:F,_threadCount:0};function Zd(e,t,n,r,a,s,l,i){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Ce(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Ce(0),this.hiddenUpdates=Ce(null),this.identifierPrefix=r,this.onUncaughtError=a,this.onCaughtError=s,this.onRecoverableError=l,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Gd(e,t,n,r,a,s,l,i,o,c,u,d){return e=new Zd(e,t,n,l,i,o,c,d),t=1,!0===s&&(t|=24),s=Fr(3,null,null,t),e.current=s,s.stateNode=e,(t=Ma()).refCount++,e.pooledCache=t,t.refCount++,s.memoizedState={element:r,isDehydrated:n,cache:t},as(s),e}function Jd(e){return e?e=Rr:Rr}function em(e,t,n,r,a,s){a=Jd(a),null===r.context?r.context=a:r.pendingContext=a,(r=ls(t)).payload={element:n},null!==(s=void 0===s?null:s)&&(r.callback=s),null!==(n=is(e,r,t))&&(Fc(n,0,t),os(n,e,t))}function tm(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function nm(e,t){tm(e,t),(e=e.alternate)&&tm(e,t)}function rm(e){if(13===e.tag){var t=Ar(e,67108864);null!==t&&Fc(t,0,67108864),nm(e,67108864)}}var am=!0;function sm(e,t,n,r){var a=R.T;R.T=null;var s=D.p;try{D.p=2,im(e,t,n,r)}finally{D.p=s,R.T=a}}function lm(e,t,n,r){var a=R.T;R.T=null;var s=D.p;try{D.p=8,im(e,t,n,r)}finally{D.p=s,R.T=a}}function im(e,t,n,r){if(am){var a=om(r);if(null===a)Hu(e,t,r,cm,n),vm(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return hm=wm(hm,e,t,n,r,a),!0;case"dragenter":return fm=wm(fm,e,t,n,r,a),!0;case"mouseover":return pm=wm(pm,e,t,n,r,a),!0;case"pointerover":var s=a.pointerId;return gm.set(s,wm(gm.get(s)||null,e,t,n,r,a)),!0;case"gotpointercapture":return s=a.pointerId,xm.set(s,wm(xm.get(s)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(vm(e,r),4&t&&-1<bm.indexOf(e)){for(;null!==a;){var s=He(a);if(null!==s)switch(s.tag){case 3:if((s=s.stateNode).current.memoizedState.isDehydrated){var l=ve(s.pendingLanes);if(0!==l){var i=s;for(i.pendingLanes|=2,i.entangledLanes|=2;l;){var o=1<<31-pe(l);i.entanglements[1]|=o,l&=~o}Nu(s),!(6&ac)&&(Nc=re()+500,Su(0))}}break;case 13:null!==(i=Ar(s,2))&&Fc(i,0,2),qc(),nm(s,2)}if(null===(s=om(r))&&Hu(e,t,r,cm,n),s===a)break;a=s}null!==a&&r.stopPropagation()}else Hu(e,t,r,null,n)}}function om(e){return um(e=At(e))}var cm=null;function um(e){if(cm=null,null!==(e=qe(e))){var t=s(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=l(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return cm=e,null}function dm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ae()){case se:return 2;case le:return 8;case ie:case oe:return 32;case ce:return 268435456;default:return 32}default:return 32}}var mm=!1,hm=null,fm=null,pm=null,gm=new Map,xm=new Map,ym=[],bm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function vm(e,t){switch(e){case"focusin":case"focusout":hm=null;break;case"dragenter":case"dragleave":fm=null;break;case"mouseover":case"mouseout":pm=null;break;case"pointerover":case"pointerout":gm.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":xm.delete(t.pointerId)}}function wm(e,t,n,r,a,s){return null===e||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[a]},null!==t&&(null!==(t=He(t))&&rm(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function jm(e){var t=qe(e.target);if(null!==t){var n=s(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=l(n)))return e.blockedOn=t,void function(e,t){var n=D.p;try{return D.p=e,t()}finally{D.p=n}}(e.priority,function(){if(13===n.tag){var e=Rc();e=_e(e);var t=Ar(n,e);null!==t&&Fc(t,0,e),nm(n,e)}})}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function km(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=om(e.nativeEvent);if(null!==n)return null!==(t=He(n))&&rm(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);zt=r,n.target.dispatchEvent(r),zt=null,t.shift()}return!0}function Nm(e,t,n){km(e)&&n.delete(t)}function Sm(){mm=!1,null!==hm&&km(hm)&&(hm=null),null!==fm&&km(fm)&&(fm=null),null!==pm&&km(pm)&&(pm=null),gm.forEach(Nm),xm.forEach(Nm)}function Cm(t,n){t.blockedOn===n&&(t.blockedOn=null,mm||(mm=!0,e.unstable_scheduleCallback(e.unstable_NormalPriority,Sm)))}var Lm=null;function Tm(t){Lm!==t&&(Lm=t,e.unstable_scheduleCallback(e.unstable_NormalPriority,function(){Lm===t&&(Lm=null);for(var e=0;e<t.length;e+=3){var n=t[e],r=t[e+1],a=t[e+2];if("function"!=typeof r){if(null===um(r||n))continue;break}var s=He(n);null!==s&&(t.splice(e,3),e-=3,zl(s,{pending:!0,data:a,method:n.method,action:r},r,a))}}))}function Em(e){function t(t){return Cm(t,e)}null!==hm&&Cm(hm,e),null!==fm&&Cm(fm,e),null!==pm&&Cm(pm,e),gm.forEach(t),xm.forEach(t);for(var n=0;n<ym.length;n++){var r=ym[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<ym.length&&null===(n=ym[0]).blockedOn;)jm(n),null===n.blockedOn&&ym.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var a=n[r],s=n[r+1],l=a[Me]||null;if("function"==typeof s)l||Tm(n);else if(l){var i=null;if(s&&s.hasAttribute("formAction")){if(a=s,l=s[Me]||null)i=l.formAction;else if(null!==um(a))continue}else i=l.action;"function"==typeof i?n[r+1]=i:(n.splice(r,3),r-=3),Tm(n)}}}function _m(e){this._internalRoot=e}function Pm(e){this._internalRoot=e}Pm.prototype.render=_m.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(r(409));em(t.current,Rc(),e,t,null,null)},Pm.prototype.unmount=_m.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;em(e.current,2,null,e,null,null),qc(),t[Re]=null}},Pm.prototype.unstable_scheduleHydration=function(e){if(e){var t=ze();e={blockedOn:null,target:e,priority:t};for(var n=0;n<ym.length&&0!==t&&t<ym[n].priority;n++);ym.splice(n,0,e),0===n&&jm(e)}};var zm=t.version;if("19.1.1"!==zm)throw Error(r(527,zm,"19.1.1"));D.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"==typeof e.render)throw Error(r(188));throw e=Object.keys(e).join(","),Error(r(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=s(e)))throw Error(r(188));return t!==e?null:e}for(var n=e,a=t;;){var l=n.return;if(null===l)break;var i=l.alternate;if(null===i){if(null!==(a=l.return)){n=a;continue}break}if(l.child===i.child){for(i=l.child;i;){if(i===n)return c(l),e;if(i===a)return c(l),t;i=i.sibling}throw Error(r(188))}if(n.return!==a.return)n=l,a=i;else{for(var o=!1,u=l.child;u;){if(u===n){o=!0,n=l,a=i;break}if(u===a){o=!0,a=l,n=i;break}u=u.sibling}if(!o){for(u=i.child;u;){if(u===n){o=!0,n=i,a=l;break}if(u===a){o=!0,a=i,n=l;break}u=u.sibling}if(!o)throw Error(r(189))}}if(n.alternate!==a)throw Error(r(190))}if(3!==n.tag)throw Error(r(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?u(e):null)?null:e.stateNode};var Am={bundleType:0,version:"19.1.1",rendererPackageName:"react-dom",currentDispatcherRef:R,reconcilerVersion:"19.1.1"};if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var Om=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Om.isDisabled&&Om.supportsFiber)try{me=Om.inject(Am),he=Om}catch(Rm){}}return L.createRoot=function(e,t){if(!a(e))throw Error(r(299));var n=!1,s="",l=bi,i=vi,o=wi;return null!=t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(s=t.identifierPrefix),void 0!==t.onUncaughtError&&(l=t.onUncaughtError),void 0!==t.onCaughtError&&(i=t.onCaughtError),void 0!==t.onRecoverableError&&(o=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Gd(e,1,!1,null,0,n,s,l,i,o,0,null),e[Re]=t.current,Wu(e),new _m(t)},L.hydrateRoot=function(e,t,n){if(!a(e))throw Error(r(299));var s=!1,l="",i=bi,o=vi,c=wi,u=null;return null!=n&&(!0===n.unstable_strictMode&&(s=!0),void 0!==n.identifierPrefix&&(l=n.identifierPrefix),void 0!==n.onUncaughtError&&(i=n.onUncaughtError),void 0!==n.onCaughtError&&(o=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Gd(e,1,!0,t,0,s,l,i,o,c,0,u)).context=Jd(null),n=t.current,(l=ls(s=_e(s=Rc()))).callback=null,is(n,l,s),n=s,t.current.lanes=n,Le(t,n),Nu(t),e[Re]=t.current,Wu(e),new Pm(t)},L.version="19.1.1",L}var z=(N||(N=1,function e(){if("undefined"!=typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),C.exports=P()),C.exports);const A=c.create({baseURL:"http://localhost:6000/api/v1",headers:{"Content-Type":"application/json"}});A.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));let O=!1,M=[];const R=(e,t=null)=>{M.forEach(({resolve:n,reject:r})=>{e?r(e):n(t)}),M=[]};A.interceptors.response.use(e=>e,async e=>{const t=e.config;if(401===e.response?.status&&!t._retry){if(O)return new Promise((e,t)=>{M.push({resolve:e,reject:t})}).then(()=>A(t)).catch(e=>Promise.reject(e));t._retry=!0,O=!0;try{const n=await Me.dispatch(U());if(U.fulfilled.match(n)){const{token:e,refreshToken:r}=n.payload;return Me.dispatch($({token:e,refreshToken:r})),t.headers.Authorization=`Bearer ${e}`,R(null,e),A(t)}return R(e,null),Me.dispatch(V()),window.location.href="/login",Promise.reject(e)}catch(n){return R(n,null),Me.dispatch(V()),window.location.href="/login",Promise.reject(n)}finally{O=!1}}return Promise.reject(e)});const D=()=>A.get("/admin/dashboard/stats"),F={user:null,token:localStorage.getItem("token"),refreshToken:localStorage.getItem("refreshToken"),isAuthenticated:!!localStorage.getItem("token"),loading:!1,error:null},B=e("auth/sendCode",async(e,{rejectWithValue:t})=>{try{return(await A.post("/auth/send-code",{email:e})).data}catch(n){return t(n.response?.data?.message||"发送验证码失败")}}),I=e("auth/verifyCode",async({email:e,code:t},{rejectWithValue:n})=>{try{return(await A.post("/auth/verify-code",{email:e,code:t})).data}catch(r){return n(r.response?.data?.message||"登录失败")}}),U=e("auth/refreshToken",async(e,{getState:t,rejectWithValue:n})=>{try{const e=t().auth.refreshToken;if(!e)throw new Error("No refresh token");return(await A.post("/auth/refresh",{refreshToken:e})).data}catch(r){return n(r.response?.data?.message||"刷新令牌失败")}}),W=e("auth/logout",async()=>{try{return await A.post("/auth/logout"),!0}catch(e){return!0}}),q=t({name:"auth",initialState:F,reducers:{clearError:e=>{e.error=null},resetAuth:e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.loading=!1,e.error=null,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")},setTokens:(e,t)=>{e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,localStorage.setItem("token",t.payload.token),localStorage.setItem("refreshToken",t.payload.refreshToken)},logout:e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.loading=!1,e.error=null,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")}},extraReducers:e=>{e.addCase(B.pending,e=>{e.loading=!0,e.error=null}).addCase(B.fulfilled,e=>{e.loading=!1}).addCase(B.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}),e.addCase(I.pending,e=>{e.loading=!0,e.error=null}).addCase(I.fulfilled,(e,t)=>{e.user=t.payload.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.loading=!1,e.error=null,localStorage.setItem("token",t.payload.token),localStorage.setItem("refreshToken",t.payload.refreshToken)}).addCase(I.rejected,(e,t)=>{e.loading=!1,e.error=t.payload}),e.addCase(U.fulfilled,(e,t)=>{e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,localStorage.setItem("token",t.payload.token),localStorage.setItem("refreshToken",t.payload.refreshToken)}).addCase(U.rejected,e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")}),e.addCase(W.fulfilled,e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")})}}),{clearError:H,resetAuth:V,setTokens:$,logout:Q}=q.actions,K=q.reducer,Y=e("translation/fetchLanguages",async(e,{rejectWithValue:t})=>{try{return(await A.get("/translation/languages")).data}catch(n){return t(n.response?.data?.message||"获取语言列表失败")}}),X=e("translation/fetchHistory",async({page:e,limit:t},{rejectWithValue:n})=>{try{return(await A.get("/translation/history",{params:{page:e,limit:t}})).data}catch(r){return n(r.response?.data?.message||"获取翻译历史失败")}}),Z=e("translation/fetchFavorites",async(e,{rejectWithValue:t})=>{try{return(await A.get("/translation/favorites")).data}catch(n){return t(n.response?.data?.message||"获取收藏翻译失败")}}),G=e("translation/translateText",async(e,{rejectWithValue:t})=>{try{return(await A.post("/translation/translate",e)).data}catch(n){return t(n.response?.data?.message||"翻译失败")}}),J=e("translation/toggleFavorite",async(e,{rejectWithValue:t})=>{try{return(await A.post(`/translation/${e}/favorite`)).data}catch(n){return t(n.response?.data?.message||"操作失败")}}),ee=t({name:"translation",initialState:{currentTranslation:{sourceText:"",translatedText:"",sourceLanguage:"auto",targetLanguage:"en",isTranslating:!1,progress:0,error:null},history:{translations:[],total:0,loading:!1,page:1,limit:20},favorites:[],languages:{popular:[],all:[],loading:!1},websocket:{connected:!1,connecting:!1}},reducers:{setSourceText:(e,t)=>{e.currentTranslation.sourceText=t.payload},setSourceLanguage:(e,t)=>{e.currentTranslation.sourceLanguage=t.payload},setTargetLanguage:(e,t)=>{e.currentTranslation.targetLanguage=t.payload},setTranslatedText:(e,t)=>{e.currentTranslation.translatedText=t.payload},startTranslation:e=>{e.currentTranslation.isTranslating=!0,e.currentTranslation.progress=0,e.currentTranslation.error=null,e.currentTranslation.translatedText=""},updateTranslationProgress:(e,t)=>{e.currentTranslation.progress=t.payload},completeTranslation:(e,t)=>{e.currentTranslation.isTranslating=!1,e.currentTranslation.progress=100,e.currentTranslation.translatedText=t.payload,e.currentTranslation.error=null},failTranslation:(e,t)=>{e.currentTranslation.isTranslating=!1,e.currentTranslation.progress=0,e.currentTranslation.error=t.payload},clearTranslationError:e=>{e.currentTranslation.error=null},setWebSocketConnecting:(e,t)=>{e.websocket.connecting=t.payload},setWebSocketConnected:(e,t)=>{e.websocket.connected=t.payload,t.payload&&(e.websocket.connecting=!1)},addToHistory:(e,t)=>{e.history.translations.unshift(t.payload),e.history.total+=1},updateHistoryTranslation:(e,t)=>{const n=e.history.translations.findIndex(e=>e.id===t.payload.id);-1!==n&&(e.history.translations[n]=t.payload);const r=e.favorites.findIndex(e=>e.id===t.payload.id);-1!==r?t.payload.isFavorite?e.favorites[r]=t.payload:e.favorites.splice(r,1):t.payload.isFavorite&&e.favorites.unshift(t.payload)}},extraReducers:e=>{e.addCase(Y.pending,e=>{e.languages.loading=!0}).addCase(Y.fulfilled,(e,t)=>{e.languages.loading=!1,e.languages.popular=t.payload.popular,e.languages.all=t.payload.all}).addCase(Y.rejected,e=>{e.languages.loading=!1}),e.addCase(X.pending,e=>{e.history.loading=!0}).addCase(X.fulfilled,(e,t)=>{e.history.loading=!1,e.history.translations=t.payload.translations,e.history.total=t.payload.total}).addCase(X.rejected,e=>{e.history.loading=!1}),e.addCase(Z.fulfilled,(e,t)=>{e.favorites=t.payload}),e.addCase(G.pending,e=>{e.currentTranslation.isTranslating=!0,e.currentTranslation.error=null}).addCase(G.fulfilled,(e,t)=>{e.currentTranslation.isTranslating=!1,e.currentTranslation.translatedText=t.payload.translatedText,e.currentTranslation.progress=100,e.history.translations.unshift(t.payload),e.history.total+=1}).addCase(G.rejected,(e,t)=>{e.currentTranslation.isTranslating=!1,e.currentTranslation.error=t.payload}),e.addCase(J.fulfilled,(e,t)=>{const n=t.payload,r=e.history.translations.findIndex(e=>e.id===n.id);-1!==r&&(e.history.translations[r]=n);const a=e.favorites.findIndex(e=>e.id===n.id);n.isFavorite?-1===a?e.favorites.unshift(n):e.favorites[a]=n:-1!==a&&e.favorites.splice(a,1)})}}),{setSourceText:te,setSourceLanguage:ne,setTargetLanguage:re,setTranslatedText:ae,startTranslation:se,updateTranslationProgress:le,completeTranslation:ie,failTranslation:oe,clearTranslationError:ce,setWebSocketConnecting:ue,setWebSocketConnected:de,addToHistory:me,updateHistoryTranslation:he}=ee.actions,fe=ee.reducer,pe=e("subscription/fetchPlans",async(e,{rejectWithValue:t})=>{try{return(await A.get("/subscription/plans")).data.data}catch(n){return t(n.response?.data?.message||"获取订阅套餐失败")}}),ge=e("subscription/fetchCurrent",async(e,{rejectWithValue:t})=>{try{return(await A.get("/subscription/current")).data.data}catch(n){return t(n.response?.data?.message||"获取当前订阅失败")}}),xe=e("subscription/fetchUsage",async(e,{rejectWithValue:t})=>{try{return(await A.get("/subscription/usage")).data.data}catch(n){return t(n.response?.data?.message||"获取使用量统计失败")}}),ye=e("subscription/create",async(e,{rejectWithValue:t})=>{try{return(await A.post("/subscription/subscribe",e)).data.data}catch(n){return t(n.response?.data?.message||"创建订阅失败")}}),be=e("subscription/cancel",async(e,{rejectWithValue:t})=>{try{return(await A.delete("/subscription/cancel",{data:{reason:e}})).data}catch(n){return t(n.response?.data?.message||"取消订阅失败")}}),ve=t({name:"subscription",initialState:{plans:{list:[],loading:!1,error:null},currentSubscription:{data:null,loading:!1,error:null},usage:{stats:null,loading:!1,error:null},selectedPlan:null,selectedBillingCycle:"monthly"},reducers:{setSelectedPlan:(e,t)=>{e.selectedPlan=t.payload},setSelectedBillingCycle:(e,t)=>{e.selectedBillingCycle=t.payload},clearErrors:e=>{e.plans.error=null,e.currentSubscription.error=null,e.usage.error=null}},extraReducers:e=>{e.addCase(pe.pending,e=>{e.plans.loading=!0,e.plans.error=null}).addCase(pe.fulfilled,(e,t)=>{e.plans.loading=!1,e.plans.list=t.payload}).addCase(pe.rejected,(e,t)=>{e.plans.loading=!1,e.plans.error=t.payload}),e.addCase(ge.pending,e=>{e.currentSubscription.loading=!0,e.currentSubscription.error=null}).addCase(ge.fulfilled,(e,t)=>{e.currentSubscription.loading=!1,e.currentSubscription.data=t.payload}).addCase(ge.rejected,(e,t)=>{e.currentSubscription.loading=!1,e.currentSubscription.error=t.payload}),e.addCase(xe.pending,e=>{e.usage.loading=!0,e.usage.error=null}).addCase(xe.fulfilled,(e,t)=>{e.usage.loading=!1,e.usage.stats=t.payload}).addCase(xe.rejected,(e,t)=>{e.usage.loading=!1,e.usage.error=t.payload}),e.addCase(ye.pending,e=>{e.currentSubscription.loading=!0,e.currentSubscription.error=null}).addCase(ye.fulfilled,(e,t)=>{e.currentSubscription.loading=!1,e.currentSubscription.data=t.payload}).addCase(ye.rejected,(e,t)=>{e.currentSubscription.loading=!1,e.currentSubscription.error=t.payload}),e.addCase(be.fulfilled,e=>{e.currentSubscription.data&&(e.currentSubscription.data.status="cancelled",e.currentSubscription.data.autoRenewal=!1)})}}),{setSelectedPlan:we,setSelectedBillingCycle:je,clearErrors:ke}=ve.actions,Ne=ve.reducer,Se=e("payment/create",async(e,{rejectWithValue:t})=>{try{return(await A.post("/payment/create",e)).data.data}catch(n){return t(n.response?.data?.message||"创建支付失败")}}),Ce=e("payment/validateCoupon",async(e,{rejectWithValue:t})=>{try{return(await A.post("/coupon/validate",e)).data.data}catch(n){return t(n.response?.data?.message||"验证优惠码失败")}}),Le=e("payment/checkStatus",async(e,{rejectWithValue:t})=>{try{return(await A.get(`/payment/${e}/status`)).data.data}catch(n){return t(n.response?.data?.message||"查询支付状态失败")}}),Te=e("payment/fetchHistory",async(e={},{rejectWithValue:t})=>{try{return(await A.get("/payment",{params:e})).data.data}catch(n){return t(n.response?.data?.message||"获取支付历史失败")}}),Ee=t({name:"payment",initialState:{currentPayment:{data:null,loading:!1,error:null},coupon:{code:"",validation:null,loading:!1,error:null},paymentHistory:{list:[],loading:!1,error:null}},reducers:{setCouponCode:(e,t)=>{e.coupon.code=t.payload,t.payload||(e.coupon.validation=null,e.coupon.error=null)},clearCouponValidation:e=>{e.coupon.validation=null,e.coupon.error=null},clearPaymentError:e=>{e.currentPayment.error=null},clearCurrentPayment:e=>{e.currentPayment.data=null,e.currentPayment.error=null},updatePaymentStatus:(e,t)=>{e.currentPayment.data?.paymentId===t.payload.paymentId&&(e.currentPayment.data.status=t.payload.status)}},extraReducers:e=>{e.addCase(Se.pending,e=>{e.currentPayment.loading=!0,e.currentPayment.error=null}).addCase(Se.fulfilled,(e,t)=>{e.currentPayment.loading=!1,e.currentPayment.data=t.payload}).addCase(Se.rejected,(e,t)=>{e.currentPayment.loading=!1,e.currentPayment.error=t.payload}),e.addCase(Ce.pending,e=>{e.coupon.loading=!0,e.coupon.error=null}).addCase(Ce.fulfilled,(e,t)=>{e.coupon.loading=!1,e.coupon.validation=t.payload}).addCase(Ce.rejected,(e,t)=>{e.coupon.loading=!1,e.coupon.error=t.payload,e.coupon.validation={valid:!1,error:t.payload}}),e.addCase(Le.fulfilled,(e,t)=>{e.currentPayment.data&&(e.currentPayment.data.status=t.payload.status)}),e.addCase(Te.pending,e=>{e.paymentHistory.loading=!0,e.paymentHistory.error=null}).addCase(Te.fulfilled,(e,t)=>{e.paymentHistory.loading=!1,e.paymentHistory.list=t.payload}).addCase(Te.rejected,(e,t)=>{e.paymentHistory.loading=!1,e.paymentHistory.error=t.payload})}}),{setCouponCode:_e,clearCouponValidation:Pe,clearPaymentError:ze,clearCurrentPayment:Ae,updatePaymentStatus:Oe}=Ee.actions,Me=n({reducer:{auth:K,translation:fe,subscription:Ne,payment:Ee.reducer}}),Re=()=>r(),De=a,Fe=({children:e})=>{const t=Re();return s.useEffect(()=>{(async()=>{const e=localStorage.getItem("token"),n=localStorage.getItem("refreshToken");if(e&&n)try{await A.post("/auth/me")}catch(r){try{await t(U()).unwrap()}catch(a){t(V())}}else(e||n)&&t(V())})()},[t]),S.jsx(S.Fragment,{children:e})};class Be extends s.Component{resetTimeoutId=null;constructor(e){super(e),this.state={hasError:!1,error:null,errorInfo:null,eventId:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t);const n=`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`;this.setState({errorInfo:t,eventId:n}),this.props.onError&&this.props.onError(e,t),this.reportError(e,t,n)}componentDidUpdate(e){const{resetOnPropsChange:t,resetKeys:n}=this.props,{hasError:r}=this.state;r&&e.resetKeys!==n&&t&&this.resetErrorBoundary()}componentWillUnmount(){this.resetTimeoutId&&clearTimeout(this.resetTimeoutId)}reportError=async(e,t,n)=>{try{const r={message:e.message,stack:e.stack,componentStack:t.componentStack,eventId:n,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent,url:window.location.href};fetch("/api/v1/errors/report",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}).catch(()=>{})}catch(r){console.error("Failed to report error:",r)}};resetErrorBoundary=()=>{this.setState({hasError:!1,error:null,errorInfo:null,eventId:null})};handleRetry=()=>{this.resetErrorBoundary()};handleReload=()=>{window.location.reload()};copyErrorDetails=()=>{const{error:e,errorInfo:t,eventId:n}=this.state,r={eventId:n,message:e?.message,stack:e?.stack,componentStack:t?.componentStack,timestamp:(new Date).toISOString(),url:window.location.href};navigator.clipboard.writeText(JSON.stringify(r,null,2)).then(()=>{alert("错误详情已复制到剪贴板")}).catch(()=>{alert("复制失败，请手动复制错误信息")})};render(){return this.state.hasError?this.props.fallback?this.props.fallback:S.jsx("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:S.jsx("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:S.jsxs("div",{className:"bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10",children:[S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100",children:S.jsx("svg",{className:"h-6 w-6 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),S.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:"出现了一些问题"}),S.jsx("p",{className:"mt-2 text-center text-sm text-gray-600",children:"应用遇到了意外错误，我们已经记录了这个问题。"}),this.state.eventId&&S.jsxs("p",{className:"mt-1 text-center text-xs text-gray-500",children:["错误ID: ",this.state.eventId]})]}),S.jsxs("div",{className:"mt-8 space-y-4",children:[S.jsx("button",{onClick:this.handleRetry,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"重试"}),S.jsx("button",{onClick:this.handleReload,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"刷新页面"}),!1]})]})})}):this.props.children}}const Ie="NETWORK_ERROR",Ue="TIMEOUT_ERROR",We="CONNECTION_ERROR",qe="UNAUTHORIZED",He="FORBIDDEN",Ve="TOKEN_EXPIRED",$e="VALIDATION_ERROR",Qe="RESOURCE_NOT_FOUND",Ke="RESOURCE_CONFLICT",Ye="INTERNAL_ERROR",Xe="SERVICE_UNAVAILABLE",Ze="UNKNOWN",Ge="TRANSLATION_FAILED",Je="LANGUAGE_NOT_SUPPORTED",et="TEXT_TOO_LONG",tt="QUOTA_EXCEEDED",nt="PAYMENT_FAILED",rt="INVALID_COUPON",at="SUBSCRIPTION_EXPIRED",st={[Ie]:"网络连接失败，请检查网络设置",[Ue]:"请求超时，请稍后重试",[We]:"无法连接到服务器",[qe]:"未授权访问，请重新登录",[He]:"权限不足，无法执行此操作",[Ve]:"登录已过期，请重新登录",[$e]:"输入数据格式错误",[Qe]:"请求的资源不存在",[Ke]:"资源冲突，请刷新后重试",[Ye]:"系统内部错误，请稍后重试",[Xe]:"服务暂时不可用，请稍后重试",[Ge]:"翻译失败，请重试",[Je]:"不支持的语言",[et]:"文本长度超出限制",[tt]:"使用配额已用完",[nt]:"支付失败，请重试",[rt]:"优惠券无效或已过期",[at]:"订阅已过期，请续费",[Ze]:"未知错误，请稍后重试"};class lt{static parseApiError(e){const t=(new Date).toISOString();if(!e.response)return{code:Ie,message:st[Ie],timestamp:t,stack:e.stack};const{status:n,data:r}=e.response,a=r;let s,l;switch(n){case 400:s=$e,l=a.message||st[$e];break;case 401:s=qe,l=st[qe];break;case 403:s=He,l=st[He];break;case 404:s=Qe,l=st[Qe];break;case 409:s=Ke,l=st[Ke];break;case 429:s=tt,l=st[tt];break;case 500:s=Ye,l=st[Ye];break;case 503:s=Xe,l=st[Xe];break;default:s=Ye,l=a.message||st[Ye]}return{code:s,message:l,details:a,timestamp:t,stack:e.stack}}static parseError(e){const t=(new Date).toISOString();return e instanceof u?this.parseApiError(e):e instanceof Error?{code:Ye,message:e.message||st[Ye],timestamp:t,stack:e.stack}:{code:Ye,message:st[Ye],details:e,timestamp:t}}static getUserMessage(e){return st[e.code]||e.message||"发生未知错误"}static isRetryableError(e){return[Ie,Ue,We,Ye,Xe].includes(e.code)}static requiresReauth(e){return[qe,Ve].includes(e.code)}static logError(e,t){const n={...e,context:t,userAgent:navigator.userAgent,url:window.location.href};this.reportToMonitoring(n)}static async reportToMonitoring(e){try{await fetch("/api/v1/errors/report",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})}catch(reportError){console.error("Failed to report error to monitoring service:",reportError)}}}const it=({size:e="md",variant:t="full",className:n=""})=>{const r={sm:"h-6",md:"h-8",lg:"h-12",xl:"h-16"},a={sm:"text-lg",md:"text-xl",lg:"text-2xl",xl:"text-3xl"},s=()=>S.jsx("div",{className:`${r[e]} aspect-square flex items-center justify-center bg-gradient-to-br from-primary-500 to-accent-500 rounded-xl shadow-soft`,children:S.jsxs("svg",{viewBox:"0 0 24 24",fill:"none",className:"w-3/5 h-3/5 text-white",xmlns:"http://www.w3.org/2000/svg",children:[S.jsx("path",{d:"M8 3C5.79086 3 4 4.79086 4 7C4 8.1 4.9 9 6 9C7.1 9 8 8.1 8 7C8 6.44772 8.44772 6 9 6H12C13.1046 6 14 6.89543 14 8C14 9.10457 13.1046 10 12 10H9C6.79086 10 5 11.7909 5 14C5 16.2091 6.79086 18 9 18H12C14.2091 18 16 16.2091 16 14C16 12.9 15.1 12 14 12C12.9 12 12 12.9 12 14C12 14.5523 11.5523 15 11 15H9C7.89543 15 7 14.1046 7 13C7 11.8954 7.89543 11 9 11H12C14.2091 11 16 9.20914 16 7C16 4.79086 14.2091 3 12 3H8Z",fill:"currentColor"}),S.jsx("path",{d:"M18 8L22 12L18 16M22 12H20",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"}),S.jsx("path",{d:"M6 8L2 12L6 16M2 12H4",stroke:"currentColor",strokeWidth:"1.5",strokeLinecap:"round",strokeLinejoin:"round"})]})}),l=()=>S.jsx("span",{className:`${a[e]} font-bold text-gradient`,children:"SynText"});return"icon"===t?S.jsx(s,{}):"text"===t?S.jsx(l,{}):S.jsxs("div",{className:`flex items-center space-x-3 ${n}`,children:[S.jsx(s,{}),S.jsx(l,{})]})},ot=()=>{const[e,t]=s.useState(""),[n,r]=s.useState(""),[a,l]=s.useState(!1),[i,o]=s.useState(0),[c,u]=s.useState(""),m=Re(),h=d(),{loading:f,error:p,isAuthenticated:g}=De(e=>e.auth);s.useEffect(()=>{g&&h("/",{replace:!0})},[g,h]),s.useEffect(()=>()=>{m(H())},[m]);const x=async()=>{if(!e||f)return;u("");const t=await m(B(e));if(B.fulfilled.match(t)){l(!0),o(60);const e=setInterval(()=>{o(t=>t<=1?(clearInterval(e),0):t-1)},1e3)}else u(t.payload)};return S.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4",children:S.jsxs("div",{className:"max-w-md w-full space-y-8",children:[S.jsxs("div",{className:"text-center",children:[S.jsx(it,{size:"lg"}),S.jsx("h2",{className:"mt-6 text-3xl font-bold text-gray-900",children:"登录到 SynText"}),S.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"使用邮箱验证码登录"})]}),S.jsxs("div",{className:"bg-white py-8 px-6 shadow rounded-lg border border-gray-200",children:[S.jsxs("form",{className:"space-y-6",onSubmit:e=>e.preventDefault(),children:[S.jsxs("div",{children:[S.jsx("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),S.jsx("input",{id:"email",name:"email",type:"email",autoComplete:"email",required:!0,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500",placeholder:"请输入您的邮箱地址",value:e,onChange:e=>t(e.target.value),disabled:a})]}),a&&S.jsxs("div",{children:[S.jsx("label",{htmlFor:"code",className:"block text-sm font-medium text-gray-700 mb-2",children:"验证码"}),S.jsx("input",{id:"code",name:"code",type:"text",required:!0,maxLength:6,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-center text-lg font-mono tracking-widest",placeholder:"请输入6位验证码",value:n,onChange:e=>r(e.target.value.replace(/\D/g,"").slice(0,6))}),S.jsxs("p",{className:"text-xs text-gray-500 text-center mt-2",children:["验证码已发送至 ",S.jsx("span",{className:"font-medium text-blue-600",children:e})]})]}),(c||p)&&S.jsx("div",{className:"bg-red-50 border border-red-200 rounded-md p-3",children:S.jsx("div",{className:"flex",children:S.jsx("div",{className:"text-sm text-red-700",children:c||p})})}),S.jsx("div",{className:"space-y-4",children:a?S.jsxs("div",{className:"space-y-4",children:[S.jsx("button",{type:"button",onClick:async()=>{if(!e||!n||f)return;const t=await m(I({email:e,code:n}));I.fulfilled.match(t)&&h("/",{replace:!0})},disabled:!n||6!==n.length||f,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"登录中...":"立即登录"}),S.jsx("button",{type:"button",onClick:x,disabled:i>0||f,className:"w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:i>0?`重新发送 (${i}s)`:"重新发送验证码"})]}):S.jsx("button",{type:"button",onClick:x,disabled:!e||f,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"发送中...":"发送验证码"})})]}),S.jsx("div",{className:"mt-6 text-center",children:S.jsxs("p",{className:"text-xs text-gray-500",children:["登录即表示您同意我们的",S.jsx("a",{href:"#",className:"text-blue-600 hover:text-blue-500 font-medium",children:"服务条款"}),"和",S.jsx("a",{href:"#",className:"text-blue-600 hover:text-blue-500 font-medium",children:"隐私政策"})]})})]})]})})},ct=Object.create(null);ct.open="0",ct.close="1",ct.ping="2",ct.pong="3",ct.message="4",ct.upgrade="5",ct.noop="6";const ut=Object.create(null);Object.keys(ct).forEach(e=>{ut[ct[e]]=e});const dt={type:"error",data:"parser error"},mt="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===Object.prototype.toString.call(Blob),ht="function"==typeof ArrayBuffer,ft=e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer,pt=({type:e,data:t},n,r)=>mt&&t instanceof Blob?n?r(t):gt(t,r):ht&&(t instanceof ArrayBuffer||ft(t))?n?r(t):gt(new Blob([t]),r):r(ct[e]+(t||"")),gt=(e,t)=>{const n=new FileReader;return n.onload=function(){const e=n.result.split(",")[1];t("b"+(e||""))},n.readAsDataURL(e)};function xt(e){return e instanceof Uint8Array?e:e instanceof ArrayBuffer?new Uint8Array(e):new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}let yt;const bt="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",vt="undefined"==typeof Uint8Array?[]:new Uint8Array(256);for(let Zn=0;Zn<64;Zn++)vt[bt.charCodeAt(Zn)]=Zn;const wt="function"==typeof ArrayBuffer,jt=(e,t)=>{if("string"!=typeof e)return{type:"message",data:Nt(e,t)};const n=e.charAt(0);if("b"===n)return{type:"message",data:kt(e.substring(1),t)};return ut[n]?e.length>1?{type:ut[n],data:e.substring(1)}:{type:ut[n]}:dt},kt=(e,t)=>{if(wt){const n=(e=>{let t,n,r,a,s,l=.75*e.length,i=e.length,o=0;"="===e[e.length-1]&&(l--,"="===e[e.length-2]&&l--);const c=new ArrayBuffer(l),u=new Uint8Array(c);for(t=0;t<i;t+=4)n=vt[e.charCodeAt(t)],r=vt[e.charCodeAt(t+1)],a=vt[e.charCodeAt(t+2)],s=vt[e.charCodeAt(t+3)],u[o++]=n<<2|r>>4,u[o++]=(15&r)<<4|a>>2,u[o++]=(3&a)<<6|63&s;return c})(e);return Nt(n,t)}return{base64:!0,data:e}},Nt=(e,t)=>"blob"===t?e instanceof Blob?e:new Blob([e]):e instanceof ArrayBuffer?e:e.buffer,St=String.fromCharCode(30);function Ct(){return new TransformStream({transform(e,t){!function(e,t){mt&&e.data instanceof Blob?e.data.arrayBuffer().then(xt).then(t):ht&&(e.data instanceof ArrayBuffer||ft(e.data))?t(xt(e.data)):pt(e,!1,e=>{yt||(yt=new TextEncoder),t(yt.encode(e))})}(e,n=>{const r=n.length;let a;if(r<126)a=new Uint8Array(1),new DataView(a.buffer).setUint8(0,r);else if(r<65536){a=new Uint8Array(3);const e=new DataView(a.buffer);e.setUint8(0,126),e.setUint16(1,r)}else{a=new Uint8Array(9);const e=new DataView(a.buffer);e.setUint8(0,127),e.setBigUint64(1,BigInt(r))}e.data&&"string"!=typeof e.data&&(a[0]|=128),t.enqueue(a),t.enqueue(n)})}})}let Lt;function Tt(e){return e.reduce((e,t)=>e+t.length,0)}function Et(e,t){if(e[0].length===t)return e.shift();const n=new Uint8Array(t);let r=0;for(let a=0;a<t;a++)n[a]=e[0][r++],r===e[0].length&&(e.shift(),r=0);return e.length&&r<e[0].length&&(e[0]=e[0].slice(r)),n}function _t(e){if(e)return function(e){for(var t in _t.prototype)e[t]=_t.prototype[t];return e}(e)}_t.prototype.on=_t.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},_t.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},_t.prototype.off=_t.prototype.removeListener=_t.prototype.removeAllListeners=_t.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var a=0;a<r.length;a++)if((n=r[a])===t||n.fn===t){r.splice(a,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},_t.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var a=(n=n.slice(0)).length;r<a;++r)n[r].apply(this,t)}return this},_t.prototype.emitReserved=_t.prototype.emit,_t.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},_t.prototype.hasListeners=function(e){return!!this.listeners(e).length};const Pt="function"==typeof Promise&&"function"==typeof Promise.resolve?e=>Promise.resolve().then(e):(e,t)=>t(e,0),zt="undefined"!=typeof self?self:"undefined"!=typeof window?window:Function("return this")();function At(e,...t){return t.reduce((t,n)=>(e.hasOwnProperty(n)&&(t[n]=e[n]),t),{})}const Ot=zt.setTimeout,Mt=zt.clearTimeout;function Rt(e,t){t.useNativeTimers?(e.setTimeoutFn=Ot.bind(zt),e.clearTimeoutFn=Mt.bind(zt)):(e.setTimeoutFn=zt.setTimeout.bind(zt),e.clearTimeoutFn=zt.clearTimeout.bind(zt))}function Dt(e){return"string"==typeof e?function(e){let t=0,n=0;for(let r=0,a=e.length;r<a;r++)t=e.charCodeAt(r),t<128?n+=1:t<2048?n+=2:t<55296||t>=57344?n+=3:(r++,n+=4);return n}(e):Math.ceil(1.33*(e.byteLength||e.size))}function Ft(){return Date.now().toString(36).substring(3)+Math.random().toString(36).substring(2,5)}class Bt extends Error{constructor(e,t,n){super(e),this.description=t,this.context=n,this.type="TransportError"}}class It extends _t{constructor(e){super(),this.writable=!1,Rt(this,e),this.opts=e,this.query=e.query,this.socket=e.socket,this.supportsBinary=!e.forceBase64}onError(e,t,n){return super.emitReserved("error",new Bt(e,t,n)),this}open(){return this.readyState="opening",this.doOpen(),this}close(){return"opening"!==this.readyState&&"open"!==this.readyState||(this.doClose(),this.onClose()),this}send(e){"open"===this.readyState&&this.write(e)}onOpen(){this.readyState="open",this.writable=!0,super.emitReserved("open")}onData(e){const t=jt(e,this.socket.binaryType);this.onPacket(t)}onPacket(e){super.emitReserved("packet",e)}onClose(e){this.readyState="closed",super.emitReserved("close",e)}pause(e){}createUri(e,t={}){return e+"://"+this._hostname()+this._port()+this.opts.path+this._query(t)}_hostname(){const e=this.opts.hostname;return-1===e.indexOf(":")?e:"["+e+"]"}_port(){return this.opts.port&&(this.opts.secure&&Number(443!==this.opts.port)||!this.opts.secure&&80!==Number(this.opts.port))?":"+this.opts.port:""}_query(e){const t=function(e){let t="";for(let n in e)e.hasOwnProperty(n)&&(t.length&&(t+="&"),t+=encodeURIComponent(n)+"="+encodeURIComponent(e[n]));return t}(e);return t.length?"?"+t:""}}class Ut extends It{constructor(){super(...arguments),this._polling=!1}get name(){return"polling"}doOpen(){this._poll()}pause(e){this.readyState="pausing";const t=()=>{this.readyState="paused",e()};if(this._polling||!this.writable){let e=0;this._polling&&(e++,this.once("pollComplete",function(){--e||t()})),this.writable||(e++,this.once("drain",function(){--e||t()}))}else t()}_poll(){this._polling=!0,this.doPoll(),this.emitReserved("poll")}onData(e){((e,t)=>{const n=e.split(St),r=[];for(let a=0;a<n.length;a++){const e=jt(n[a],t);if(r.push(e),"error"===e.type)break}return r})(e,this.socket.binaryType).forEach(e=>{if("opening"===this.readyState&&"open"===e.type&&this.onOpen(),"close"===e.type)return this.onClose({description:"transport closed by the server"}),!1;this.onPacket(e)}),"closed"!==this.readyState&&(this._polling=!1,this.emitReserved("pollComplete"),"open"===this.readyState&&this._poll())}doClose(){const e=()=>{this.write([{type:"close"}])};"open"===this.readyState?e():this.once("open",e)}write(e){this.writable=!1,((e,t)=>{const n=e.length,r=new Array(n);let a=0;e.forEach((e,s)=>{pt(e,!1,e=>{r[s]=e,++a===n&&t(r.join(St))})})})(e,e=>{this.doWrite(e,()=>{this.writable=!0,this.emitReserved("drain")})})}uri(){const e=this.opts.secure?"https":"http",t=this.query||{};return!1!==this.opts.timestampRequests&&(t[this.opts.timestampParam]=Ft()),this.supportsBinary||t.sid||(t.b64=1),this.createUri(e,t)}}let Wt=!1;try{Wt="undefined"!=typeof XMLHttpRequest&&"withCredentials"in new XMLHttpRequest}catch(Xn){}const qt=Wt;function Ht(){}class Vt extends Ut{constructor(e){if(super(e),"undefined"!=typeof location){const t="https:"===location.protocol;let n=location.port;n||(n=t?"443":"80"),this.xd="undefined"!=typeof location&&e.hostname!==location.hostname||n!==e.port}}doWrite(e,t){const n=this.request({method:"POST",data:e});n.on("success",t),n.on("error",(e,t)=>{this.onError("xhr post error",e,t)})}doPoll(){const e=this.request();e.on("data",this.onData.bind(this)),e.on("error",(e,t)=>{this.onError("xhr poll error",e,t)}),this.pollXhr=e}}class $t extends _t{constructor(e,t,n){super(),this.createRequest=e,Rt(this,n),this._opts=n,this._method=n.method||"GET",this._uri=t,this._data=void 0!==n.data?n.data:null,this._create()}_create(){var e;const t=At(this._opts,"agent","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","autoUnref");t.xdomain=!!this._opts.xd;const n=this._xhr=this.createRequest(t);try{n.open(this._method,this._uri,!0);try{if(this._opts.extraHeaders){n.setDisableHeaderCheck&&n.setDisableHeaderCheck(!0);for(let e in this._opts.extraHeaders)this._opts.extraHeaders.hasOwnProperty(e)&&n.setRequestHeader(e,this._opts.extraHeaders[e])}}catch(r){}if("POST"===this._method)try{n.setRequestHeader("Content-type","text/plain;charset=UTF-8")}catch(r){}try{n.setRequestHeader("Accept","*/*")}catch(r){}null===(e=this._opts.cookieJar)||void 0===e||e.addCookies(n),"withCredentials"in n&&(n.withCredentials=this._opts.withCredentials),this._opts.requestTimeout&&(n.timeout=this._opts.requestTimeout),n.onreadystatechange=()=>{var e;3===n.readyState&&(null===(e=this._opts.cookieJar)||void 0===e||e.parseCookies(n.getResponseHeader("set-cookie"))),4===n.readyState&&(200===n.status||1223===n.status?this._onLoad():this.setTimeoutFn(()=>{this._onError("number"==typeof n.status?n.status:0)},0))},n.send(this._data)}catch(r){return void this.setTimeoutFn(()=>{this._onError(r)},0)}"undefined"!=typeof document&&(this._index=$t.requestsCount++,$t.requests[this._index]=this)}_onError(e){this.emitReserved("error",e,this._xhr),this._cleanup(!0)}_cleanup(e){if(void 0!==this._xhr&&null!==this._xhr){if(this._xhr.onreadystatechange=Ht,e)try{this._xhr.abort()}catch(t){}"undefined"!=typeof document&&delete $t.requests[this._index],this._xhr=null}}_onLoad(){const e=this._xhr.responseText;null!==e&&(this.emitReserved("data",e),this.emitReserved("success"),this._cleanup())}abort(){this._cleanup()}}if($t.requestsCount=0,$t.requests={},"undefined"!=typeof document)if("function"==typeof attachEvent)attachEvent("onunload",Qt);else if("function"==typeof addEventListener){addEventListener("onpagehide"in zt?"pagehide":"unload",Qt,!1)}function Qt(){for(let e in $t.requests)$t.requests.hasOwnProperty(e)&&$t.requests[e].abort()}const Kt=function(){const e=Yt({xdomain:!1});return e&&null!==e.responseType}();function Yt(e){const t=e.xdomain;try{if("undefined"!=typeof XMLHttpRequest&&(!t||qt))return new XMLHttpRequest}catch(n){}if(!t)try{return new(zt[["Active"].concat("Object").join("X")])("Microsoft.XMLHTTP")}catch(n){}}const Xt="undefined"!=typeof navigator&&"string"==typeof navigator.product&&"reactnative"===navigator.product.toLowerCase();class Zt extends It{get name(){return"websocket"}doOpen(){const e=this.uri(),t=this.opts.protocols,n=Xt?{}:At(this.opts,"agent","perMessageDeflate","pfx","key","passphrase","cert","ca","ciphers","rejectUnauthorized","localAddress","protocolVersion","origin","maxPayload","family","checkServerIdentity");this.opts.extraHeaders&&(n.headers=this.opts.extraHeaders);try{this.ws=this.createSocket(e,t,n)}catch(Xn){return this.emitReserved("error",Xn)}this.ws.binaryType=this.socket.binaryType,this.addEventListeners()}addEventListeners(){this.ws.onopen=()=>{this.opts.autoUnref&&this.ws._socket.unref(),this.onOpen()},this.ws.onclose=e=>this.onClose({description:"websocket connection closed",context:e}),this.ws.onmessage=e=>this.onData(e.data),this.ws.onerror=e=>this.onError("websocket error",e)}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;pt(n,this.supportsBinary,e=>{try{this.doWrite(n,e)}catch(t){}r&&Pt(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){void 0!==this.ws&&(this.ws.onerror=()=>{},this.ws.close(),this.ws=null)}uri(){const e=this.opts.secure?"wss":"ws",t=this.query||{};return this.opts.timestampRequests&&(t[this.opts.timestampParam]=Ft()),this.supportsBinary||(t.b64=1),this.createUri(e,t)}}const Gt=zt.WebSocket||zt.MozWebSocket;const Jt={websocket:class extends Zt{createSocket(e,t,n){return Xt?new Gt(e,t,n):t?new Gt(e,t):new Gt(e)}doWrite(e,t){this.ws.send(t)}},webtransport:class extends It{get name(){return"webtransport"}doOpen(){try{this._transport=new WebTransport(this.createUri("https"),this.opts.transportOptions[this.name])}catch(Xn){return this.emitReserved("error",Xn)}this._transport.closed.then(()=>{this.onClose()}).catch(e=>{this.onError("webtransport error",e)}),this._transport.ready.then(()=>{this._transport.createBidirectionalStream().then(e=>{const t=function(e,t){Lt||(Lt=new TextDecoder);const n=[];let r=0,a=-1,s=!1;return new TransformStream({transform(l,i){for(n.push(l);;){if(0===r){if(Tt(n)<1)break;const e=Et(n,1);s=!(128&~e[0]),a=127&e[0],r=a<126?3:126===a?1:2}else if(1===r){if(Tt(n)<2)break;const e=Et(n,2);a=new DataView(e.buffer,e.byteOffset,e.length).getUint16(0),r=3}else if(2===r){if(Tt(n)<8)break;const e=Et(n,8),t=new DataView(e.buffer,e.byteOffset,e.length),s=t.getUint32(0);if(s>Math.pow(2,21)-1){i.enqueue(dt);break}a=s*Math.pow(2,32)+t.getUint32(4),r=3}else{if(Tt(n)<a)break;const e=Et(n,a);i.enqueue(jt(s?e:Lt.decode(e),t)),r=0}if(0===a||a>e){i.enqueue(dt);break}}}})}(Number.MAX_SAFE_INTEGER,this.socket.binaryType),n=e.readable.pipeThrough(t).getReader(),r=Ct();r.readable.pipeTo(e.writable),this._writer=r.writable.getWriter();const a=()=>{n.read().then(({done:e,value:t})=>{e||(this.onPacket(t),a())}).catch(e=>{})};a();const s={type:"open"};this.query.sid&&(s.data=`{"sid":"${this.query.sid}"}`),this._writer.write(s).then(()=>this.onOpen())})})}write(e){this.writable=!1;for(let t=0;t<e.length;t++){const n=e[t],r=t===e.length-1;this._writer.write(n).then(()=>{r&&Pt(()=>{this.writable=!0,this.emitReserved("drain")},this.setTimeoutFn)})}}doClose(){var e;null===(e=this._transport)||void 0===e||e.close()}},polling:class extends Vt{constructor(e){super(e);const t=e&&e.forceBase64;this.supportsBinary=Kt&&!t}request(e={}){return Object.assign(e,{xd:this.xd},this.opts),new $t(Yt,this.uri(),e)}}},en=/^(?:(?![^:@\/?#]+:[^:@\/]*@)(http|https|ws|wss):\/\/)?((?:(([^:@\/?#]*)(?::([^:@\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/,tn=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"];function nn(e){if(e.length>8e3)throw"URI too long";const t=e,n=e.indexOf("["),r=e.indexOf("]");-1!=n&&-1!=r&&(e=e.substring(0,n)+e.substring(n,r).replace(/:/g,";")+e.substring(r,e.length));let a=en.exec(e||""),s={},l=14;for(;l--;)s[tn[l]]=a[l]||"";return-1!=n&&-1!=r&&(s.source=t,s.host=s.host.substring(1,s.host.length-1).replace(/;/g,":"),s.authority=s.authority.replace("[","").replace("]","").replace(/;/g,":"),s.ipv6uri=!0),s.pathNames=function(e,t){const n=/\/{2,9}/g,r=t.replace(n,"/").split("/");"/"!=t.slice(0,1)&&0!==t.length||r.splice(0,1);"/"==t.slice(-1)&&r.splice(r.length-1,1);return r}(0,s.path),s.queryKey=function(e,t){const n={};return t.replace(/(?:^|&)([^&=]*)=?([^&]*)/g,function(e,t,r){t&&(n[t]=r)}),n}(0,s.query),s}const rn="function"==typeof addEventListener&&"function"==typeof removeEventListener,an=[];rn&&addEventListener("offline",()=>{an.forEach(e=>e())},!1);class sn extends _t{constructor(e,t){if(super(),this.binaryType="arraybuffer",this.writeBuffer=[],this._prevBufferLen=0,this._pingInterval=-1,this._pingTimeout=-1,this._maxPayload=-1,this._pingTimeoutTime=1/0,e&&"object"==typeof e&&(t=e,e=null),e){const n=nn(e);t.hostname=n.host,t.secure="https"===n.protocol||"wss"===n.protocol,t.port=n.port,n.query&&(t.query=n.query)}else t.host&&(t.hostname=nn(t.host).host);Rt(this,t),this.secure=null!=t.secure?t.secure:"undefined"!=typeof location&&"https:"===location.protocol,t.hostname&&!t.port&&(t.port=this.secure?"443":"80"),this.hostname=t.hostname||("undefined"!=typeof location?location.hostname:"localhost"),this.port=t.port||("undefined"!=typeof location&&location.port?location.port:this.secure?"443":"80"),this.transports=[],this._transportsByName={},t.transports.forEach(e=>{const t=e.prototype.name;this.transports.push(t),this._transportsByName[t]=e}),this.opts=Object.assign({path:"/engine.io",agent:!1,withCredentials:!1,upgrade:!0,timestampParam:"t",rememberUpgrade:!1,addTrailingSlash:!0,rejectUnauthorized:!0,perMessageDeflate:{threshold:1024},transportOptions:{},closeOnBeforeunload:!1},t),this.opts.path=this.opts.path.replace(/\/$/,"")+(this.opts.addTrailingSlash?"/":""),"string"==typeof this.opts.query&&(this.opts.query=function(e){let t={},n=e.split("&");for(let r=0,a=n.length;r<a;r++){let e=n[r].split("=");t[decodeURIComponent(e[0])]=decodeURIComponent(e[1])}return t}(this.opts.query)),rn&&(this.opts.closeOnBeforeunload&&(this._beforeunloadEventListener=()=>{this.transport&&(this.transport.removeAllListeners(),this.transport.close())},addEventListener("beforeunload",this._beforeunloadEventListener,!1)),"localhost"!==this.hostname&&(this._offlineEventListener=()=>{this._onClose("transport close",{description:"network connection lost"})},an.push(this._offlineEventListener))),this.opts.withCredentials&&(this._cookieJar=void 0),this._open()}createTransport(e){const t=Object.assign({},this.opts.query);t.EIO=4,t.transport=e,this.id&&(t.sid=this.id);const n=Object.assign({},this.opts,{query:t,socket:this,hostname:this.hostname,secure:this.secure,port:this.port},this.opts.transportOptions[e]);return new this._transportsByName[e](n)}_open(){if(0===this.transports.length)return void this.setTimeoutFn(()=>{this.emitReserved("error","No transports available")},0);const e=this.opts.rememberUpgrade&&sn.priorWebsocketSuccess&&-1!==this.transports.indexOf("websocket")?"websocket":this.transports[0];this.readyState="opening";const t=this.createTransport(e);t.open(),this.setTransport(t)}setTransport(e){this.transport&&this.transport.removeAllListeners(),this.transport=e,e.on("drain",this._onDrain.bind(this)).on("packet",this._onPacket.bind(this)).on("error",this._onError.bind(this)).on("close",e=>this._onClose("transport close",e))}onOpen(){this.readyState="open",sn.priorWebsocketSuccess="websocket"===this.transport.name,this.emitReserved("open"),this.flush()}_onPacket(e){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState)switch(this.emitReserved("packet",e),this.emitReserved("heartbeat"),e.type){case"open":this.onHandshake(JSON.parse(e.data));break;case"ping":this._sendPacket("pong"),this.emitReserved("ping"),this.emitReserved("pong"),this._resetPingTimeout();break;case"error":const t=new Error("server error");t.code=e.data,this._onError(t);break;case"message":this.emitReserved("data",e.data),this.emitReserved("message",e.data)}}onHandshake(e){this.emitReserved("handshake",e),this.id=e.sid,this.transport.query.sid=e.sid,this._pingInterval=e.pingInterval,this._pingTimeout=e.pingTimeout,this._maxPayload=e.maxPayload,this.onOpen(),"closed"!==this.readyState&&this._resetPingTimeout()}_resetPingTimeout(){this.clearTimeoutFn(this._pingTimeoutTimer);const e=this._pingInterval+this._pingTimeout;this._pingTimeoutTime=Date.now()+e,this._pingTimeoutTimer=this.setTimeoutFn(()=>{this._onClose("ping timeout")},e),this.opts.autoUnref&&this._pingTimeoutTimer.unref()}_onDrain(){this.writeBuffer.splice(0,this._prevBufferLen),this._prevBufferLen=0,0===this.writeBuffer.length?this.emitReserved("drain"):this.flush()}flush(){if("closed"!==this.readyState&&this.transport.writable&&!this.upgrading&&this.writeBuffer.length){const e=this._getWritablePackets();this.transport.send(e),this._prevBufferLen=e.length,this.emitReserved("flush")}}_getWritablePackets(){if(!(this._maxPayload&&"polling"===this.transport.name&&this.writeBuffer.length>1))return this.writeBuffer;let e=1;for(let t=0;t<this.writeBuffer.length;t++){const n=this.writeBuffer[t].data;if(n&&(e+=Dt(n)),t>0&&e>this._maxPayload)return this.writeBuffer.slice(0,t);e+=2}return this.writeBuffer}_hasPingExpired(){if(!this._pingTimeoutTime)return!0;const e=Date.now()>this._pingTimeoutTime;return e&&(this._pingTimeoutTime=0,Pt(()=>{this._onClose("ping timeout")},this.setTimeoutFn)),e}write(e,t,n){return this._sendPacket("message",e,t,n),this}send(e,t,n){return this._sendPacket("message",e,t,n),this}_sendPacket(e,t,n,r){if("function"==typeof t&&(r=t,t=void 0),"function"==typeof n&&(r=n,n=null),"closing"===this.readyState||"closed"===this.readyState)return;(n=n||{}).compress=!1!==n.compress;const a={type:e,data:t,options:n};this.emitReserved("packetCreate",a),this.writeBuffer.push(a),r&&this.once("flush",r),this.flush()}close(){const e=()=>{this._onClose("forced close"),this.transport.close()},t=()=>{this.off("upgrade",t),this.off("upgradeError",t),e()},n=()=>{this.once("upgrade",t),this.once("upgradeError",t)};return"opening"!==this.readyState&&"open"!==this.readyState||(this.readyState="closing",this.writeBuffer.length?this.once("drain",()=>{this.upgrading?n():e()}):this.upgrading?n():e()),this}_onError(e){if(sn.priorWebsocketSuccess=!1,this.opts.tryAllTransports&&this.transports.length>1&&"opening"===this.readyState)return this.transports.shift(),this._open();this.emitReserved("error",e),this._onClose("transport error",e)}_onClose(e,t){if("opening"===this.readyState||"open"===this.readyState||"closing"===this.readyState){if(this.clearTimeoutFn(this._pingTimeoutTimer),this.transport.removeAllListeners("close"),this.transport.close(),this.transport.removeAllListeners(),rn&&(this._beforeunloadEventListener&&removeEventListener("beforeunload",this._beforeunloadEventListener,!1),this._offlineEventListener)){const e=an.indexOf(this._offlineEventListener);-1!==e&&an.splice(e,1)}this.readyState="closed",this.id=null,this.emitReserved("close",e,t),this.writeBuffer=[],this._prevBufferLen=0}}}sn.protocol=4;class ln extends sn{constructor(){super(...arguments),this._upgrades=[]}onOpen(){if(super.onOpen(),"open"===this.readyState&&this.opts.upgrade)for(let e=0;e<this._upgrades.length;e++)this._probe(this._upgrades[e])}_probe(e){let t=this.createTransport(e),n=!1;sn.priorWebsocketSuccess=!1;const r=()=>{n||(t.send([{type:"ping",data:"probe"}]),t.once("packet",e=>{if(!n)if("pong"===e.type&&"probe"===e.data){if(this.upgrading=!0,this.emitReserved("upgrading",t),!t)return;sn.priorWebsocketSuccess="websocket"===t.name,this.transport.pause(()=>{n||"closed"!==this.readyState&&(c(),this.setTransport(t),t.send([{type:"upgrade"}]),this.emitReserved("upgrade",t),t=null,this.upgrading=!1,this.flush())})}else{const e=new Error("probe error");e.transport=t.name,this.emitReserved("upgradeError",e)}}))};function a(){n||(n=!0,c(),t.close(),t=null)}const s=e=>{const n=new Error("probe error: "+e);n.transport=t.name,a(),this.emitReserved("upgradeError",n)};function l(){s("transport closed")}function i(){s("socket closed")}function o(e){t&&e.name!==t.name&&a()}const c=()=>{t.removeListener("open",r),t.removeListener("error",s),t.removeListener("close",l),this.off("close",i),this.off("upgrading",o)};t.once("open",r),t.once("error",s),t.once("close",l),this.once("close",i),this.once("upgrading",o),-1!==this._upgrades.indexOf("webtransport")&&"webtransport"!==e?this.setTimeoutFn(()=>{n||t.open()},200):t.open()}onHandshake(e){this._upgrades=this._filterUpgrades(e.upgrades),super.onHandshake(e)}_filterUpgrades(e){const t=[];for(let n=0;n<e.length;n++)~this.transports.indexOf(e[n])&&t.push(e[n]);return t}}let on=class extends ln{constructor(e,t={}){const n="object"==typeof e?e:t;(!n.transports||n.transports&&"string"==typeof n.transports[0])&&(n.transports=(n.transports||["polling","websocket","webtransport"]).map(e=>Jt[e]).filter(e=>!!e)),super(e,n)}};const cn="function"==typeof ArrayBuffer,un=Object.prototype.toString,dn="function"==typeof Blob||"undefined"!=typeof Blob&&"[object BlobConstructor]"===un.call(Blob),mn="function"==typeof File||"undefined"!=typeof File&&"[object FileConstructor]"===un.call(File);function hn(e){return cn&&(e instanceof ArrayBuffer||(e=>"function"==typeof ArrayBuffer.isView?ArrayBuffer.isView(e):e.buffer instanceof ArrayBuffer)(e))||dn&&e instanceof Blob||mn&&e instanceof File}function fn(e,t){if(!e||"object"!=typeof e)return!1;if(Array.isArray(e)){for(let t=0,n=e.length;t<n;t++)if(fn(e[t]))return!0;return!1}if(hn(e))return!0;if(e.toJSON&&"function"==typeof e.toJSON&&1===arguments.length)return fn(e.toJSON(),!0);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&fn(e[n]))return!0;return!1}function pn(e){const t=[],n=e.data,r=e;return r.data=gn(n,t),r.attachments=t.length,{packet:r,buffers:t}}function gn(e,t){if(!e)return e;if(hn(e)){const n={_placeholder:!0,num:t.length};return t.push(e),n}if(Array.isArray(e)){const n=new Array(e.length);for(let r=0;r<e.length;r++)n[r]=gn(e[r],t);return n}if("object"==typeof e&&!(e instanceof Date)){const n={};for(const r in e)Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=gn(e[r],t));return n}return e}function xn(e,t){return e.data=yn(e.data,t),delete e.attachments,e}function yn(e,t){if(!e)return e;if(e&&!0===e._placeholder){if("number"==typeof e.num&&e.num>=0&&e.num<t.length)return t[e.num];throw new Error("illegal attachments")}if(Array.isArray(e))for(let n=0;n<e.length;n++)e[n]=yn(e[n],t);else if("object"==typeof e)for(const n in e)Object.prototype.hasOwnProperty.call(e,n)&&(e[n]=yn(e[n],t));return e}const bn=["connect","connect_error","disconnect","disconnecting","newListener","removeListener"];var vn;!function(e){e[e.CONNECT=0]="CONNECT",e[e.DISCONNECT=1]="DISCONNECT",e[e.EVENT=2]="EVENT",e[e.ACK=3]="ACK",e[e.CONNECT_ERROR=4]="CONNECT_ERROR",e[e.BINARY_EVENT=5]="BINARY_EVENT",e[e.BINARY_ACK=6]="BINARY_ACK"}(vn||(vn={}));function wn(e){return"[object Object]"===Object.prototype.toString.call(e)}class jn extends _t{constructor(e){super(),this.reviver=e}add(e){let t;if("string"==typeof e){if(this.reconstructor)throw new Error("got plaintext data when reconstructing a packet");t=this.decodeString(e);const n=t.type===vn.BINARY_EVENT;n||t.type===vn.BINARY_ACK?(t.type=n?vn.EVENT:vn.ACK,this.reconstructor=new kn(t),0===t.attachments&&super.emitReserved("decoded",t)):super.emitReserved("decoded",t)}else{if(!hn(e)&&!e.base64)throw new Error("Unknown type: "+e);if(!this.reconstructor)throw new Error("got binary data when not reconstructing a packet");t=this.reconstructor.takeBinaryData(e),t&&(this.reconstructor=null,super.emitReserved("decoded",t))}}decodeString(e){let t=0;const n={type:Number(e.charAt(0))};if(void 0===vn[n.type])throw new Error("unknown packet type "+n.type);if(n.type===vn.BINARY_EVENT||n.type===vn.BINARY_ACK){const r=t+1;for(;"-"!==e.charAt(++t)&&t!=e.length;);const a=e.substring(r,t);if(a!=Number(a)||"-"!==e.charAt(t))throw new Error("Illegal attachments");n.attachments=Number(a)}if("/"===e.charAt(t+1)){const r=t+1;for(;++t;){if(","===e.charAt(t))break;if(t===e.length)break}n.nsp=e.substring(r,t)}else n.nsp="/";const r=e.charAt(t+1);if(""!==r&&Number(r)==r){const r=t+1;for(;++t;){const n=e.charAt(t);if(null==n||Number(n)!=n){--t;break}if(t===e.length)break}n.id=Number(e.substring(r,t+1))}if(e.charAt(++t)){const r=this.tryParse(e.substr(t));if(!jn.isPayloadValid(n.type,r))throw new Error("invalid payload");n.data=r}return n}tryParse(e){try{return JSON.parse(e,this.reviver)}catch(t){return!1}}static isPayloadValid(e,t){switch(e){case vn.CONNECT:return wn(t);case vn.DISCONNECT:return void 0===t;case vn.CONNECT_ERROR:return"string"==typeof t||wn(t);case vn.EVENT:case vn.BINARY_EVENT:return Array.isArray(t)&&("number"==typeof t[0]||"string"==typeof t[0]&&-1===bn.indexOf(t[0]));case vn.ACK:case vn.BINARY_ACK:return Array.isArray(t)}}destroy(){this.reconstructor&&(this.reconstructor.finishedReconstruction(),this.reconstructor=null)}}class kn{constructor(e){this.packet=e,this.buffers=[],this.reconPack=e}takeBinaryData(e){if(this.buffers.push(e),this.buffers.length===this.reconPack.attachments){const e=xn(this.reconPack,this.buffers);return this.finishedReconstruction(),e}return null}finishedReconstruction(){this.reconPack=null,this.buffers=[]}}const Nn=Object.freeze(Object.defineProperty({__proto__:null,Decoder:jn,Encoder:class{constructor(e){this.replacer=e}encode(e){return e.type!==vn.EVENT&&e.type!==vn.ACK||!fn(e)?[this.encodeAsString(e)]:this.encodeAsBinary({type:e.type===vn.EVENT?vn.BINARY_EVENT:vn.BINARY_ACK,nsp:e.nsp,data:e.data,id:e.id})}encodeAsString(e){let t=""+e.type;return e.type!==vn.BINARY_EVENT&&e.type!==vn.BINARY_ACK||(t+=e.attachments+"-"),e.nsp&&"/"!==e.nsp&&(t+=e.nsp+","),null!=e.id&&(t+=e.id),null!=e.data&&(t+=JSON.stringify(e.data,this.replacer)),t}encodeAsBinary(e){const t=pn(e),n=this.encodeAsString(t.packet),r=t.buffers;return r.unshift(n),r}},get PacketType(){return vn},protocol:5},Symbol.toStringTag,{value:"Module"}));function Sn(e,t,n){return e.on(t,n),function(){e.off(t,n)}}const Cn=Object.freeze({connect:1,connect_error:1,disconnect:1,disconnecting:1,newListener:1,removeListener:1});class Ln extends _t{constructor(e,t,n){super(),this.connected=!1,this.recovered=!1,this.receiveBuffer=[],this.sendBuffer=[],this._queue=[],this._queueSeq=0,this.ids=0,this.acks={},this.flags={},this.io=e,this.nsp=t,n&&n.auth&&(this.auth=n.auth),this._opts=Object.assign({},n),this.io._autoConnect&&this.open()}get disconnected(){return!this.connected}subEvents(){if(this.subs)return;const e=this.io;this.subs=[Sn(e,"open",this.onopen.bind(this)),Sn(e,"packet",this.onpacket.bind(this)),Sn(e,"error",this.onerror.bind(this)),Sn(e,"close",this.onclose.bind(this))]}get active(){return!!this.subs}connect(){return this.connected||(this.subEvents(),this.io._reconnecting||this.io.open(),"open"===this.io._readyState&&this.onopen()),this}open(){return this.connect()}send(...e){return e.unshift("message"),this.emit.apply(this,e),this}emit(e,...t){var n,r,a;if(Cn.hasOwnProperty(e))throw new Error('"'+e.toString()+'" is a reserved event name');if(t.unshift(e),this._opts.retries&&!this.flags.fromQueue&&!this.flags.volatile)return this._addToQueue(t),this;const s={type:vn.EVENT,data:t,options:{}};if(s.options.compress=!1!==this.flags.compress,"function"==typeof t[t.length-1]){const e=this.ids++,n=t.pop();this._registerAckCallback(e,n),s.id=e}const l=null===(r=null===(n=this.io.engine)||void 0===n?void 0:n.transport)||void 0===r?void 0:r.writable,i=this.connected&&!(null===(a=this.io.engine)||void 0===a?void 0:a._hasPingExpired());return this.flags.volatile&&!l||(i?(this.notifyOutgoingListeners(s),this.packet(s)):this.sendBuffer.push(s)),this.flags={},this}_registerAckCallback(e,t){var n;const r=null!==(n=this.flags.timeout)&&void 0!==n?n:this._opts.ackTimeout;if(void 0===r)return void(this.acks[e]=t);const a=this.io.setTimeoutFn(()=>{delete this.acks[e];for(let t=0;t<this.sendBuffer.length;t++)this.sendBuffer[t].id===e&&this.sendBuffer.splice(t,1);t.call(this,new Error("operation has timed out"))},r),s=(...e)=>{this.io.clearTimeoutFn(a),t.apply(this,e)};s.withError=!0,this.acks[e]=s}emitWithAck(e,...t){return new Promise((n,r)=>{const a=(e,t)=>e?r(e):n(t);a.withError=!0,t.push(a),this.emit(e,...t)})}_addToQueue(e){let t;"function"==typeof e[e.length-1]&&(t=e.pop());const n={id:this._queueSeq++,tryCount:0,pending:!1,args:e,flags:Object.assign({fromQueue:!0},this.flags)};e.push((e,...r)=>{if(n!==this._queue[0])return;return null!==e?n.tryCount>this._opts.retries&&(this._queue.shift(),t&&t(e)):(this._queue.shift(),t&&t(null,...r)),n.pending=!1,this._drainQueue()}),this._queue.push(n),this._drainQueue()}_drainQueue(e=!1){if(!this.connected||0===this._queue.length)return;const t=this._queue[0];t.pending&&!e||(t.pending=!0,t.tryCount++,this.flags=t.flags,this.emit.apply(this,t.args))}packet(e){e.nsp=this.nsp,this.io._packet(e)}onopen(){"function"==typeof this.auth?this.auth(e=>{this._sendConnectPacket(e)}):this._sendConnectPacket(this.auth)}_sendConnectPacket(e){this.packet({type:vn.CONNECT,data:this._pid?Object.assign({pid:this._pid,offset:this._lastOffset},e):e})}onerror(e){this.connected||this.emitReserved("connect_error",e)}onclose(e,t){this.connected=!1,delete this.id,this.emitReserved("disconnect",e,t),this._clearAcks()}_clearAcks(){Object.keys(this.acks).forEach(e=>{if(!this.sendBuffer.some(t=>String(t.id)===e)){const t=this.acks[e];delete this.acks[e],t.withError&&t.call(this,new Error("socket has been disconnected"))}})}onpacket(e){if(e.nsp===this.nsp)switch(e.type){case vn.CONNECT:e.data&&e.data.sid?this.onconnect(e.data.sid,e.data.pid):this.emitReserved("connect_error",new Error("It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)"));break;case vn.EVENT:case vn.BINARY_EVENT:this.onevent(e);break;case vn.ACK:case vn.BINARY_ACK:this.onack(e);break;case vn.DISCONNECT:this.ondisconnect();break;case vn.CONNECT_ERROR:this.destroy();const t=new Error(e.data.message);t.data=e.data.data,this.emitReserved("connect_error",t)}}onevent(e){const t=e.data||[];null!=e.id&&t.push(this.ack(e.id)),this.connected?this.emitEvent(t):this.receiveBuffer.push(Object.freeze(t))}emitEvent(e){if(this._anyListeners&&this._anyListeners.length){const t=this._anyListeners.slice();for(const n of t)n.apply(this,e)}super.emit.apply(this,e),this._pid&&e.length&&"string"==typeof e[e.length-1]&&(this._lastOffset=e[e.length-1])}ack(e){const t=this;let n=!1;return function(...r){n||(n=!0,t.packet({type:vn.ACK,id:e,data:r}))}}onack(e){const t=this.acks[e.id];"function"==typeof t&&(delete this.acks[e.id],t.withError&&e.data.unshift(null),t.apply(this,e.data))}onconnect(e,t){this.id=e,this.recovered=t&&this._pid===t,this._pid=t,this.connected=!0,this.emitBuffered(),this.emitReserved("connect"),this._drainQueue(!0)}emitBuffered(){this.receiveBuffer.forEach(e=>this.emitEvent(e)),this.receiveBuffer=[],this.sendBuffer.forEach(e=>{this.notifyOutgoingListeners(e),this.packet(e)}),this.sendBuffer=[]}ondisconnect(){this.destroy(),this.onclose("io server disconnect")}destroy(){this.subs&&(this.subs.forEach(e=>e()),this.subs=void 0),this.io._destroy(this)}disconnect(){return this.connected&&this.packet({type:vn.DISCONNECT}),this.destroy(),this.connected&&this.onclose("io client disconnect"),this}close(){return this.disconnect()}compress(e){return this.flags.compress=e,this}get volatile(){return this.flags.volatile=!0,this}timeout(e){return this.flags.timeout=e,this}onAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.push(e),this}prependAny(e){return this._anyListeners=this._anyListeners||[],this._anyListeners.unshift(e),this}offAny(e){if(!this._anyListeners)return this;if(e){const t=this._anyListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyListeners=[];return this}listenersAny(){return this._anyListeners||[]}onAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.push(e),this}prependAnyOutgoing(e){return this._anyOutgoingListeners=this._anyOutgoingListeners||[],this._anyOutgoingListeners.unshift(e),this}offAnyOutgoing(e){if(!this._anyOutgoingListeners)return this;if(e){const t=this._anyOutgoingListeners;for(let n=0;n<t.length;n++)if(e===t[n])return t.splice(n,1),this}else this._anyOutgoingListeners=[];return this}listenersAnyOutgoing(){return this._anyOutgoingListeners||[]}notifyOutgoingListeners(e){if(this._anyOutgoingListeners&&this._anyOutgoingListeners.length){const t=this._anyOutgoingListeners.slice();for(const n of t)n.apply(this,e.data)}}}function Tn(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Tn.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),n=Math.floor(t*this.jitter*e);e=1&Math.floor(10*t)?e+n:e-n}return 0|Math.min(e,this.max)},Tn.prototype.reset=function(){this.attempts=0},Tn.prototype.setMin=function(e){this.ms=e},Tn.prototype.setMax=function(e){this.max=e},Tn.prototype.setJitter=function(e){this.jitter=e};class En extends _t{constructor(e,t){var n;super(),this.nsps={},this.subs=[],e&&"object"==typeof e&&(t=e,e=void 0),(t=t||{}).path=t.path||"/socket.io",this.opts=t,Rt(this,t),this.reconnection(!1!==t.reconnection),this.reconnectionAttempts(t.reconnectionAttempts||1/0),this.reconnectionDelay(t.reconnectionDelay||1e3),this.reconnectionDelayMax(t.reconnectionDelayMax||5e3),this.randomizationFactor(null!==(n=t.randomizationFactor)&&void 0!==n?n:.5),this.backoff=new Tn({min:this.reconnectionDelay(),max:this.reconnectionDelayMax(),jitter:this.randomizationFactor()}),this.timeout(null==t.timeout?2e4:t.timeout),this._readyState="closed",this.uri=e;const r=t.parser||Nn;this.encoder=new r.Encoder,this.decoder=new r.Decoder,this._autoConnect=!1!==t.autoConnect,this._autoConnect&&this.open()}reconnection(e){return arguments.length?(this._reconnection=!!e,e||(this.skipReconnect=!0),this):this._reconnection}reconnectionAttempts(e){return void 0===e?this._reconnectionAttempts:(this._reconnectionAttempts=e,this)}reconnectionDelay(e){var t;return void 0===e?this._reconnectionDelay:(this._reconnectionDelay=e,null===(t=this.backoff)||void 0===t||t.setMin(e),this)}randomizationFactor(e){var t;return void 0===e?this._randomizationFactor:(this._randomizationFactor=e,null===(t=this.backoff)||void 0===t||t.setJitter(e),this)}reconnectionDelayMax(e){var t;return void 0===e?this._reconnectionDelayMax:(this._reconnectionDelayMax=e,null===(t=this.backoff)||void 0===t||t.setMax(e),this)}timeout(e){return arguments.length?(this._timeout=e,this):this._timeout}maybeReconnectOnOpen(){!this._reconnecting&&this._reconnection&&0===this.backoff.attempts&&this.reconnect()}open(e){if(~this._readyState.indexOf("open"))return this;this.engine=new on(this.uri,this.opts);const t=this.engine,n=this;this._readyState="opening",this.skipReconnect=!1;const r=Sn(t,"open",function(){n.onopen(),e&&e()}),a=t=>{this.cleanup(),this._readyState="closed",this.emitReserved("error",t),e?e(t):this.maybeReconnectOnOpen()},s=Sn(t,"error",a);if(!1!==this._timeout){const e=this._timeout,n=this.setTimeoutFn(()=>{r(),a(new Error("timeout")),t.close()},e);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}return this.subs.push(r),this.subs.push(s),this}connect(e){return this.open(e)}onopen(){this.cleanup(),this._readyState="open",this.emitReserved("open");const e=this.engine;this.subs.push(Sn(e,"ping",this.onping.bind(this)),Sn(e,"data",this.ondata.bind(this)),Sn(e,"error",this.onerror.bind(this)),Sn(e,"close",this.onclose.bind(this)),Sn(this.decoder,"decoded",this.ondecoded.bind(this)))}onping(){this.emitReserved("ping")}ondata(e){try{this.decoder.add(e)}catch(t){this.onclose("parse error",t)}}ondecoded(e){Pt(()=>{this.emitReserved("packet",e)},this.setTimeoutFn)}onerror(e){this.emitReserved("error",e)}socket(e,t){let n=this.nsps[e];return n?this._autoConnect&&!n.active&&n.connect():(n=new Ln(this,e,t),this.nsps[e]=n),n}_destroy(e){const t=Object.keys(this.nsps);for(const n of t){if(this.nsps[n].active)return}this._close()}_packet(e){const t=this.encoder.encode(e);for(let n=0;n<t.length;n++)this.engine.write(t[n],e.options)}cleanup(){this.subs.forEach(e=>e()),this.subs.length=0,this.decoder.destroy()}_close(){this.skipReconnect=!0,this._reconnecting=!1,this.onclose("forced close")}disconnect(){return this._close()}onclose(e,t){var n;this.cleanup(),null===(n=this.engine)||void 0===n||n.close(),this.backoff.reset(),this._readyState="closed",this.emitReserved("close",e,t),this._reconnection&&!this.skipReconnect&&this.reconnect()}reconnect(){if(this._reconnecting||this.skipReconnect)return this;const e=this;if(this.backoff.attempts>=this._reconnectionAttempts)this.backoff.reset(),this.emitReserved("reconnect_failed"),this._reconnecting=!1;else{const t=this.backoff.duration();this._reconnecting=!0;const n=this.setTimeoutFn(()=>{e.skipReconnect||(this.emitReserved("reconnect_attempt",e.backoff.attempts),e.skipReconnect||e.open(t=>{t?(e._reconnecting=!1,e.reconnect(),this.emitReserved("reconnect_error",t)):e.onreconnect()}))},t);this.opts.autoUnref&&n.unref(),this.subs.push(()=>{this.clearTimeoutFn(n)})}}onreconnect(){const e=this.backoff.attempts;this._reconnecting=!1,this.backoff.reset(),this.emitReserved("reconnect",e)}}const _n={};function Pn(e,t){"object"==typeof e&&(t=e,e=void 0);const n=function(e,t="",n){let r=e;n=n||"undefined"!=typeof location&&location,null==e&&(e=n.protocol+"//"+n.host),"string"==typeof e&&("/"===e.charAt(0)&&(e="/"===e.charAt(1)?n.protocol+e:n.host+e),/^(https?|wss?):\/\//.test(e)||(e=void 0!==n?n.protocol+"//"+e:"https://"+e),r=nn(e)),r.port||(/^(http|ws)$/.test(r.protocol)?r.port="80":/^(http|ws)s$/.test(r.protocol)&&(r.port="443")),r.path=r.path||"/";const a=-1!==r.host.indexOf(":")?"["+r.host+"]":r.host;return r.id=r.protocol+"://"+a+":"+r.port+t,r.href=r.protocol+"://"+a+(n&&n.port===r.port?"":":"+r.port),r}(e,(t=t||{}).path||"/socket.io"),r=n.source,a=n.id,s=n.path,l=_n[a]&&s in _n[a].nsps;let i;return t.forceNew||t["force new connection"]||!1===t.multiplex||l?i=new En(r,t):(_n[a]||(_n[a]=new En(r,t)),i=_n[a]),n.query&&!t.query&&(t.query=n.queryKey),i.socket(n.path,t)}Object.assign(Pn,{Manager:En,Socket:Ln,io:Pn,connect:Pn});const zn=new class{socket=null;reconnectAttempts=0;maxReconnectAttempts=5;connect(){const e=localStorage.getItem("token");if(!e)return void console.warn("No token found, cannot connect to WebSocket");Me.dispatch(ue(!0));const t="http://localhost:6000/api/v1".replace("/api/v1","").replace("http","ws");this.socket=Pn(`${t}/translation`,{auth:{token:e},transports:["websocket"],timeout:1e4}),this.setupEventListeners()}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{console.log("WebSocket connected"),this.reconnectAttempts=0,Me.dispatch(de(!0))}),this.socket.on("disconnect",e=>{console.log("WebSocket disconnected:",e),Me.dispatch(de(!1)),"io server disconnect"===e&&this.reconnect()}),this.socket.on("connect_error",e=>{console.error("WebSocket connection error:",e),Me.dispatch(de(!1)),Me.dispatch(ue(!1)),this.reconnect()}),this.socket.on("error",e=>{console.error("WebSocket error:",e)}),this.socket.on("connected",e=>{console.log("WebSocket authentication successful:",e),Me.dispatch(de(!0)),Me.dispatch(ue(!1))}),this.socket.on("translation:start",e=>{console.log("Translation started:",e),Me.dispatch(se())}),this.socket.on("translation:progress",e=>{console.log("Translation progress:",e),Me.dispatch(le(e.progress))}),this.socket.on("translation:complete",e=>{console.log("Translation completed:",e),Me.dispatch(ie(e.result.translatedText)),Me.dispatch(me(e.result))}),this.socket.on("translation:error",e=>{console.error("Translation error:",e),Me.dispatch(oe(e.message))}),this.socket.on("translation:favorite:updated",e=>{console.log("Favorite updated:",e),Me.dispatch(he(e.translation))}))}reconnect(){if(this.reconnectAttempts>=this.maxReconnectAttempts)return void console.error("Max reconnection attempts reached");this.reconnectAttempts++;const e=Math.min(1e3*Math.pow(2,this.reconnectAttempts),3e4);console.log(`Attempting to reconnect in ${e}ms (attempt ${this.reconnectAttempts})`),setTimeout(()=>{this.socket&&this.socket.connect()},e)}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null),Me.dispatch(de(!1)),Me.dispatch(ue(!1))}startTranslation(e){this.socket?.connected&&this.socket.emit("translation:start",e)}startBatchTranslation(e){this.socket?.connected&&this.socket.emit("translation:batch",{translations:e})}getTranslationHistory(e=1,t=20){this.socket?.connected&&this.socket.emit("translation:history",{page:e,limit:t})}toggleFavorite(e){this.socket?.connected&&this.socket.emit("translation:favorite",{translationId:e})}onTranslationStart(e){this.socket?.on("translation:start",e)}onTranslationProgress(e){this.socket?.on("translation:progress",e)}onTranslationComplete(e){this.socket?.on("translation:complete",e)}onTranslationError(e){this.socket?.on("translation:error",e)}onBatchTranslationStart(e){this.socket?.on("translation:batch:start",e)}onBatchTranslationProgress(e){this.socket?.on("translation:batch:progress",e)}onBatchTranslationComplete(e){this.socket?.on("translation:batch:complete",e)}onTranslationHistory(e){this.socket?.on("translation:history",e)}onFavoriteUpdated(e){this.socket?.on("translation:favorite:updated",e)}removeAllListeners(){this.socket&&this.socket.removeAllListeners()}isConnected(){return this.socket?.connected||!1}getSocket(){return this.socket}},An=()=>{const e=d(),t=Re(),{user:n}=De(e=>e.auth),{history:r,websocket:a}=De(e=>e.translation);s.useEffect(()=>{t(X({page:1,limit:5})),a.connected||a.connecting||zn.connect()},[t,a.connected,a.connecting]);return S.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[S.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"flex justify-between items-center py-6",children:[S.jsxs("div",{className:"flex items-center space-x-4",children:[S.jsx(it,{size:"md"}),S.jsxs("div",{children:[S.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"SynText"}),S.jsx("p",{className:"text-sm text-gray-600",children:"智能翻译平台"})]})]}),S.jsxs("div",{className:"flex items-center space-x-6",children:[S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("div",{className:"w-2 h-2 rounded-full "+(a.connected?"bg-green-500":a.connecting?"bg-yellow-500":"bg-red-500")}),S.jsx("span",{className:"text-sm text-gray-600",children:a.connected?"在线":a.connecting?"连接中":"离线"})]}),S.jsxs("div",{className:"flex items-center space-x-4",children:[S.jsxs("div",{className:"text-right",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:n?.name||n?.email}),S.jsx("p",{className:"text-xs text-gray-500",children:"premium"===n?.role?"高级用户":"普通用户"})]}),S.jsxs("button",{onClick:()=>{zn.disconnect(),t(Q()),e("/login")},className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors",children:[S.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"})}),"退出登录"]})]})]})]})})}),S.jsxs("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[S.jsx("div",{className:"mb-8",children:S.jsx("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:S.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-12",children:S.jsxs("div",{className:"text-center text-white",children:[S.jsx("h2",{className:"text-3xl font-bold mb-4",children:"欢迎使用 SynText"}),S.jsx("p",{className:"text-xl opacity-90 mb-8",children:"AI 驱动的智能翻译平台，让语言不再是障碍"}),S.jsxs("button",{onClick:()=>e("/translate"),className:"inline-flex items-center px-8 py-4 bg-white text-blue-600 font-semibold rounded-lg hover:bg-gray-50 transition-colors shadow-lg",children:[S.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})}),"立即开始翻译"]})]})})})}),S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[S.jsx("div",{className:"lg:col-span-2",children:S.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[S.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"快速操作"}),S.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[S.jsxs("button",{onClick:()=>e("/translate"),className:"flex items-center p-4 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors group",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center group-hover:bg-blue-700 transition-colors",children:S.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})})})}),S.jsxs("div",{className:"ml-4 text-left",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:"文本翻译"}),S.jsx("p",{className:"text-sm text-gray-500",children:"实时智能翻译"})]})]}),S.jsxs("button",{onClick:()=>e("/history"),className:"flex items-center p-4 bg-green-50 hover:bg-green-100 rounded-lg transition-colors group",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center group-hover:bg-green-700 transition-colors",children:S.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})}),S.jsxs("div",{className:"ml-4 text-left",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:"翻译历史"}),S.jsx("p",{className:"text-sm text-gray-500",children:"查看历史记录"})]})]}),S.jsxs("button",{className:"flex items-center p-4 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors group",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"w-10 h-10 bg-purple-600 rounded-lg flex items-center justify-center group-hover:bg-purple-700 transition-colors",children:S.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})})}),S.jsxs("div",{className:"ml-4 text-left",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:"收藏夹"}),S.jsx("p",{className:"text-sm text-gray-500",children:"收藏的翻译"})]})]}),S.jsxs("button",{className:"flex items-center p-4 bg-orange-50 hover:bg-orange-100 rounded-lg transition-colors group",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"w-10 h-10 bg-orange-600 rounded-lg flex items-center justify-center group-hover:bg-orange-700 transition-colors",children:S.jsxs("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})})}),S.jsxs("div",{className:"ml-4 text-left",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:"设置"}),S.jsx("p",{className:"text-sm text-gray-500",children:"个人设置"})]})]})]})]})}),S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[S.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-4",children:"使用统计"}),S.jsxs("div",{className:"space-y-4",children:[S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsx("span",{className:"text-sm text-gray-600",children:"今日翻译"}),S.jsx("span",{className:"text-lg font-semibold text-blue-600",children:"0"})]}),S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsx("span",{className:"text-sm text-gray-600",children:"本月翻译"}),S.jsx("span",{className:"text-lg font-semibold text-green-600",children:"0"})]}),S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsx("span",{className:"text-sm text-gray-600",children:"总计翻译"}),S.jsx("span",{className:"text-lg font-semibold text-purple-600",children:r.total})]})]})]}),S.jsxs("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[S.jsxs("div",{className:"flex items-center justify-between mb-4",children:[S.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"最近翻译"}),S.jsx("button",{onClick:()=>e("/history"),className:"text-sm text-blue-600 hover:text-blue-800 transition-colors",children:"查看全部"})]}),S.jsxs("div",{className:"space-y-3",children:[r.translations.slice(0,3).map(e=>S.jsxs("div",{className:"p-3 bg-gray-50 rounded-lg",children:[S.jsx("p",{className:"text-sm text-gray-900 truncate mb-1",children:e.sourceText}),S.jsxs("p",{className:"text-xs text-gray-500",children:[e.sourceLanguage," → ",e.targetLanguage]})]},e.id)),0===r.translations.length&&S.jsx("p",{className:"text-sm text-gray-500 text-center py-4",children:"暂无翻译记录"})]})]})]})]})]})]})},On=()=>{const e=d(),t=Re(),{currentTranslation:n,languages:r,websocket:a}=De(e=>e.translation),[l,i]=s.useState(!0),[o,c]=s.useState(!1);s.useEffect(()=>(t(Y()),!l||a.connected||a.connecting||zn.connect(),()=>{t(ce())}),[t,l,a.connected,a.connecting]);const u=(e=!1)=>({popular:r.popular.filter(t=>!e||"auto"!==t.code),others:r.all.filter(t=>!(r.popular.some(e=>e.code===t.code)||e&&"auto"===t.code))}),{popular:m,others:h}=u(),{popular:f,others:p}=u(!0);return S.jsxs("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[S.jsx("nav",{className:"bg-white shadow-sm border-b border-gray-200",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"flex justify-between items-center h-16",children:[S.jsxs("div",{className:"flex items-center space-x-4",children:[S.jsxs("button",{onClick:()=>e("/"),className:"inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[S.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"返回首页"]}),S.jsx("div",{className:"h-6 w-px bg-gray-300"}),S.jsx("h1",{className:"text-xl font-semibold text-gray-900",children:"智能翻译"})]}),S.jsxs("div",{className:"flex items-center space-x-4",children:[S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("div",{className:"w-2 h-2 rounded-full "+(a.connected?"bg-green-500":a.connecting?"bg-yellow-500":"bg-red-500")}),S.jsx("span",{className:"text-sm text-gray-600",children:a.connected?"实时连接":a.connecting?"连接中...":"离线模式"})]}),S.jsxs("label",{className:"flex items-center space-x-2",children:[S.jsx("input",{type:"checkbox",checked:l,onChange:e=>i(e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),S.jsx("span",{className:"text-sm text-gray-600",children:"实时翻译"})]})]})]})})}),S.jsx("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"bg-white rounded-xl shadow-lg overflow-hidden",children:[S.jsx("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:S.jsx("div",{className:"flex items-center justify-between",children:S.jsxs("div",{className:"flex items-center space-x-4",children:[S.jsxs("div",{className:"flex flex-col",children:[S.jsx("label",{className:"text-xs font-medium text-gray-500 mb-1",children:"源语言"}),S.jsxs("select",{value:n.sourceLanguage,onChange:e=>t(ne(e.target.value)),className:"text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[S.jsx("optgroup",{label:"热门语言",children:m.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))}),h.length>0&&S.jsx("optgroup",{label:"其他语言",children:h.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))})]})]}),S.jsx("button",{onClick:()=>{"auto"!==n.sourceLanguage&&(t(ne(n.targetLanguage)),t(re(n.sourceLanguage)),t(te(n.translatedText)))},disabled:"auto"===n.sourceLanguage,className:"p-2 text-gray-400 hover:text-gray-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",title:"交换语言",children:S.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"})})}),S.jsxs("div",{className:"flex flex-col",children:[S.jsx("label",{className:"text-xs font-medium text-gray-500 mb-1",children:"目标语言"}),S.jsxs("select",{value:n.targetLanguage,onChange:e=>t(re(e.target.value)),className:"text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[S.jsx("optgroup",{label:"热门语言",children:f.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))}),p.length>0&&S.jsx("optgroup",{label:"其他语言",children:p.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))})]})]})]})})}),S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 divide-y lg:divide-y-0 lg:divide-x divide-gray-200",children:[S.jsxs("div",{className:"p-6",children:[S.jsxs("div",{className:"flex items-center justify-between mb-4",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"原文"}),S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsxs("span",{className:"text-sm text-gray-500",children:[n.sourceText.length," 字符"]}),n.sourceText&&S.jsx("button",{onClick:()=>{t(te(""))},className:"text-sm text-gray-400 hover:text-gray-600 transition-colors",children:"清空"})]})]}),S.jsx("textarea",{value:n.sourceText,onChange:e=>t(te(e.target.value)),placeholder:"请输入要翻译的文本...",className:"w-full h-80 p-4 text-gray-900 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",maxLength:1e4}),S.jsxs("div",{className:"mt-4 flex items-center justify-between",children:[S.jsx("div",{className:"text-xs text-gray-500",children:"最多支持 10,000 字符"}),S.jsx("button",{onClick:async()=>{if(!n.sourceText.trim())return;const e={sourceText:n.sourceText,sourceLanguage:n.sourceLanguage,targetLanguage:n.targetLanguage};l&&a.connected?(c(!0),zn.startTranslation(e)):t(G(e))},disabled:!n.sourceText.trim()||n.isTranslating,className:"inline-flex items-center px-6 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all",children:n.isTranslating?S.jsxs(S.Fragment,{children:[S.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24",children:[S.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),S.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"翻译中..."]}):S.jsxs(S.Fragment,{children:[S.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})}),"开始翻译"]})})]})]}),S.jsxs("div",{className:"p-6",children:[S.jsxs("div",{className:"flex items-center justify-between mb-4",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"译文"}),S.jsx("div",{className:"flex items-center space-x-2",children:n.translatedText&&S.jsxs(S.Fragment,{children:[S.jsx("button",{onClick:()=>navigator.clipboard.writeText(n.translatedText),className:"text-sm text-blue-600 hover:text-blue-800 transition-colors",title:"复制译文",children:"复制"}),S.jsx("button",{className:"text-sm text-blue-600 hover:text-blue-800 transition-colors",title:"收藏翻译",children:"收藏"})]})})]}),(n.isTranslating||o)&&n.progress>0&&S.jsxs("div",{className:"mb-4",children:[S.jsxs("div",{className:"flex items-center justify-between text-sm text-gray-600 mb-2",children:[S.jsx("span",{children:"翻译进度"}),S.jsxs("span",{children:[n.progress,"%"]})]}),S.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:S.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300 ease-out",style:{width:`${n.progress}%`}})})]}),S.jsx("textarea",{value:n.translatedText,readOnly:!0,placeholder:"翻译结果将显示在这里...",className:"w-full h-80 p-4 text-gray-900 border border-gray-300 rounded-lg resize-none bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:border-transparent"}),n.translatedText&&S.jsxs("div",{className:"mt-4 flex items-center justify-between",children:[S.jsxs("div",{className:"text-xs text-gray-500",children:["译文长度: ",n.translatedText.length," 字符"]}),S.jsx("div",{className:"flex items-center space-x-4",children:S.jsx("button",{onClick:()=>t(te(n.translatedText)),className:"text-sm text-blue-600 hover:text-blue-800 transition-colors",children:"继续翻译"})})]})]})]}),n.error&&S.jsx("div",{className:"mx-6 mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:S.jsxs("div",{className:"flex items-start",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("svg",{className:"h-5 w-5 text-red-400",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),S.jsxs("div",{className:"ml-3",children:[S.jsx("h3",{className:"text-sm font-medium text-red-800",children:"翻译失败"}),S.jsx("p",{className:"text-sm text-red-700 mt-1",children:n.error})]}),S.jsx("div",{className:"ml-auto pl-3",children:S.jsx("button",{onClick:()=>t(ce()),className:"text-red-400 hover:text-red-600",children:S.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})})]})})]})})]})},Mn=()=>{const e=d(),t=Re(),{user:n}=De(e=>e.auth),{history:r,favorites:a,languages:l}=De(e=>e.translation),i=Math.ceil(r.total/r.limit),[o,c]=s.useState(new Set),[u,m]=s.useState({page:1,limit:20,search:"",sourceLang:"",targetLang:"",startDate:"",endDate:"",onlyFavorites:!1});s.useEffect(()=>{t(X({page:u.page,limit:u.limit})),u.onlyFavorites&&t(Z())},[t,u.page,u.limit,u.onlyFavorites]);const h=()=>{m(e=>({...e,page:1})),t(X({page:1,limit:u.limit}))},f=e=>{m(t=>({...t,page:e}))},p=(u.onlyFavorites?a:r.translations).filter(e=>!(u.search&&!e.sourceText.toLowerCase().includes(u.search.toLowerCase())&&!e.translatedText.toLowerCase().includes(u.search.toLowerCase()))&&((!u.sourceLang||e.sourceLanguage===u.sourceLang)&&(!u.targetLang||e.targetLanguage===u.targetLang))),g=e=>{const t=[...l.popular,...l.all].find(t=>t.code===e);return t?t.name:e.toUpperCase()},x=e=>{const t=Math.round(100*e);let n="";return n=t>=90?"bg-green-100 text-green-800":t>=70?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800",S.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${n}`,children:[t,"%"]})},y=(e,t=100)=>e.length<=t?e:e.substring(0,t)+"...";return S.jsxs("div",{className:"min-h-screen bg-gray-50",children:[S.jsx("nav",{className:"bg-white shadow-sm",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"flex justify-between h-16",children:[S.jsxs("div",{className:"flex items-center",children:[S.jsx("button",{onClick:()=>e("/"),className:"text-gray-500 hover:text-gray-700 mr-4",children:"← 返回首页"}),S.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"翻译历史"})]}),S.jsx("div",{className:"flex items-center space-x-4",children:S.jsx("span",{className:"text-sm text-gray-700",children:n?.name||n?.email})})]})})}),S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[S.jsxs("div",{className:"bg-white shadow rounded-lg p-4 sm:p-6 mb-6",children:[S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-3 sm:gap-4",children:[S.jsxs("div",{className:"lg:col-span-2",children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"搜索内容"}),S.jsxs("div",{className:"flex",children:[S.jsx("input",{type:"text",className:"flex-1 block w-full px-3 py-2 border border-gray-300 rounded-l-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500",placeholder:"搜索翻译内容...",value:u.search,onChange:e=>m({...u,search:e.target.value}),onKeyPress:e=>"Enter"===e.key&&h()}),S.jsx("button",{onClick:h,className:"px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100",children:S.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"源语言"}),S.jsxs("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:u.sourceLang,onChange:e=>m({...u,sourceLang:e.target.value,page:1}),children:[S.jsx("option",{value:"",children:"全部语言"}),S.jsx("optgroup",{label:"热门语言",children:l.popular.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))}),l.all.length>0&&S.jsx("optgroup",{label:"其他语言",children:l.all.filter(e=>!l.popular.some(t=>t.code===e.code)).map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))})]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"目标语言"}),S.jsxs("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:u.targetLang,onChange:e=>m({...u,targetLang:e.target.value,page:1}),children:[S.jsx("option",{value:"",children:"全部语言"}),S.jsx("optgroup",{label:"热门语言",children:l.popular.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))}),l.all.length>0&&S.jsx("optgroup",{label:"其他语言",children:l.all.filter(e=>!l.popular.some(t=>t.code===e.code)).map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))})]})]}),S.jsx("div",{className:"flex items-end",children:S.jsxs("label",{className:"flex items-center",children:[S.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-primary-600 shadow-sm focus:border-primary-300 focus:ring focus:ring-primary-200 focus:ring-opacity-50",checked:u.onlyFavorites,onChange:e=>m({...u,onlyFavorites:e.target.checked,page:1})}),S.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"仅显示收藏"})]})})]}),S.jsxs("div",{className:"mt-4 flex justify-between items-center",children:[S.jsx("div",{className:"flex items-center space-x-4",children:S.jsx("button",{onClick:()=>{m({page:1,limit:20,search:"",sourceLang:"",targetLang:"",startDate:"",endDate:"",onlyFavorites:!1}),t(X({page:1,limit:20}))},className:"px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50",children:"重置筛选"})}),o.size>0&&S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsxs("span",{className:"text-sm text-gray-700",children:["已选择 ",o.size," 项"]}),S.jsx("button",{onClick:async()=>{if(0!==o.size)try{c(new Set),console.log("删除功能待实现")}catch(e){console.error("删除翻译失败:",e)}},className:"px-3 py-1 text-sm text-red-600 hover:text-red-800",children:"删除选中"})]})]})]}),S.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:r.loading?S.jsx("div",{className:"flex justify-center items-center h-64",children:S.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"})}):0===p.length?S.jsxs("div",{className:"text-center py-12",children:[S.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),S.jsx("h3",{className:"mt-2 text-sm font-medium text-gray-900",children:"暂无翻译记录"}),S.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"开始您的第一次翻译吧！"}),S.jsx("div",{className:"mt-6",children:S.jsx("button",{onClick:()=>e("/translate"),className:"inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700",children:"开始翻译"})})]}):S.jsxs(S.Fragment,{children:[S.jsx("div",{className:"bg-gray-50 px-6 py-3 border-b border-gray-200",children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("label",{className:"flex items-center",children:[S.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",checked:o.size===p.length&&p.length>0,onChange:()=>{o.size===p.length?c(new Set):c(new Set(p.map(e=>e.id)))}}),S.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"全选"})]}),S.jsxs("div",{className:"text-sm text-gray-500",children:["共 ",u.onlyFavorites?a.length:r.total," 条记录"]})]})}),S.jsx("div",{className:"divide-y divide-gray-200",children:p.map(n=>{return S.jsx("div",{className:"p-4 sm:p-6 hover:bg-gray-50",children:S.jsxs("div",{className:"flex items-start space-x-3 sm:space-x-4",children:[S.jsx("input",{type:"checkbox",className:"mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50",checked:o.has(n.id),onChange:()=>(e=>{const t=new Set(o);t.has(e)?t.delete(e):t.add(e),c(t)})(n.id)}),S.jsxs("div",{className:"flex-1 space-y-2 sm:space-y-3",children:[S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4",children:[S.jsxs("div",{children:[S.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[S.jsx("span",{className:"text-xs text-gray-500",children:"原文"}),S.jsx("span",{className:"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded",children:g(n.sourceLanguage)})]}),S.jsx("p",{className:"text-sm text-gray-900",title:n.sourceText,children:y(n.sourceText)})]}),S.jsxs("div",{children:[S.jsxs("div",{className:"flex items-center space-x-2 mb-2",children:[S.jsx("span",{className:"text-xs text-gray-500",children:"译文"}),S.jsx("span",{className:"text-xs bg-blue-100 text-blue-600 px-2 py-1 rounded",children:g(n.targetLanguage)})]}),S.jsx("p",{className:"text-sm text-blue-600",title:n.translatedText,children:y(n.translatedText)})]})]}),S.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2 sm:gap-0",children:[S.jsxs("div",{className:"flex items-center space-x-2 sm:space-x-4 text-xs sm:text-sm text-gray-500",children:[S.jsx("span",{children:(r=n.createdAt,new Date(r).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))}),n.quality&&x(n.quality)]}),S.jsxs("div",{className:"flex items-center space-x-1 sm:space-x-2",children:[S.jsx("button",{onClick:()=>{return e=n.id,void t(J(e));var e},className:"p-1 rounded "+(n.isFavorite?"text-yellow-500 hover:text-yellow-600":"text-gray-400 hover:text-yellow-500"),title:n.isFavorite?"取消收藏":"收藏",children:S.jsx("svg",{className:"h-5 w-5",fill:n.isFavorite?"currentColor":"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})})}),S.jsx("button",{onClick:()=>(t=>{e("/translate",{state:{sourceText:t.sourceText,sourceLanguage:t.sourceLanguage,targetLanguage:t.targetLanguage}})})(n),className:"p-1 text-gray-400 hover:text-blue-600 rounded",title:"重新翻译",children:S.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})})}),S.jsx("button",{onClick:()=>(e=>{navigator.clipboard.writeText(e);const t=document.createElement("div");t.className="fixed top-4 right-4 bg-green-500 text-white px-4 py-2 rounded-md shadow-lg z-50",t.textContent="复制成功",document.body.appendChild(t),setTimeout(()=>{document.body.removeChild(t)},2e3)})(n.translatedText),className:"p-1 text-gray-400 hover:text-blue-600 rounded",title:"复制译文",children:S.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})]})]})]})]})},n.id);var r})}),i>1&&S.jsxs("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[S.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[S.jsx("button",{disabled:1===u.page,onClick:()=>f(u.page-1),className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"上一页"}),S.jsx("button",{disabled:u.page===i,onClick:()=>f(u.page+1),className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"下一页"})]}),S.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[S.jsx("div",{children:S.jsxs("p",{className:"text-sm text-gray-700",children:["显示第 ",S.jsx("span",{className:"font-medium",children:(u.page-1)*u.limit+1})," 到"," ",S.jsx("span",{className:"font-medium",children:Math.min(u.page*u.limit,r.total)})," ","条，共 ",S.jsx("span",{className:"font-medium",children:r.total})," 条记录"]})}),S.jsx("div",{children:S.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[S.jsx("button",{disabled:1===u.page,onClick:()=>f(u.page-1),className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"上一页"}),S.jsx("button",{disabled:u.page===i,onClick:()=>f(u.page+1),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"下一页"})]})})]})]})]})})]})]})},Rn=()=>{const e=d(),t=Re(),{user:n}=De(e=>e.auth),[r,a]=s.useState(null),[l,i]=s.useState(null),[o,c]=s.useState(null),[u,m]=s.useState(!0);s.useEffect(()=>{h()},[]);const h=async()=>{try{m(!0),setTimeout(()=>{a({id:n?.id||"1",email:n?.email||"<EMAIL>",name:n?.name||"张三",role:n?.role||"user",status:"active",translationCount:156,monthlyTranslationCount:45,createdAt:"2025-01-15T10:00:00Z",lastLoginAt:"2025-08-03T14:30:00Z",preferences:{defaultSourceLanguage:"auto",defaultTargetLanguage:"zh",theme:"light"}}),i({totalTranslations:156,totalCharacters:8450,monthlyTranslations:45,monthlyCharacters:2340,favoriteTranslations:12,averageConfidence:.94}),c({planType:"premium",status:"active",currentPeriodStart:"2025-08-01T00:00:00Z",currentPeriodEnd:"2025-09-01T00:00:00Z",usageLimit:-1,usageCount:45,features:["无限翻译","高质量翻译","批量翻译","优先支持"]}),m(!1)},1e3)}catch(e){console.error("获取用户数据失败:",e),m(!1)}},f=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),p=e=>S.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{free:"bg-gray-100 text-gray-800",premium:"bg-yellow-100 text-yellow-800",enterprise:"bg-purple-100 text-purple-800"}[e]}`,children:{free:"免费版",premium:"高级版",enterprise:"企业版"}[e]});return u?S.jsx("div",{className:"min-h-screen bg-gray-50 flex justify-center items-center",children:S.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):S.jsxs("div",{className:"min-h-screen bg-gray-50",children:[S.jsx("nav",{className:"bg-white shadow-sm",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"flex justify-between h-16",children:[S.jsxs("div",{className:"flex items-center",children:[S.jsx("button",{onClick:()=>e("/"),className:"text-gray-500 hover:text-gray-700 mr-4",children:"← 返回首页"}),S.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"个人中心"})]}),S.jsx("div",{className:"flex items-center space-x-4",children:S.jsx("button",{onClick:()=>{t(Q()),e("/login")},className:"text-gray-500 hover:text-gray-700",children:"退出登录"})})]})})}),S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8",children:[S.jsx("div",{className:"lg:col-span-1",children:S.jsxs("div",{className:"bg-white shadow rounded-lg p-4 sm:p-6",children:[S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"mx-auto h-24 w-24 bg-primary-100 rounded-full flex items-center justify-center",children:S.jsx("span",{className:"text-2xl font-bold text-primary-600",children:r?.name?.charAt(0)||r?.email.charAt(0).toUpperCase()})}),S.jsx("h2",{className:"mt-4 text-xl font-bold text-gray-900",children:r?.name||"未设置姓名"}),S.jsx("p",{className:"text-gray-500",children:r?.email}),S.jsx("div",{className:"mt-3",children:o&&p(o.planType)})]}),S.jsx("div",{className:"mt-6 border-t pt-6",children:S.jsxs("dl",{className:"space-y-3",children:[S.jsxs("div",{children:[S.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"注册时间"}),S.jsx("dd",{className:"text-sm text-gray-900",children:r?.createdAt&&f(r.createdAt)})]}),S.jsxs("div",{children:[S.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"最后登录"}),S.jsx("dd",{className:"text-sm text-gray-900",children:r?.lastLoginAt&&f(r.lastLoginAt)})]}),S.jsxs("div",{children:[S.jsx("dt",{className:"text-sm font-medium text-gray-500",children:"账户状态"}),S.jsx("dd",{className:"text-sm text-green-600 font-medium",children:"正常"})]})]})}),S.jsx("div",{className:"mt-6",children:S.jsx("button",{onClick:()=>e("/settings"),className:"w-full bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 transition-colors",children:"编辑个人信息"})})]})}),S.jsxs("div",{className:"lg:col-span-2 space-y-6 lg:space-y-8",children:[S.jsxs("div",{className:"bg-white shadow rounded-lg p-4 sm:p-6",children:[S.jsx("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-4 sm:mb-6",children:"使用统计"}),S.jsxs("div",{className:"grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6",children:[S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"text-2xl font-bold text-primary-600",children:l?.totalTranslations.toLocaleString()}),S.jsx("div",{className:"text-sm text-gray-500",children:"总翻译次数"})]}),S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"text-2xl font-bold text-green-600",children:l?.totalCharacters.toLocaleString()}),S.jsx("div",{className:"text-sm text-gray-500",children:"总翻译字符"})]}),S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"text-2xl font-bold text-yellow-600",children:l?.monthlyTranslations.toLocaleString()}),S.jsx("div",{className:"text-sm text-gray-500",children:"本月翻译"})]}),S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"text-2xl font-bold text-red-600",children:l?.favoriteTranslations.toLocaleString()}),S.jsx("div",{className:"text-sm text-gray-500",children:"收藏翻译"})]})]}),S.jsxs("div",{className:"mt-6",children:[S.jsxs("div",{className:"flex justify-between items-center mb-2",children:[S.jsx("span",{className:"text-sm font-medium text-gray-500",children:"翻译质量"}),S.jsxs("span",{className:"text-sm font-bold text-gray-900",children:[l&&Math.round(100*l.averageConfidence),"%"]})]}),S.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:S.jsx("div",{className:"bg-green-600 h-2 rounded-full",style:{width:`${l&&100*l.averageConfidence}%`}})})]})]}),o&&S.jsxs("div",{className:"bg-white shadow rounded-lg p-4 sm:p-6",children:[S.jsxs("div",{className:"flex justify-between items-center mb-6",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900",children:"订阅信息"}),S.jsx("button",{onClick:()=>e("/subscription"),className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"管理订阅"})]}),S.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6",children:[S.jsxs("div",{children:[S.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"当前套餐"}),S.jsxs("div",{className:"flex items-center space-x-2",children:[p(o.planType),S.jsxs("span",{className:"text-sm text-green-600",children:["(","active"===o.status?"有效":"无效",")"]})]})]}),S.jsxs("div",{children:[S.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"订阅周期"}),S.jsxs("p",{className:"text-sm text-gray-900",children:[f(o.currentPeriodStart),S.jsx("br",{}),"至 ",f(o.currentPeriodEnd)]})]})]}),S.jsxs("div",{className:"mt-6",children:[S.jsx("h4",{className:"text-sm font-medium text-gray-500 mb-3",children:"套餐特权"}),S.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:o.features.map((e,t)=>S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("svg",{className:"h-4 w-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),S.jsx("span",{className:"text-sm text-gray-700",children:e})]},t))})]}),o.usageLimit>0&&S.jsxs("div",{className:"mt-6",children:[S.jsxs("div",{className:"flex justify-between items-center mb-2",children:[S.jsx("span",{className:"text-sm font-medium text-gray-500",children:"本月用量"}),S.jsxs("span",{className:"text-sm text-gray-900",children:[o.usageCount," / ",o.usageLimit]})]}),S.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2",children:S.jsx("div",{className:"bg-primary-600 h-2 rounded-full",style:{width:`${Math.min(o.usageCount/o.usageLimit*100,100)}%`}})})]})]}),S.jsxs("div",{className:"bg-white shadow rounded-lg p-4 sm:p-6",children:[S.jsx("h3",{className:"text-base sm:text-lg font-medium text-gray-900 mb-4 sm:mb-6",children:"快速操作"}),S.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4",children:[S.jsx("button",{onClick:()=>e("/translate"),className:"flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors",children:S.jsxs("div",{className:"text-center",children:[S.jsx("svg",{className:"mx-auto h-8 w-8 text-gray-400 mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})}),S.jsx("span",{className:"text-sm font-medium text-gray-900",children:"开始翻译"})]})}),S.jsx("button",{onClick:()=>e("/history"),className:"flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors",children:S.jsxs("div",{className:"text-center",children:[S.jsx("svg",{className:"mx-auto h-8 w-8 text-gray-400 mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),S.jsx("span",{className:"text-sm font-medium text-gray-900",children:"翻译历史"})]})}),S.jsx("button",{onClick:()=>e("/help"),className:"flex items-center justify-center p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-colors",children:S.jsxs("div",{className:"text-center",children:[S.jsx("svg",{className:"mx-auto h-8 w-8 text-gray-400 mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),S.jsx("span",{className:"text-sm font-medium text-gray-900",children:"帮助支持"})]})})]})]})]})]})})]})},Dn=()=>{const e=d(),{user:t}=De(e=>e.auth),[n,r]=s.useState({profile:{name:"",email:""},preferences:{defaultSourceLanguage:"auto",defaultTargetLanguage:"zh",theme:"light",language:"zh-CN",autoTranslate:!1,saveHistory:!0,enableNotifications:!0},security:{twoFactorEnabled:!1,lastPasswordChange:""}}),[a,l]=s.useState("profile"),[i,o]=s.useState(!0),[c,u]=s.useState(!1),[m,h]=s.useState(null),f=[{code:"auto",name:"自动检测"},{code:"zh",name:"中文"},{code:"en",name:"English"},{code:"ja",name:"日本語"},{code:"ko",name:"한국어"},{code:"fr",name:"Français"},{code:"de",name:"Deutsch"},{code:"es",name:"Español"},{code:"ru",name:"Русский"},{code:"pt",name:"Português"},{code:"it",name:"Italiano"},{code:"ar",name:"العربية"}];s.useEffect(()=>{p()},[]);const p=async()=>{try{o(!0),setTimeout(()=>{r({profile:{name:t?.name||"张三",email:t?.email||"<EMAIL>"},preferences:{defaultSourceLanguage:"auto",defaultTargetLanguage:"zh",theme:"light",language:"zh-CN",autoTranslate:!1,saveHistory:!0,enableNotifications:!0},security:{twoFactorEnabled:!1,lastPasswordChange:"2025-07-15T10:00:00Z"}}),o(!1)},1e3)}catch(e){console.error("获取设置失败:",e),o(!1)}},g=(e,t,n)=>{r(r=>({...r,[e]:{...r[e],[t]:n}}))};return i?S.jsx("div",{className:"min-h-screen bg-gray-50 flex justify-center items-center",children:S.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):S.jsxs("div",{className:"min-h-screen bg-gray-50",children:[S.jsx("nav",{className:"bg-white shadow-sm",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"flex justify-between h-16",children:[S.jsxs("div",{className:"flex items-center",children:[S.jsx("button",{onClick:()=>e("/"),className:"text-gray-500 hover:text-gray-700 mr-4",children:"← 返回首页"}),S.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"个人设置"})]}),S.jsx("div",{className:"flex items-center space-x-4",children:S.jsx("span",{className:"text-sm text-gray-700",children:t?.name||t?.email})})]})})}),S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[m&&S.jsx("div",{className:"mb-6 p-4 rounded-md "+("success"===m.type?"bg-green-50 border border-green-200 text-green-800":"bg-red-50 border border-red-200 text-red-800"),children:S.jsxs("div",{className:"flex",children:[S.jsx("div",{className:"flex-shrink-0",children:"success"===m.type?S.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}):S.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:S.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),S.jsx("div",{className:"ml-3",children:S.jsx("p",{className:"text-sm font-medium",children:m.text})})]})}),S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6 lg:gap-8",children:[S.jsx("div",{className:"lg:col-span-1",children:S.jsx("nav",{className:"bg-white shadow rounded-lg p-3 sm:p-4",children:S.jsx("ul",{className:"space-y-2",children:[{id:"profile",name:"个人信息",icon:"👤"},{id:"preferences",name:"偏好设置",icon:"⚙️"},{id:"security",name:"安全设置",icon:"🔒"}].map(e=>S.jsx("li",{children:S.jsxs("button",{onClick:()=>l(e.id),className:"w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors "+(a===e.id?"bg-primary-100 text-primary-700":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[S.jsx("span",{className:"mr-3",children:e.icon}),e.name]})},e.id))})})}),S.jsx("div",{className:"lg:col-span-3",children:S.jsxs("div",{className:"bg-white shadow rounded-lg",children:["profile"===a&&S.jsxs("div",{className:"p-4 sm:p-6",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"个人信息"}),S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"姓名"}),S.jsx("input",{type:"text",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:n.profile.name,onChange:e=>g("profile","name",e.target.value)})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"邮箱地址"}),S.jsx("input",{type:"email",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm bg-gray-50",value:n.profile.email,disabled:!0}),S.jsx("p",{className:"mt-1 text-sm text-gray-500",children:"邮箱地址不可修改，如需更换请联系客服"})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"头像"}),S.jsxs("div",{className:"flex items-center space-x-4",children:[S.jsx("div",{className:"h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center",children:S.jsx("span",{className:"text-xl font-bold text-primary-600",children:n.profile.name.charAt(0)})}),S.jsx("button",{className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"更换头像"})]})]})]})]}),"preferences"===a&&S.jsxs("div",{className:"p-4 sm:p-6",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"偏好设置"}),S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"默认源语言"}),S.jsx("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:n.preferences.defaultSourceLanguage,onChange:e=>g("preferences","defaultSourceLanguage",e.target.value),children:f.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"默认目标语言"}),S.jsx("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:n.preferences.defaultTargetLanguage,onChange:e=>g("preferences","defaultTargetLanguage",e.target.value),children:f.filter(e=>"auto"!==e.code).map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))})]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"界面语言"}),S.jsx("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:n.preferences.language,onChange:e=>g("preferences","language",e.target.value),children:[{code:"zh-CN",name:"简体中文"},{code:"zh-TW",name:"繁體中文"},{code:"en-US",name:"English"},{code:"ja-JP",name:"日本語"},{code:"ko-KR",name:"한국어"}].map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-3",children:"主题设置"}),S.jsx("div",{className:"space-y-3",children:[{value:"light",name:"浅色模式",description:"适合白天使用"},{value:"dark",name:"深色模式",description:"适合夜间使用"},{value:"system",name:"跟随系统",description:"自动切换主题"}].map(e=>S.jsxs("label",{className:"flex items-center",children:[S.jsx("input",{type:"radio",name:"theme",value:e.value,checked:n.preferences.theme===e.value,onChange:e=>g("preferences","theme",e.target.value),className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"}),S.jsxs("div",{className:"ml-3",children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),S.jsx("div",{className:"text-sm text-gray-500",children:e.description})]})]},e.value))})]}),S.jsxs("div",{className:"space-y-4",children:[S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:"自动翻译"}),S.jsx("div",{className:"text-sm text-gray-500",children:"输入文本后自动开始翻译"})]}),S.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[S.jsx("input",{type:"checkbox",checked:n.preferences.autoTranslate,onChange:e=>g("preferences","autoTranslate",e.target.checked),className:"sr-only peer"}),S.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:"保存翻译历史"}),S.jsx("div",{className:"text-sm text-gray-500",children:"自动保存您的翻译记录"})]}),S.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[S.jsx("input",{type:"checkbox",checked:n.preferences.saveHistory,onChange:e=>g("preferences","saveHistory",e.target.checked),className:"sr-only peer"}),S.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]}),S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:"通知提醒"}),S.jsx("div",{className:"text-sm text-gray-500",children:"接收重要通知和更新"})]}),S.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[S.jsx("input",{type:"checkbox",checked:n.preferences.enableNotifications,onChange:e=>g("preferences","enableNotifications",e.target.checked),className:"sr-only peer"}),S.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]})]})]})]}),"security"===a&&S.jsxs("div",{className:"p-4 sm:p-6",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-6",children:"安全设置"}),S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{children:[S.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"密码管理"}),S.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:"密码"}),S.jsxs("div",{className:"text-sm text-gray-500",children:["上次修改: ",new Date(n.security.lastPasswordChange).toLocaleDateString("zh-CN")]})]}),S.jsx("button",{onClick:()=>{h({type:"success",text:"密码重置邮件已发送到您的邮箱"}),setTimeout(()=>h(null),3e3)},className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"重置密码"})]})})]}),S.jsxs("div",{children:[S.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"两步验证"}),S.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:"两步验证"}),S.jsx("div",{className:"text-sm text-gray-500",children:n.security.twoFactorEnabled?"已启用":"未启用"})]}),S.jsxs("label",{className:"relative inline-flex items-center cursor-pointer",children:[S.jsx("input",{type:"checkbox",checked:n.security.twoFactorEnabled,onChange:e=>g("security","twoFactorEnabled",e.target.checked),className:"sr-only peer"}),S.jsx("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"})]})]})})]}),S.jsxs("div",{children:[S.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"数据管理"}),S.jsxs("div",{className:"space-y-3",children:[S.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:"导出数据"}),S.jsx("div",{className:"text-sm text-gray-500",children:"下载您的翻译数据"})]}),S.jsx("button",{onClick:()=>{h({type:"success",text:"数据导出请求已提交，稍后将通过邮件发送"}),setTimeout(()=>h(null),3e3)},className:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50",children:"导出数据"})]})}),S.jsx("div",{className:"bg-red-50 rounded-lg p-4 border border-red-200",children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-sm font-medium text-red-900",children:"删除账户"}),S.jsx("div",{className:"text-sm text-red-700",children:"永久删除您的账户和所有数据"})]}),S.jsx("button",{onClick:()=>{window.confirm("确定要删除账户吗？此操作不可撤销。")&&(h({type:"error",text:"账户删除功能暂未开放，请联系客服"}),setTimeout(()=>h(null),3e3))},className:"px-4 py-2 border border-red-300 rounded-md text-sm font-medium text-red-700 hover:bg-red-100",children:"删除账户"})]})})]})]})]})]}),S.jsx("div",{className:"px-6 py-4 bg-gray-50 border-t border-gray-200 rounded-b-lg",children:S.jsx("div",{className:"flex justify-end",children:S.jsx("button",{onClick:async()=>{try{u(!0),setTimeout(()=>{u(!1),h({type:"success",text:"设置已保存"}),setTimeout(()=>h(null),3e3)},1e3)}catch(e){u(!1),h({type:"error",text:"保存失败，请重试"}),setTimeout(()=>h(null),3e3)}},disabled:c,className:"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50",children:c?S.jsxs(S.Fragment,{children:[S.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-white",fill:"none",viewBox:"0 0 24 24",children:[S.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),S.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"保存中..."]}):"保存设置"})})})]})})]})]})]})},Fn=({onPlanSelect:e})=>{const t=Re(),{plans:n,selectedPlan:r,selectedBillingCycle:a}=De(e=>e.subscription),[l,i]=s.useState(null);s.useEffect(()=>{0!==n.list.length||n.loading||t(pe())},[t,n.list.length,n.loading]);const o=e=>{t(je(e))},c=e=>"yearly"===a?e.yearlyPrice:e.monthlyPrice,u=e=>({free:"🎯",basic:"⭐",premium:"💎",enterprise:"🚀"}[e]||"📦"),d=e=>({free:"bg-gray-600 hover:bg-gray-700",basic:"bg-blue-600 hover:bg-blue-700",premium:"bg-purple-600 hover:bg-purple-700",enterprise:"bg-yellow-600 hover:bg-yellow-700"}[e]||"bg-gray-600 hover:bg-gray-700");return n.loading?S.jsx("div",{className:"flex items-center justify-center py-12",children:S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),S.jsx("div",{className:"text-sm text-gray-500",children:"加载订阅套餐中..."})]})}):n.error?S.jsxs("div",{className:"text-center py-12",children:[S.jsx("div",{className:"text-red-600 mb-4",children:"加载订阅套餐失败"}),S.jsx("div",{className:"text-sm text-gray-500",children:n.error}),S.jsx("button",{onClick:()=>t(pe()),className:"mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"重试"})]}):S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[S.jsx("div",{className:"text-center mb-8",children:S.jsxs("div",{className:"inline-flex items-center bg-gray-100 rounded-lg p-1",children:[S.jsx("button",{onClick:()=>o("monthly"),className:`\n              px-4 py-2 text-sm font-medium rounded-md transition-colors\n              ${"monthly"===a?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}\n            `,children:"按月付费"}),S.jsxs("button",{onClick:()=>o("yearly"),className:`\n              px-4 py-2 text-sm font-medium rounded-md transition-colors relative\n              ${"yearly"===a?"bg-white text-gray-900 shadow-sm":"text-gray-600 hover:text-gray-900"}\n            `,children:["按年付费",S.jsx("span",{className:"absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded",children:"省钱"})]})]})}),S.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:n.list.map(n=>{const s=r?.id===n.id,o=(e=>{if("monthly"===a||0===e.monthlyPrice)return 0;const t=12*e.monthlyPrice;return Math.round((t-e.yearlyPrice)/t*100)})(n),m=(e=>"monthly"===a?e.monthlyPrice:Math.round(e.yearlyPrice/12*100)/100)(n);return S.jsxs("div",{className:`\n                relative rounded-lg border-2 p-6 cursor-pointer transition-all duration-200\n                ${s?"border-blue-500 shadow-lg scale-105":(h=n.type,{free:"border-gray-200 bg-white",basic:"border-blue-200 bg-blue-50",premium:"border-purple-200 bg-purple-50",enterprise:"border-yellow-200 bg-yellow-50"}[h]||"border-gray-200 bg-white")}\n                ${l===n.id?"shadow-md scale-102":""}\n              `,onMouseEnter:()=>i(n.id),onMouseLeave:()=>i(null),onClick:()=>(n=>{t(we(n)),e?.(n,a)})(n),children:["premium"===n.type&&S.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:S.jsx("span",{className:"bg-purple-600 text-white px-3 py-1 text-xs font-medium rounded-full",children:"推荐"})}),o>0&&"yearly"===a&&S.jsx("div",{className:"absolute -top-3 -right-3",children:S.jsxs("span",{className:"bg-green-500 text-white px-2 py-1 text-xs font-medium rounded-full",children:["省",o,"%"]})}),S.jsxs("div",{className:"text-center",children:[S.jsxs("div",{className:"mb-4",children:[S.jsx("div",{className:"text-3xl mb-2",children:u(n.type)}),S.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:n.name}),S.jsx("p",{className:"text-sm text-gray-600 mt-1",children:n.description})]}),S.jsx("div",{className:"mb-6",children:"free"===n.type?S.jsx("div",{className:"text-2xl font-bold text-gray-900",children:"免费"}):S.jsxs("div",{children:[S.jsxs("div",{className:"text-3xl font-bold text-gray-900",children:["¥",c(n),S.jsxs("span",{className:"text-sm font-normal text-gray-600",children:["/","yearly"===a?"年":"月"]})]}),"yearly"===a&&m!==n.monthlyPrice&&S.jsxs("div",{className:"text-sm text-gray-500",children:["相当于 ¥",m,"/月"]})]})}),S.jsxs("div",{className:"space-y-3 mb-6 text-left",children:[S.jsxs("div",{className:"flex items-center text-sm",children:[S.jsx("svg",{className:"w-4 h-4 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),S.jsx("span",{children:-1===n.monthlyCharacterLimit?"无限字符":`${n.monthlyCharacterLimit.toLocaleString()} 字符/月`})]}),S.jsxs("div",{className:"flex items-center text-sm",children:[S.jsx("svg",{className:"w-4 h-4 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),S.jsx("span",{children:-1===n.maxTranslationsPerDay?"无限翻译次数":`${n.maxTranslationsPerDay} 次翻译/天`})]}),S.jsxs("div",{className:"flex items-center text-sm",children:[S.jsx("svg",{className:"w-4 h-4 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),S.jsxs("span",{children:[n.maxConcurrentTranslations," 个并发翻译"]})]}),n.supportsBatchTranslation&&S.jsxs("div",{className:"flex items-center text-sm",children:[S.jsx("svg",{className:"w-4 h-4 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),S.jsx("span",{children:"批量翻译"})]}),n.prioritySupport&&S.jsxs("div",{className:"flex items-center text-sm",children:[S.jsx("svg",{className:"w-4 h-4 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),S.jsx("span",{children:"优先技术支持"})]}),n.apiAccess&&S.jsxs("div",{className:"flex items-center text-sm",children:[S.jsx("svg",{className:"w-4 h-4 text-green-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),S.jsx("span",{children:"API 访问"})]})]}),S.jsx("button",{className:`\n                    w-full py-2 px-4 rounded-lg text-white font-medium transition-colors\n                    ${s?"bg-blue-600 hover:bg-blue-700":d(n.type)}\n                  `,children:"free"===n.type?"当前套餐":"选择套餐"})]})]},n.id);var h})}),S.jsxs("div",{className:"mt-8 text-center text-sm text-gray-600",children:[S.jsx("p",{children:"所有套餐都包含基础翻译功能和50+种语言支持"}),S.jsx("p",{className:"mt-1",children:"年付用户享受优惠价格，随时可以取消订阅"})]})]})},Bn=({originalAmount:e,planType:t,onValidationChange:n,disabled:r=!1})=>{const a=Re(),{coupon:l}=De(e=>e.payment),[i,o]=s.useState(l.code),[c,u]=s.useState(!1);s.useEffect(()=>{l.validation&&n?.(l.validation)},[l.validation,n]);const d=async()=>{if(i.trim()){u(!0);try{await a(Ce({code:i,originalAmount:e,planType:t}))}finally{u(!1)}}};return S.jsxs("div",{className:"space-y-3",children:[S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsxs("div",{className:"flex-1 relative",children:[S.jsx("input",{type:"text",value:i,onChange:e=>{const t=e.target.value.toUpperCase();o(t),a(_e(t)),t||a(Pe())},onKeyPress:e=>{"Enter"!==e.key||r||d()},placeholder:"输入优惠码",disabled:r,className:`\n              w-full px-3 py-2 border rounded-md text-sm\n              ${r?"bg-gray-100 text-gray-500 cursor-not-allowed":"bg-white text-gray-900"}\n              ${!0===l.validation?.valid?"border-green-500 focus:ring-green-500":!1===l.validation?.valid?"border-red-500 focus:ring-red-500":"border-gray-300 focus:ring-blue-500"}\n              focus:outline-none focus:ring-2 focus:border-transparent\n            `}),(l.loading||c||l.validation)&&S.jsx("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2",children:l.loading||c?S.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"}):l.validation?.valid?S.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):!1===l.validation?.valid?S.jsx("svg",{className:"w-4 h-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}):null})]}),i&&!l.validation?.valid&&S.jsx("button",{onClick:d,disabled:r||l.loading||c,className:`\n              px-4 py-2 text-sm font-medium rounded-md transition-colors\n              ${r||l.loading||c?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-blue-600 text-white hover:bg-blue-700"}\n            `,children:l.loading||c?"验证中...":"应用"}),l.validation?.valid&&S.jsx("button",{onClick:()=>{o(""),a(_e("")),a(Pe())},disabled:r,className:"px-4 py-2 text-sm font-medium text-red-600 border border-red-300 rounded-md hover:bg-red-50 transition-colors",children:"移除"})]}),l.validation&&S.jsx("div",{className:"text-sm",children:l.validation.valid?S.jsxs("div",{className:"flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded-md",children:[S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),S.jsx("span",{className:"text-green-700",children:l.validation.couponName||"优惠码已应用"})]}),S.jsxs("div",{className:"text-green-700 font-medium",children:["-¥",l.validation.discountAmount?.toFixed(2)]})]}):S.jsxs("div",{className:"flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-md",children:[S.jsx("svg",{className:"w-4 h-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),S.jsx("span",{className:"text-red-700",children:l.validation.error||l.error||"优惠码无效"})]})}),!i&&!r&&S.jsx("div",{className:"text-xs text-gray-500",children:"输入优惠码享受额外折扣"})]})},In=({selectedMethod:e,onMethodChange:t,disabled:n=!1})=>S.jsxs("div",{className:"space-y-3",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"选择支付方式"}),S.jsx("div",{className:"grid grid-cols-1 gap-3",children:[{id:"alipay",name:"支付宝",icon:"💙",description:"使用支付宝扫码支付",popular:!0},{id:"wechat_pay",name:"微信支付",icon:"💚",description:"使用微信扫码支付",popular:!0},{id:"credit_card",name:"信用卡",icon:"💳",description:"支持 Visa、MasterCard 等",popular:!1},{id:"paypal",name:"PayPal",icon:"🌐",description:"使用 PayPal 账户支付",popular:!1}].map(r=>S.jsxs("label",{className:`\n              relative flex items-center p-4 border rounded-lg cursor-pointer transition-all\n              ${n?"cursor-not-allowed opacity-50":"hover:border-blue-300 hover:bg-blue-50"}\n              ${e===r.id?"border-blue-500 bg-blue-50":"border-gray-200 bg-white"}\n            `,children:[S.jsx("input",{type:"radio",name:"paymentMethod",value:r.id,checked:e===r.id,onChange:e=>t(e.target.value),disabled:n,className:"sr-only"}),S.jsx("div",{className:`\n              flex-shrink-0 w-4 h-4 border-2 rounded-full mr-4 transition-colors\n              ${e===r.id?"border-blue-500 bg-blue-500":"border-gray-300"}\n            `,children:e===r.id&&S.jsx("div",{className:"w-full h-full rounded-full bg-white transform scale-50"})}),S.jsx("div",{className:"flex-shrink-0 w-8 h-8 mr-4 text-2xl flex items-center justify-center",children:r.icon}),S.jsxs("div",{className:"flex-1",children:[S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("span",{className:"font-medium text-gray-900",children:r.name}),r.popular&&S.jsx("span",{className:"px-2 py-1 text-xs bg-orange-100 text-orange-600 rounded-full",children:"推荐"})]}),S.jsx("p",{className:"text-sm text-gray-600 mt-1",children:r.description})]}),e===r.id&&S.jsx("div",{className:"flex-shrink-0 ml-4",children:S.jsx("svg",{className:"w-5 h-5 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})})]},r.id))}),S.jsx("div",{className:"mt-4 p-3 bg-gray-50 rounded-lg",children:S.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-600",children:[S.jsx("svg",{className:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-9a2 2 0 00-2-2H6a2 2 0 00-2 2v9a2 2 0 002 2zm10-12V6a4 4 0 00-8 0v3h8z"})}),S.jsx("span",{children:"所有支付信息均采用 SSL 加密保护，确保您的资金安全"})]})})]}),Un=()=>{const e=Re(),t=d(),{currentSubscription:n,selectedPlan:r,selectedBillingCycle:a}=De(e=>e.subscription),{coupon:l,currentPayment:i}=De(e=>e.payment),[o,c]=s.useState("plans"),[u,m]=s.useState("alipay"),[h,f]=s.useState(0),[p,g]=s.useState(0);s.useEffect(()=>{e(pe()),e(ge())},[e]),s.useEffect(()=>{if(r){const e="yearly"===a?r.yearlyPrice:r.monthlyPrice;g(e),f(e)}},[r,a]);const x=e=>{const n=setInterval(async()=>{try{const r=await fetch(`/api/v1/payment/${e}/status`,{headers:{Authorization:`Bearer ${localStorage.getItem("token")}`}}),a=await r.json();"completed"===a.data.status?(clearInterval(n),t("/subscription/success")):"failed"===a.data.status&&(clearInterval(n),c("payment"))}catch(r){console.error("Payment status check failed:",r)}},3e3);setTimeout(()=>{clearInterval(n)},3e5)},y=()=>{"payment"===o?(c("plans"),e(Ae())):"processing"===o&&c("payment")};return S.jsx("div",{className:"min-h-screen bg-gray-50",children:S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[S.jsxs("div",{className:"text-center mb-8",children:[S.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"选择订阅套餐"}),S.jsx("p",{className:"text-gray-600",children:"升级您的 SynText 体验，享受更多翻译额度和高级功能"})]}),(()=>{if(!n.data)return null;const e=n.data,t="active"===e.status,r=new Date(e.endDate),a=new Date,s=Math.ceil((r.getTime()-a.getTime())/864e5);return S.jsx("div",{className:"mb-8 p-6 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200",children:S.jsxs("div",{className:"flex items-center justify-between",children:[S.jsxs("div",{children:[S.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"当前订阅"}),S.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:[e.plan.name," - ","yearly"===e.billingCycle?"年付":"月付"]}),t&&s>0&&S.jsxs("p",{className:"text-sm text-green-600 mt-2",children:["还有 ",s," 天到期"]})]}),S.jsxs("div",{className:"text-right",children:[S.jsx("div",{className:`\n              px-3 py-1 rounded-full text-sm font-medium\n              ${t?"bg-green-100 text-green-700":"bg-red-100 text-red-700"}\n            `,children:t?"活跃":"已过期"}),S.jsxs("div",{className:"text-sm text-gray-600 mt-2",children:["¥",e.amount," / ","yearly"===e.billingCycle?"年":"月"]})]})]})})})(),S.jsx("div",{className:"flex items-center justify-center mb-8",children:S.jsxs("div",{className:"flex items-center space-x-4",children:[S.jsx("div",{className:`\n              flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium\n              ${"plans"===o?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"}\n            `,children:"1"}),S.jsx("div",{className:"w-16 h-0.5 bg-gray-200"}),S.jsx("div",{className:`\n              flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium\n              ${"payment"===o||"processing"===o?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"}\n            `,children:"2"}),S.jsx("div",{className:"w-16 h-0.5 bg-gray-200"}),S.jsx("div",{className:`\n              flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium\n              ${"processing"===o?"bg-blue-600 text-white":"bg-gray-200 text-gray-600"}\n            `,children:"3"})]})}),"plans"===o&&S.jsx(Fn,{onPlanSelect:(e,n)=>{"free"!==e.type?c("payment"):t("/translate")}}),"payment"===o&&r&&S.jsx("div",{className:"max-w-2xl mx-auto",children:S.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-6",children:[S.jsxs("div",{className:"flex items-center justify-between mb-6",children:[S.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"支付信息"}),S.jsx("button",{onClick:y,className:"text-sm text-blue-600 hover:text-blue-700",children:"返回选择套餐"})]}),S.jsxs("div",{className:"mb-6 p-4 bg-gray-50 rounded-lg",children:[S.jsx("h3",{className:"font-medium text-gray-900 mb-2",children:"订单摘要"}),S.jsxs("div",{className:"space-y-2 text-sm",children:[S.jsxs("div",{className:"flex justify-between",children:[S.jsx("span",{children:"套餐"}),S.jsx("span",{children:r.name})]}),S.jsxs("div",{className:"flex justify-between",children:[S.jsx("span",{children:"计费周期"}),S.jsx("span",{children:"yearly"===a?"年付":"月付"})]}),S.jsxs("div",{className:"flex justify-between",children:[S.jsx("span",{children:"原价"}),S.jsxs("span",{children:["¥",p]})]}),l.validation?.valid&&S.jsxs("div",{className:"flex justify-between text-green-600",children:[S.jsx("span",{children:"优惠折扣"}),S.jsxs("span",{children:["-¥",l.validation.discountAmount?.toFixed(2)]})]}),S.jsxs("div",{className:"border-t pt-2 flex justify-between font-medium",children:[S.jsx("span",{children:"应付金额"}),S.jsxs("span",{className:"text-lg",children:["¥",h.toFixed(2)]})]})]})]}),S.jsx("div",{className:"mb-6",children:S.jsx(Bn,{originalAmount:p,planType:r.type,onValidationChange:e=>{e?.valid&&void 0!==e.finalAmount?f(e.finalAmount):f(p)}})}),S.jsx("div",{className:"mb-6",children:S.jsx(In,{selectedMethod:u,onMethodChange:m})}),S.jsx("button",{onClick:async()=>{if(r){c("processing");try{const t=await e(ye({planType:r.type,billingCycle:a,autoRenewal:!0}));if(ye.rejected.match(t))throw new Error(t.payload);const n=t.payload,s=await e(Se({subscriptionId:n.id,amount:h,currency:"CNY",method:u,description:`${r.name} - ${"yearly"===a?"年费":"月费"}`,couponId:l.validation?.valid?l.validation.couponId:void 0,returnUrl:`${window.location.origin}/subscription/success`}));if(Se.rejected.match(s))throw new Error(s.payload);const i=s.payload;i.paymentUrl?window.open(i.paymentUrl,"_blank"):i.qrCode,x(i.paymentId)}catch(t){console.error("Payment creation failed:",t),c("payment")}}},disabled:!r||i.loading,className:`\n                  w-full py-3 px-4 rounded-lg text-white font-medium transition-colors\n                  ${i.loading?"bg-gray-400 cursor-not-allowed":"bg-blue-600 hover:bg-blue-700"}\n                `,children:i.loading?"处理中...":`支付 ¥${h.toFixed(2)}`})]})}),"processing"===o&&S.jsx("div",{className:"max-w-md mx-auto text-center",children:S.jsxs("div",{className:"bg-white rounded-lg shadow-sm border border-gray-200 p-8",children:[S.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"}),S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"正在处理支付"}),S.jsx("p",{className:"text-gray-600",children:"请在新窗口中完成支付，支付完成后页面将自动跳转..."}),S.jsx("button",{onClick:y,className:"mt-4 text-sm text-blue-600 hover:text-blue-700",children:"返回修改支付方式"})]})})]})})},Wn=()=>{const e=d(),{user:t}=De(e=>e.auth),[n,r]=s.useState("all"),[a,l]=s.useState(null),[i,o]=s.useState({name:"",email:"",subject:"",message:""}),[c,u]=s.useState(!1),m=[{id:1,question:"如何开始使用 SynText 翻译服务？",answer:"您只需要注册账户并登录，然后在翻译页面输入要翻译的文本，选择源语言和目标语言，点击翻译按钮即可。我们支持自动检测源语言功能。",category:"basic"},{id:2,question:"SynText 支持哪些语言？",answer:"我们支持50多种主流语言，包括中文、英文、日文、韩文、法语、德语、西班牙语、俄语、阿拉伯语等。您可以在翻译页面查看完整的语言列表。",category:"translation"},{id:3,question:"翻译结果的准确性如何？",answer:"我们使用基于 DeepSeek AI 的先进翻译引擎，翻译准确率通常在90%以上。系统会显示置信度分数，帮助您评估翻译质量。对于专业术语，建议结合语境进行人工校对。",category:"translation"},{id:4,question:"如何查看和管理翻译历史？",answer:"在翻译历史页面，您可以查看所有翻译记录，支持按语言、时间、收藏状态进行筛选。您还可以收藏重要翻译、重新翻译或复制翻译结果。",category:"basic"},{id:5,question:"免费版和高级版有什么区别？",answer:"免费版提供基础翻译功能，每月有字符数限制。高级版提供无限翻译、批量翻译、优先处理、高级API接口等功能。企业版还包含定制化服务和专属支持。",category:"billing"},{id:6,question:"如何修改个人信息和偏好设置？",answer:"在个人设置页面，您可以修改姓名、默认翻译语言、界面主题、通知设置等。还可以启用两步验证来提高账户安全性。",category:"account"},{id:7,question:"翻译速度慢怎么办？",answer:"翻译速度可能受网络状况、文本长度和服务器负载影响。建议检查网络连接，避免翻译过长文本。高级版用户享有优先处理，速度更快。",category:"technical"},{id:8,question:"如何导出我的翻译数据？",answer:"在个人设置的安全设置中，您可以申请导出所有翻译数据。我们会将数据整理后通过邮件发送给您，通常需要1-3个工作日。",category:"account"}],h=[{title:"注册账户",description:"创建您的 SynText 账户，获取翻译服务权限",icon:S.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"})})},{title:"输入文本",description:"在翻译页面输入要翻译的内容，选择语言",icon:S.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})},{title:"获取翻译",description:"点击翻译按钮，即可获得高质量的翻译结果",icon:S.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})})},{title:"管理历史",description:"查看翻译历史，收藏重要内容，随时回顾",icon:S.jsx("svg",{className:"h-6 w-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}],f="all"===n?m:m.filter(e=>e.category===n);return S.jsxs("div",{className:"min-h-screen bg-gray-50",children:[S.jsx("nav",{className:"bg-white shadow-sm",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"flex justify-between h-16",children:[S.jsxs("div",{className:"flex items-center",children:[S.jsx("button",{onClick:()=>e("/"),className:"text-gray-500 hover:text-gray-700 mr-4",children:"← 返回首页"}),S.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"帮助中心"})]}),S.jsx("div",{className:"flex items-center space-x-4",children:S.jsx("span",{className:"text-sm text-gray-700",children:t?.name||t?.email})})]})})}),S.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[S.jsxs("div",{className:"text-center mb-12",children:[S.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:"我们来帮助您"}),S.jsx("p",{className:"text-lg text-gray-600 max-w-2xl mx-auto",children:"在这里您可以找到使用 SynText 的详细指南、常见问题解答和联系支持的方式"})]}),S.jsxs("div",{className:"mb-12",children:[S.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-6 text-center",children:"快速开始指南"}),S.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6",children:h.map((e,t)=>S.jsxs("div",{className:"bg-white rounded-lg shadow-sm p-4 sm:p-6 text-center",children:[S.jsx("div",{className:"w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center mx-auto mb-4",children:S.jsx("div",{className:"text-primary-600",children:e.icon})}),S.jsx("h4",{className:"text-lg font-semibold text-gray-900 mb-2",children:e.title}),S.jsx("p",{className:"text-sm text-gray-600",children:e.description}),S.jsxs("div",{className:"mt-4 text-xs text-gray-400",children:["步骤 ",t+1]})]},t))})]}),S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8",children:[S.jsx("div",{className:"lg:col-span-2",children:S.jsxs("div",{className:"bg-white shadow rounded-lg",children:[S.jsx("div",{className:"px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200",children:S.jsx("h3",{className:"text-base sm:text-lg font-medium text-gray-900",children:"常见问题"})}),S.jsx("div",{className:"px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200",children:S.jsx("div",{className:"flex flex-wrap gap-2",children:[{id:"all",name:"全部"},{id:"basic",name:"基础使用"},{id:"account",name:"账户管理"},{id:"translation",name:"翻译功能"},{id:"billing",name:"订阅付费"},{id:"technical",name:"技术问题"}].map(e=>S.jsx("button",{onClick:()=>r(e.id),className:"px-3 py-1 text-sm rounded-full transition-colors "+(n===e.id?"bg-primary-100 text-primary-700":"bg-gray-100 text-gray-600 hover:bg-gray-200"),children:e.name},e.id))})}),S.jsx("div",{className:"divide-y divide-gray-200",children:f.map(e=>S.jsxs("div",{className:"px-4 sm:px-6 py-3 sm:py-4",children:[S.jsxs("button",{onClick:()=>{return t=e.id,void l(a===t?null:t);var t},className:"w-full text-left flex justify-between items-center",children:[S.jsx("h4",{className:"text-sm font-medium text-gray-900 pr-4",children:e.question}),S.jsx("svg",{className:"h-5 w-5 text-gray-500 transition-transform "+(a===e.id?"rotate-180":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),a===e.id&&S.jsx("div",{className:"mt-3 text-sm text-gray-600",children:e.answer})]},e.id))})]})}),S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{className:"bg-white shadow rounded-lg p-6",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"快速链接"}),S.jsxs("div",{className:"space-y-3",children:[S.jsxs("button",{onClick:()=>e("/translate"),className:"w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50",children:[S.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})}),"开始翻译"]}),S.jsxs("button",{onClick:()=>e("/history"),className:"w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50",children:[S.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),"翻译历史"]}),S.jsxs("button",{onClick:()=>e("/settings"),className:"w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50",children:[S.jsxs("svg",{className:"h-5 w-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),"个人设置"]}),S.jsxs("button",{onClick:()=>e("/profile"),className:"w-full flex items-center px-3 py-2 text-sm text-gray-700 rounded-md hover:bg-gray-50",children:[S.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),"个人中心"]})]})]}),S.jsxs("div",{className:"bg-white shadow rounded-lg p-6",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"联系我们"}),S.jsxs("form",{onSubmit:async e=>{e.preventDefault(),u(!0);try{setTimeout(()=>{alert("消息已发送，我们会尽快回复您！"),o({name:"",email:"",subject:"",message:""}),u(!1)},1e3)}catch(t){console.error("提交失败:",t),alert("提交失败，请重试"),u(!1)}},className:"space-y-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"姓名"}),S.jsx("input",{type:"text",value:i.name,onChange:e=>o({...i,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",required:!0})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"邮箱"}),S.jsx("input",{type:"email",value:i.email,onChange:e=>o({...i,email:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",required:!0})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"主题"}),S.jsxs("select",{value:i.subject,onChange:e=>o({...i,subject:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",required:!0,children:[S.jsx("option",{value:"",children:"请选择主题"}),S.jsx("option",{value:"technical",children:"技术问题"}),S.jsx("option",{value:"billing",children:"订阅付费"}),S.jsx("option",{value:"feature",children:"功能建议"}),S.jsx("option",{value:"other",children:"其他问题"})]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"消息内容"}),S.jsx("textarea",{value:i.message,onChange:e=>o({...i,message:e.target.value}),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",placeholder:"请详细描述您的问题...",required:!0})]}),S.jsx("button",{type:"submit",disabled:c,className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50",children:c?"发送中...":"发送消息"})]})]}),S.jsxs("div",{className:"bg-white shadow rounded-lg p-6",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"其他支持方式"}),S.jsxs("div",{className:"space-y-3",children:[S.jsxs("div",{className:"flex items-center",children:[S.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),S.jsx("span",{className:"text-sm text-gray-600",children:"<EMAIL>"})]}),S.jsxs("div",{className:"flex items-center",children:[S.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),S.jsx("span",{className:"text-sm text-gray-600",children:"工作日 9:00-18:00"})]}),S.jsxs("div",{className:"flex items-center",children:[S.jsx("svg",{className:"h-5 w-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),S.jsx("span",{className:"text-sm text-gray-600",children:"24小时内回复"})]})]})]})]})]})]})]})},qn=()=>{const e=d(),[t,n]=s.useState(null),[r,a]=s.useState(!0),[l,i]=s.useState(null),[o,c]=s.useState(!1);s.useEffect(()=>{u()},[]);const u=async()=>{try{a(!0),i(null);const e=await D();n(e.data)}catch(e){console.error("获取仪表板数据失败:",e),i(e.response?.data?.message||"获取数据失败"),n({users:{total:0,active:0,new:0},translations:{total:0,today:0,avgConfidence:0},revenue:{total:0,thisMonth:0,growth:0},system:{uptime:"0%",avgResponseTime:0,errorRate:0}})}finally{a(!1)}},m=async()=>{c(!0),await u(),c(!1)};if(r)return S.jsx("div",{className:"min-h-screen bg-gray-50",children:S.jsx("div",{className:"flex justify-center items-center h-64",children:S.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"})})});if(!t)return null;const h=({title:e,value:t,subtitle:n,icon:r,trend:a})=>S.jsx("div",{className:"bg-white overflow-hidden shadow-lg rounded-xl border border-gray-100",children:S.jsx("div",{className:"p-6",children:S.jsxs("div",{className:"flex items-center",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"h-10 w-10 text-blue-600",children:r})}),S.jsx("div",{className:"ml-5 w-0 flex-1",children:S.jsxs("dl",{children:[S.jsx("dt",{className:"text-sm font-medium text-gray-500 truncate",children:e}),S.jsxs("dd",{className:"flex items-baseline",children:[S.jsx("div",{className:"text-2xl font-semibold text-gray-900",children:"number"==typeof t?t.toLocaleString():t}),a&&S.jsxs("div",{className:"ml-2 flex items-baseline text-sm font-semibold "+(a.isPositive?"text-green-600":"text-red-600"),children:[S.jsx("svg",{className:"self-center flex-shrink-0 h-5 w-5 "+(a.isPositive?"text-green-500":"text-red-500"),viewBox:"0 0 20 20",fill:"currentColor",children:S.jsx("path",{fillRule:"evenodd",d:a.isPositive?"M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z":"M14.707 10.293a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 111.414-1.414L9 12.586V5a1 1 0 012 0v7.586l2.293-2.293a1 1 0 011.414 0z",clipRule:"evenodd"})}),S.jsx("span",{className:"sr-only",children:a.isPositive?"增长":"下降"}),Math.abs(a.value),"%"]})]}),n&&S.jsx("dd",{className:"text-sm text-gray-500 mt-1",children:n})]})})]})})});return S.jsxs("div",{className:"min-h-screen bg-gray-50",children:[S.jsx("header",{className:"bg-white shadow-sm border-b border-gray-200",children:S.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:S.jsxs("div",{className:"flex justify-between items-center py-6",children:[S.jsxs("div",{className:"flex items-center space-x-4",children:[S.jsx("button",{onClick:()=>e("/"),className:"text-gray-500 hover:text-gray-700",children:"← 返回首页"}),S.jsxs("div",{children:[S.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"管理后台"}),S.jsx("p",{className:"text-sm text-gray-600",children:"系统运营概览"})]})]}),S.jsx("div",{className:"flex items-center space-x-4",children:S.jsxs("button",{onClick:m,disabled:o,className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:[S.jsx("svg",{className:"w-4 h-4 mr-2 "+(o?"animate-spin":""),fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})}),o?"刷新中...":"刷新数据"]})})]})})}),S.jsxs("main",{className:"max-w-7xl mx-auto py-8 px-4 sm:px-6 lg:px-8",children:[l&&S.jsx("div",{className:"mb-6 bg-red-50 border border-red-200 rounded-lg p-4",children:S.jsxs("div",{className:"flex",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:S.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),S.jsxs("div",{className:"ml-3",children:[S.jsx("h3",{className:"text-sm font-medium text-red-800",children:"数据加载失败"}),S.jsx("div",{className:"mt-2 text-sm text-red-700",children:S.jsx("p",{children:l})}),S.jsx("div",{className:"mt-4",children:S.jsx("button",{onClick:m,className:"bg-red-100 border border-red-300 rounded-md px-3 py-1 text-sm font-medium text-red-800 hover:bg-red-200",children:"重试"})})]})]})}),S.jsxs("div",{className:"mb-8",children:[S.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"快速操作"}),S.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4",children:[S.jsxs("button",{onClick:()=>e("/admin/users"),className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center",children:S.jsx("svg",{className:"w-5 h-5 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})})}),S.jsxs("div",{className:"ml-4 text-left",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:"用户管理"}),S.jsx("p",{className:"text-sm text-gray-500",children:"管理系统用户"})]})]}),S.jsxs("button",{onClick:()=>e("/admin/translations"),className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center",children:S.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})})})}),S.jsxs("div",{className:"ml-4 text-left",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:"翻译管理"}),S.jsx("p",{className:"text-sm text-gray-500",children:"查看翻译记录"})]})]}),S.jsxs("button",{onClick:()=>e("/admin/coupons"),className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center",children:S.jsx("svg",{className:"w-5 h-5 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})})})}),S.jsxs("div",{className:"ml-4 text-left",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:"优惠券管理"}),S.jsx("p",{className:"text-sm text-gray-500",children:"管理优惠券"})]})]}),S.jsxs("button",{className:"flex items-center p-4 bg-white rounded-lg shadow hover:shadow-md transition-shadow border border-gray-200",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("div",{className:"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center",children:S.jsxs("svg",{className:"w-5 h-5 text-orange-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]})})}),S.jsxs("div",{className:"ml-4 text-left",children:[S.jsx("p",{className:"text-sm font-medium text-gray-900",children:"系统设置"}),S.jsx("p",{className:"text-sm text-gray-500",children:"系统配置"})]})]})]})]}),S.jsxs("div",{className:"mb-8",children:[S.jsx("h2",{className:"text-lg font-semibold text-gray-900 mb-4",children:"数据概览"}),S.jsxs("div",{className:"grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4",children:[S.jsx(h,{title:"总用户数",value:t.users.total,subtitle:`活跃用户: ${t.users.active.toLocaleString()}`,icon:S.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"})})}),S.jsx(h,{title:"翻译总数",value:t.translations.total,subtitle:`今日: ${t.translations.today.toLocaleString()}`,icon:S.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"})})}),S.jsx(h,{title:"总收入",value:`¥${t.revenue.total.toLocaleString()}`,subtitle:`本月: ¥${t.revenue.thisMonth.toLocaleString()}`,trend:{value:t.revenue.growth,isPositive:t.revenue.growth>0},icon:S.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})})}),S.jsx(h,{title:"系统可用性",value:t.system.uptime,subtitle:`平均响应: ${t.system.avgResponseTime}ms`,icon:S.jsx("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})})})]})]}),S.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[S.jsxs("div",{className:"bg-white shadow-lg rounded-xl p-6 border border-gray-100",children:[S.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"用户统计"}),S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{className:"flex justify-between items-center",children:[S.jsx("span",{className:"text-sm font-medium text-gray-600",children:"新用户 (今日)"}),S.jsx("span",{className:"text-2xl font-bold text-green-600",children:t.users.new.toLocaleString()})]}),S.jsxs("div",{className:"flex justify-between items-center",children:[S.jsx("span",{className:"text-sm font-medium text-gray-600",children:"活跃用户比例"}),S.jsxs("span",{className:"text-lg font-semibold text-gray-900",children:[t.users.total>0?(t.users.active/t.users.total*100).toFixed(1):0,"%"]})]}),S.jsxs("div",{className:"pt-4",children:[S.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[S.jsx("span",{children:"活跃度"}),S.jsxs("span",{children:[t.users.active.toLocaleString()," / ",t.users.total.toLocaleString()]})]}),S.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:S.jsx("div",{className:"bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-300",style:{width:(t.users.total>0?t.users.active/t.users.total*100:0)+"%"}})})]})]})]}),S.jsxs("div",{className:"bg-white shadow-lg rounded-xl p-6 border border-gray-100",children:[S.jsx("h3",{className:"text-lg font-semibold text-gray-900 mb-6",children:"翻译质量"}),S.jsxs("div",{className:"space-y-6",children:[S.jsxs("div",{className:"flex justify-between items-center",children:[S.jsx("span",{className:"text-sm font-medium text-gray-600",children:"平均质量分数"}),S.jsxs("span",{className:"text-2xl font-bold text-blue-600",children:[(100*t.translations.avgConfidence).toFixed(1),"%"]})]}),S.jsxs("div",{className:"pt-4",children:[S.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[S.jsx("span",{children:"质量评级"}),S.jsx("span",{children:t.translations.avgConfidence>=.9?"优秀":t.translations.avgConfidence>=.8?"良好":t.translations.avgConfidence>=.7?"一般":"需改进"})]}),S.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3",children:S.jsx("div",{className:"h-3 rounded-full transition-all duration-300 "+(t.translations.avgConfidence>=.9?"bg-gradient-to-r from-green-400 to-green-600":t.translations.avgConfidence>=.8?"bg-gradient-to-r from-blue-400 to-blue-600":t.translations.avgConfidence>=.7?"bg-gradient-to-r from-yellow-400 to-yellow-600":"bg-gradient-to-r from-red-400 to-red-600"),style:{width:100*t.translations.avgConfidence+"%"}})})]})]})]})]})]})]})},Hn=()=>{const[e,t]=s.useState([]),[n,r]=s.useState(!0),[a,l]=s.useState({page:1,limit:20,search:"",role:"",status:""}),[i,o]=s.useState({total:0,totalPages:0});s.useEffect(()=>{c()},[a.page,a.role,a.status]);const c=async()=>{try{r(!0),setTimeout(()=>{t([{id:"1",email:"<EMAIL>",name:"张三",role:"user",status:"active",translationCount:150,createdAt:"2025-08-01T10:00:00Z",lastLoginAt:"2025-08-03T09:00:00Z",subscription:{planType:"premium",status:"active"}},{id:"2",email:"<EMAIL>",name:"李四",role:"premium",status:"active",translationCount:500,createdAt:"2025-07-15T10:00:00Z",lastLoginAt:"2025-08-02T14:30:00Z"},{id:"3",email:"<EMAIL>",name:"管理员",role:"admin",status:"active",translationCount:0,createdAt:"2025-06-01T10:00:00Z",lastLoginAt:"2025-08-03T08:00:00Z"}]),o({total:3,totalPages:1}),r(!1)},1e3)}catch(e){console.error("获取用户列表失败:",e),r(!1)}},u=()=>{l({...a,page:1}),c()},d=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"});return S.jsxs("div",{children:[S.jsxs("div",{className:"mb-8",children:[S.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"用户管理"}),S.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"管理系统中的所有用户"})]}),S.jsx("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"搜索用户"}),S.jsxs("div",{className:"flex",children:[S.jsx("input",{type:"text",className:"flex-1 block w-full px-3 py-2 border border-gray-300 rounded-l-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500",placeholder:"邮箱或姓名",value:a.search,onChange:e=>l({...a,search:e.target.value}),onKeyPress:e=>"Enter"===e.key&&u()}),S.jsx("button",{onClick:u,className:"px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100",children:S.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"角色"}),S.jsxs("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:a.role,onChange:e=>{return t=e.target.value,void l({...a,role:t,page:1});var t},children:[S.jsx("option",{value:"",children:"全部角色"}),S.jsx("option",{value:"user",children:"普通用户"}),S.jsx("option",{value:"premium",children:"高级用户"}),S.jsx("option",{value:"admin",children:"管理员"})]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"状态"}),S.jsxs("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:a.status,onChange:e=>{return t=e.target.value,void l({...a,status:t,page:1});var t},children:[S.jsx("option",{value:"",children:"全部状态"}),S.jsx("option",{value:"active",children:"活跃"}),S.jsx("option",{value:"inactive",children:"非活跃"}),S.jsx("option",{value:"banned",children:"已禁用"})]})]}),S.jsx("div",{className:"flex items-end",children:S.jsx("button",{onClick:()=>window.location.reload(),className:"w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"重置筛选"})})]})}),S.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:n?S.jsx("div",{className:"flex justify-center items-center h-64",children:S.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):S.jsxs(S.Fragment,{children:[S.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[S.jsx("thead",{className:"bg-gray-50",children:S.jsxs("tr",{children:[S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户信息"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"角色/状态"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"翻译次数"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"注册时间"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"最后登录"}),S.jsx("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"操作"})]})}),S.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>{return S.jsxs("tr",{className:"hover:bg-gray-50",children:[S.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:S.jsxs("div",{className:"flex items-center",children:[S.jsx("div",{className:"flex-shrink-0 h-10 w-10",children:S.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:S.jsx("span",{className:"text-sm font-medium text-gray-700",children:e.name?.charAt(0)||e.email.charAt(0).toUpperCase()})})}),S.jsxs("div",{className:"ml-4",children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name||"未设置"}),S.jsx("div",{className:"text-sm text-gray-500",children:e.email})]})]})}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:S.jsxs("div",{className:"space-y-1",children:[(n=e.role,S.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{user:"bg-gray-100 text-gray-800",premium:"bg-yellow-100 text-yellow-800",admin:"bg-red-100 text-red-800"}[n]}`,children:{user:"普通用户",premium:"高级用户",admin:"管理员"}[n]})),(t=e.status,S.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",banned:"bg-red-100 text-red-800"}[t]}`,children:{active:"活跃",inactive:"非活跃",banned:"已禁用"}[t]}))]})}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.translationCount.toLocaleString()}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:d(e.createdAt)}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:d(e.lastLoginAt)}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium",children:S.jsxs("div",{className:"flex justify-end space-x-2",children:[S.jsx("button",{onClick:()=>{console.log("编辑用户功能待实现")},className:"text-primary-600 hover:text-primary-900",children:"编辑"}),S.jsx("button",{onClick:()=>(async()=>{try{c()}catch(e){console.error("切换用户状态失败:",e)}})(),className:""+("active"===e.status?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"),children:"active"===e.status?"禁用":"启用"})]})})]},e.id);var t,n})})]}),S.jsxs("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[S.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[S.jsx("button",{disabled:1===a.page,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"上一页"}),S.jsx("button",{disabled:a.page===i.totalPages,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"下一页"})]}),S.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[S.jsx("div",{children:S.jsxs("p",{className:"text-sm text-gray-700",children:["显示第 ",S.jsx("span",{className:"font-medium",children:(a.page-1)*a.limit+1})," 到"," ",S.jsx("span",{className:"font-medium",children:Math.min(a.page*a.limit,i.total)})," ","条，共 ",S.jsx("span",{className:"font-medium",children:i.total})," 条记录"]})}),S.jsx("div",{children:S.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[S.jsx("button",{disabled:1===a.page,onClick:()=>l({...a,page:a.page-1}),className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"上一页"}),S.jsx("button",{disabled:a.page===i.totalPages,onClick:()=>l({...a,page:a.page+1}),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"下一页"})]})})]})]})]})})]})},Vn=()=>{const[e,t]=s.useState([]),[n,r]=s.useState(!0),[a,l]=s.useState({page:1,limit:20,search:"",sourceLang:"",targetLang:"",startDate:"",endDate:""}),[i,o]=s.useState({total:0,totalPages:0}),c=[{code:"zh",name:"中文"},{code:"en",name:"English"},{code:"ja",name:"日本語"},{code:"ko",name:"한국어"},{code:"fr",name:"Français"},{code:"de",name:"Deutsch"},{code:"es",name:"Español"}];s.useEffect(()=>{u()},[a.page,a.sourceLang,a.targetLang,a.startDate,a.endDate]);const u=async()=>{try{r(!0),setTimeout(()=>{t([{id:"1",originalText:"Hello, how are you?",translatedText:"你好，你好吗？",sourceLang:"en",targetLang:"zh",confidence:.95,user:{id:"1",email:"<EMAIL>",name:"张三"},createdAt:"2025-08-03T10:30:00Z"},{id:"2",originalText:"这是一个测试文本。",translatedText:"This is a test text.",sourceLang:"zh",targetLang:"en",confidence:.92,user:{id:"2",email:"<EMAIL>",name:"李四"},createdAt:"2025-08-03T09:15:00Z"},{id:"3",originalText:"Bonjour le monde",translatedText:"Hello world",sourceLang:"fr",targetLang:"en",confidence:.88,user:{id:"3",email:"<EMAIL>",name:"王五"},createdAt:"2025-08-03T08:45:00Z"}]),o({total:3,totalPages:1}),r(!1)},1e3)}catch(e){console.error("获取翻译记录失败:",e),r(!1)}},d=()=>{l({...a,page:1}),u()},m=e=>{const t=c.find(t=>t.code===e);return t?t.name:e.toUpperCase()},h=e=>{const t=Math.round(100*e);let n="";return n=t>=90?"bg-green-100 text-green-800":t>=70?"bg-yellow-100 text-yellow-800":"bg-red-100 text-red-800",S.jsxs("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${n}`,children:[t,"%"]})},f=(e,t=50)=>e.length<=t?e:e.substring(0,t)+"...";return S.jsxs("div",{children:[S.jsxs("div",{className:"mb-8",children:[S.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"翻译记录管理"}),S.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"查看和管理所有翻译记录"})]}),S.jsxs("div",{className:"bg-white shadow rounded-lg p-6 mb-6",children:[S.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4",children:[S.jsxs("div",{className:"lg:col-span-2",children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"搜索内容"}),S.jsxs("div",{className:"flex",children:[S.jsx("input",{type:"text",className:"flex-1 block w-full px-3 py-2 border border-gray-300 rounded-l-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500",placeholder:"搜索原文或译文",value:a.search,onChange:e=>l({...a,search:e.target.value}),onKeyPress:e=>"Enter"===e.key&&d()}),S.jsx("button",{onClick:d,className:"px-4 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 hover:bg-gray-100",children:S.jsx("svg",{className:"h-5 w-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})})]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"源语言"}),S.jsxs("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:a.sourceLang,onChange:e=>l({...a,sourceLang:e.target.value,page:1}),children:[S.jsx("option",{value:"",children:"全部语言"}),c.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"目标语言"}),S.jsxs("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:a.targetLang,onChange:e=>l({...a,targetLang:e.target.value,page:1}),children:[S.jsx("option",{value:"",children:"全部语言"}),c.map(e=>S.jsx("option",{value:e.code,children:e.name},e.code))]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"开始日期"}),S.jsx("input",{type:"date",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:a.startDate,onChange:e=>l({...a,startDate:e.target.value,page:1})})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"结束日期"}),S.jsx("input",{type:"date",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:a.endDate,onChange:e=>l({...a,endDate:e.target.value,page:1})})]})]}),S.jsx("div",{className:"mt-4 flex justify-end",children:S.jsx("button",{onClick:()=>{l({page:1,limit:20,search:"",sourceLang:"",targetLang:"",startDate:"",endDate:""}),u()},className:"px-4 py-2 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"重置筛选"})})]}),S.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:n?S.jsx("div",{className:"flex justify-center items-center h-64",children:S.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):S.jsxs(S.Fragment,{children:[S.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[S.jsx("thead",{className:"bg-gray-50",children:S.jsxs("tr",{children:[S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"翻译内容"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"语言对"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"置信度"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"用户"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"创建时间"})]})}),S.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>{return S.jsxs("tr",{className:"hover:bg-gray-50",children:[S.jsx("td",{className:"px-6 py-4",children:S.jsxs("div",{className:"space-y-2",children:[S.jsxs("div",{children:[S.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"原文"}),S.jsx("div",{className:"text-sm text-gray-900",title:e.originalText,children:f(e.originalText)})]}),S.jsxs("div",{children:[S.jsx("div",{className:"text-xs text-gray-500 mb-1",children:"译文"}),S.jsx("div",{className:"text-sm text-blue-600",title:e.translatedText,children:f(e.translatedText)})]})]})}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:S.jsx("div",{className:"text-sm text-gray-900",children:S.jsxs("div",{className:"flex items-center space-x-2",children:[S.jsx("span",{className:"text-gray-600",children:m(e.sourceLang)}),S.jsx("svg",{className:"h-4 w-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 8l4 4m0 0l-4 4m4-4H3"})}),S.jsx("span",{className:"text-gray-600",children:m(e.targetLang)})]})})}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:h(e.confidence)}),S.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[S.jsx("div",{className:"text-sm text-gray-900",children:e.user.name||"未知用户"}),S.jsx("div",{className:"text-sm text-gray-500",children:e.user.email})]}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:(t=e.createdAt,new Date(t).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}))})]},e.id);var t})})]}),S.jsxs("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[S.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[S.jsx("button",{disabled:1===a.page,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"上一页"}),S.jsx("button",{disabled:a.page===i.totalPages,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"下一页"})]}),S.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[S.jsx("div",{children:S.jsxs("p",{className:"text-sm text-gray-700",children:["显示第 ",S.jsx("span",{className:"font-medium",children:(a.page-1)*a.limit+1})," 到"," ",S.jsx("span",{className:"font-medium",children:Math.min(a.page*a.limit,i.total)})," ","条，共 ",S.jsx("span",{className:"font-medium",children:i.total})," 条记录"]})}),S.jsx("div",{children:S.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[S.jsx("button",{disabled:1===a.page,onClick:()=>l({...a,page:a.page-1}),className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"上一页"}),S.jsx("button",{disabled:a.page===i.totalPages,onClick:()=>l({...a,page:a.page+1}),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"下一页"})]})})]})]})]})})]})},$n=()=>{const[e,t]=s.useState([]),[n,r]=s.useState(!0),[a,l]=s.useState(!1),[i,o]=s.useState({page:1,limit:20,total:0,totalPages:0}),[c,u]=s.useState({code:"",name:"",description:"",type:"percentage",value:0,minOrderAmount:0,maxDiscountAmount:0,usageLimit:1e3,userUsageLimit:1,validFrom:"",validTo:""});s.useEffect(()=>{d()},[i.page]);const d=async()=>{try{r(!0),setTimeout(()=>{t([{id:"1",code:"WELCOME20",name:"新用户欢迎优惠",description:"新用户注册专享8折优惠",type:"percentage",value:20,minOrderAmount:50,maxDiscountAmount:100,usageLimit:1e3,usedCount:156,userUsageLimit:1,validFrom:"2025-08-01T00:00:00Z",validTo:"2025-12-31T23:59:59Z",status:"active",createdAt:"2025-08-01T10:00:00Z"},{id:"2",code:"SAVE50",name:"限时优惠",description:"满200减50元",type:"fixed_amount",value:50,minOrderAmount:200,maxDiscountAmount:50,usageLimit:500,usedCount:89,userUsageLimit:2,validFrom:"2025-08-01T00:00:00Z",validTo:"2025-08-31T23:59:59Z",status:"active",createdAt:"2025-08-01T12:00:00Z"},{id:"3",code:"EXPIRED10",name:"过期优惠码",description:"已过期的优惠码",type:"percentage",value:10,minOrderAmount:0,maxDiscountAmount:0,usageLimit:100,usedCount:100,userUsageLimit:1,validFrom:"2025-07-01T00:00:00Z",validTo:"2025-07-31T23:59:59Z",status:"expired",createdAt:"2025-07-01T10:00:00Z"}]),o(e=>({...e,total:3,totalPages:1})),r(!1)},1e3)}catch(e){console.error("获取优惠码列表失败:",e),r(!1)}},m=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"short",day:"numeric"}),h=(e,t)=>Math.round(e/t*100);return S.jsxs("div",{children:[S.jsx("div",{className:"mb-8",children:S.jsxs("div",{className:"flex justify-between items-center",children:[S.jsxs("div",{children:[S.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"优惠码管理"}),S.jsx("p",{className:"mt-2 text-sm text-gray-600",children:"管理系统中的优惠码"})]}),S.jsx("button",{onClick:()=>l(!0),className:"bg-primary-600 text-white px-4 py-2 rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500",children:"创建优惠码"})]})}),S.jsx("div",{className:"bg-white shadow overflow-hidden sm:rounded-lg",children:n?S.jsx("div",{className:"flex justify-center items-center h-64",children:S.jsx("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-500"})}):S.jsxs(S.Fragment,{children:[S.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[S.jsx("thead",{className:"bg-gray-50",children:S.jsxs("tr",{children:[S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"优惠码信息"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"类型/状态"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"使用情况"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"有效期"}),S.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"创建时间"})]})}),S.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>{return S.jsxs("tr",{className:"hover:bg-gray-50",children:[S.jsx("td",{className:"px-6 py-4",children:S.jsxs("div",{children:[S.jsx("div",{className:"text-sm font-medium text-gray-900",children:e.name}),S.jsx("div",{className:"text-sm text-primary-600 font-mono",children:e.code}),S.jsx("div",{className:"text-sm text-gray-500 mt-1",children:e.description}),S.jsxs("div",{className:"text-sm text-gray-900 mt-1",children:["percentage"===e.type?`${e.value}% 折扣`:`¥${e.value} 优惠`,e.minOrderAmount>0&&S.jsxs("span",{className:"text-gray-500",children:[" (满¥",e.minOrderAmount,")"]})]})]})}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:S.jsxs("div",{className:"space-y-1",children:[(n=e.type,S.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{percentage:"bg-blue-100 text-blue-800",fixed_amount:"bg-purple-100 text-purple-800"}[n]}`,children:{percentage:"百分比",fixed_amount:"固定金额"}[n]})),(t=e.status,S.jsx("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${{active:"bg-green-100 text-green-800",inactive:"bg-gray-100 text-gray-800",expired:"bg-red-100 text-red-800"}[t]}`,children:{active:"有效",inactive:"无效",expired:"已过期"}[t]}))]})}),S.jsxs("td",{className:"px-6 py-4 whitespace-nowrap",children:[S.jsxs("div",{className:"text-sm text-gray-900",children:[e.usedCount," / ",e.usageLimit]}),S.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-1",children:S.jsx("div",{className:"bg-primary-600 h-2 rounded-full",style:{width:`${h(e.usedCount,e.usageLimit)}%`}})}),S.jsxs("div",{className:"text-xs text-gray-500 mt-1",children:[h(e.usedCount,e.usageLimit),"% 已使用"]})]}),S.jsxs("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:[S.jsx("div",{children:m(e.validFrom)}),S.jsx("div",{children:"到"}),S.jsx("div",{children:m(e.validTo)})]}),S.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:m(e.createdAt)})]},e.id);var t,n})})]}),S.jsxs("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[S.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[S.jsx("button",{disabled:1===i.page,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"上一页"}),S.jsx("button",{disabled:i.page===i.totalPages,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50",children:"下一页"})]}),S.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[S.jsx("div",{children:S.jsxs("p",{className:"text-sm text-gray-700",children:["显示第 ",S.jsx("span",{className:"font-medium",children:(i.page-1)*i.limit+1})," 到"," ",S.jsx("span",{className:"font-medium",children:Math.min(i.page*i.limit,i.total)})," ","条，共 ",S.jsx("span",{className:"font-medium",children:i.total})," 条记录"]})}),S.jsx("div",{children:S.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px",children:[S.jsx("button",{disabled:1===i.page,onClick:()=>o({...i,page:i.page-1}),className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"上一页"}),S.jsx("button",{disabled:i.page===i.totalPages,onClick:()=>o({...i,page:i.page+1}),className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50",children:"下一页"})]})})]})]})]})}),a&&S.jsx("div",{className:"fixed inset-0 z-50 overflow-y-auto",children:S.jsxs("div",{className:"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0",children:[S.jsx("div",{className:"fixed inset-0 transition-opacity",onClick:()=>l(!1),children:S.jsx("div",{className:"absolute inset-0 bg-gray-500 opacity-75"})}),S.jsx("div",{className:"inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full",children:S.jsxs("form",{onSubmit:async e=>{e.preventDefault();try{console.log("创建优惠码:",c),l(!1),u({code:"",name:"",description:"",type:"percentage",value:0,minOrderAmount:0,maxDiscountAmount:0,usageLimit:1e3,userUsageLimit:1,validFrom:"",validTo:""}),d()}catch(t){console.error("创建优惠码失败:",t)}},children:[S.jsx("div",{className:"bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4",children:S.jsxs("div",{className:"mb-4",children:[S.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"创建优惠码"}),S.jsxs("div",{className:"grid grid-cols-1 gap-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"优惠码"}),S.jsx("input",{type:"text",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.code,onChange:e=>u({...c,code:e.target.value.toUpperCase()}),placeholder:"例如: WELCOME20"})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"优惠码名称"}),S.jsx("input",{type:"text",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.name,onChange:e=>u({...c,name:e.target.value}),placeholder:"例如: 新用户欢迎优惠"})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"描述"}),S.jsx("textarea",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",rows:3,value:c.description,onChange:e=>u({...c,description:e.target.value}),placeholder:"优惠码描述"})]}),S.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"优惠类型"}),S.jsxs("select",{className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.type,onChange:e=>u({...c,type:e.target.value}),children:[S.jsx("option",{value:"percentage",children:"百分比"}),S.jsx("option",{value:"fixed_amount",children:"固定金额"})]})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"percentage"===c.type?"折扣百分比":"优惠金额"}),S.jsx("input",{type:"number",required:!0,min:"0",max:"percentage"===c.type?"100":void 0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.value,onChange:e=>u({...c,value:parseFloat(e.target.value)||0}),placeholder:"percentage"===c.type?"20":"50"})]})]}),S.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最小消费金额"}),S.jsx("input",{type:"number",min:"0",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.minOrderAmount,onChange:e=>u({...c,minOrderAmount:parseFloat(e.target.value)||0}),placeholder:"0"})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"最大优惠金额"}),S.jsx("input",{type:"number",min:"0",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.maxDiscountAmount,onChange:e=>u({...c,maxDiscountAmount:parseFloat(e.target.value)||0}),placeholder:"0"})]})]}),S.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"总使用次数限制"}),S.jsx("input",{type:"number",required:!0,min:"1",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.usageLimit,onChange:e=>u({...c,usageLimit:parseInt(e.target.value)||1}),placeholder:"1000"})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"每用户使用次数限制"}),S.jsx("input",{type:"number",required:!0,min:"1",className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.userUsageLimit,onChange:e=>u({...c,userUsageLimit:parseInt(e.target.value)||1}),placeholder:"1"})]})]}),S.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"开始时间"}),S.jsx("input",{type:"datetime-local",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.validFrom,onChange:e=>u({...c,validFrom:e.target.value})})]}),S.jsxs("div",{children:[S.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"结束时间"}),S.jsx("input",{type:"datetime-local",required:!0,className:"block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500",value:c.validTo,onChange:e=>u({...c,validTo:e.target.value})})]})]})]})]})}),S.jsxs("div",{className:"bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse",children:[S.jsx("button",{type:"submit",className:"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary-600 text-base font-medium text-white hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:ml-3 sm:w-auto sm:text-sm",children:"创建"}),S.jsx("button",{type:"button",onClick:()=>l(!1),className:"mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm",children:"取消"})]})]})})]})})]})},Qn=()=>S.jsx("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 flex items-center justify-center px-4",children:S.jsxs("div",{className:"max-w-md w-full text-center",children:[S.jsx("div",{className:"flex justify-center mb-8",children:S.jsx(it,{size:"xl"})}),S.jsxs("div",{className:"mb-8",children:[S.jsx("div",{className:"text-8xl font-bold text-primary-200 mb-4",children:"404"}),S.jsx("div",{className:"w-24 h-1 bg-primary-300 mx-auto rounded-full"})]}),S.jsxs("div",{className:"mb-8",children:[S.jsx("h1",{className:"text-2xl font-bold text-secondary-900 mb-2",children:"页面未找到"}),S.jsx("p",{className:"text-secondary-600",children:"抱歉，您访问的页面不存在或已被移动。"})]}),S.jsxs("div",{className:"space-y-4",children:[S.jsx(m,{to:"/",className:"btn-primary w-full",children:"返回首页"}),S.jsx(m,{to:"/login",className:"btn-secondary w-full",children:"前往登录"})]}),S.jsxs("div",{className:"mt-8 p-4 bg-white rounded-xl shadow-soft border border-secondary-100",children:[S.jsx("h3",{className:"font-semibold text-secondary-900 mb-2",children:"需要帮助？"}),S.jsx("p",{className:"text-sm text-secondary-600 mb-3",children:"如果您认为这是一个错误，请联系我们的支持团队。"}),S.jsx(m,{to:"/help",className:"text-primary-600 hover:text-primary-700 text-sm font-medium",children:"联系支持 →"})]})]})}),Kn=()=>S.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:S.jsxs("div",{className:"text-center",children:[S.jsx("div",{className:"w-8 h-8 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),S.jsx("p",{className:"text-gray-600",children:"加载中..."})]})});function Yn(){const{isAuthenticated:e}=De(e=>e.auth);return s.useEffect(()=>{window.addEventListener("unhandledrejection",e=>{const t=lt.parseError(e.reason);lt.logError(t,"unhandledrejection"),e.preventDefault()}),window.addEventListener("error",e=>{const t=lt.parseError(e.error);lt.logError(t,"uncaught_error")})},[]),S.jsx(Be,{children:S.jsx(h,{children:S.jsx(s.Suspense,{fallback:S.jsx(Kn,{}),children:S.jsxs(f,{children:[S.jsx(p,{path:"/login",element:S.jsx(ot,{})}),S.jsx(p,{path:"/",element:e?S.jsx(An,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/translate",element:e?S.jsx(On,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/history",element:e?S.jsx(Mn,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/profile",element:e?S.jsx(Rn,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/settings",element:e?S.jsx(Dn,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/subscription",element:e?S.jsx(Un,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/help",element:S.jsx(Wn,{})}),S.jsx(p,{path:"/admin",element:e?S.jsx(qn,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/admin/users",element:e?S.jsx(Hn,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/admin/translations",element:e?S.jsx(Vn,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"/admin/coupons",element:e?S.jsx($n,{}):S.jsx(g,{to:"/login",replace:!0})}),S.jsx(p,{path:"*",element:S.jsx(Qn,{})})]})})})})}z.createRoot(document.getElementById("root")).render(S.jsx(s.StrictMode,{children:S.jsx(l,{store:Me,children:S.jsx(Fe,{children:S.jsx(Yn,{})})})}));
//# sourceMappingURL=index-c7T1_3zn.js.map
