import React, { useState, useRef, useEffect } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import {
  setSourceText,
  setSourceLanguage,
  setTargetLanguage,
  translateText,
  clearTranslationError
} from '../store/slices/translationSlice';
import LanguageSelector from './LanguageSelector';
import { usePerformanceMonitor, useDebounce, useThrottle } from '../utils/performance';

export const TranslationArea: React.FC = () => {
  const dispatch = useAppDispatch();
  const { currentTranslation } = useAppSelector((state) => state.translation);
  const [isSwapping, setIsSwapping] = useState(false);
  const sourceTextRef = useRef<HTMLTextAreaElement>(null);

  
  // 性能监控
  const { startOperation, endOperation } = usePerformanceMonitor('TranslationArea');
  
  // 防抖优化文本输入
  const debouncedSourceText = useDebounce(currentTranslation.sourceText, 300);
  
  // 节流优化字符计数
  const throttledCharCount = useThrottle(currentTranslation.sourceText.length, 100);

  const handleSourceTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    dispatch(setSourceText(e.target.value));
    if (currentTranslation.error) {
      dispatch(clearTranslationError());
    }
  };

  const handleTranslate = () => {
    if (!currentTranslation.sourceText.trim()) {
      return;
    }

    startOperation('translation_request');
    dispatch(translateText({
      sourceText: currentTranslation.sourceText,
      sourceLanguage: currentTranslation.sourceLanguage,
      targetLanguage: currentTranslation.targetLanguage,
    }));
  };

  const handleSwapLanguages = () => {
    if (currentTranslation.sourceLanguage === 'auto') {
      return;
    }

    setIsSwapping(true);
    const oldSource = currentTranslation.sourceLanguage;
    const oldTarget = currentTranslation.targetLanguage;

    const oldTranslatedText = currentTranslation.translatedText;

    dispatch(setSourceLanguage(oldTarget));
    dispatch(setTargetLanguage(oldSource));
    dispatch(setSourceText(oldTranslatedText));

    setTimeout(() => setIsSwapping(false), 300);
  };

  const handleClearText = () => {
    dispatch(setSourceText(''));
    if (currentTranslation.error) {
      dispatch(clearTranslationError());
    }
  };

  const handleCopySourceText = async () => {
    try {
      await navigator.clipboard.writeText(currentTranslation.sourceText);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const handleCopyTranslatedText = async () => {
    try {
      await navigator.clipboard.writeText(currentTranslation.translatedText);
    } catch (error) {
      console.error('Failed to copy text:', error);
    }
  };

  const getCharacterCount = () => {
    return throttledCharCount;
  };

  const getWordCount = React.useCallback(() => {
    const text = debouncedSourceText.trim();
    if (!text) return 0;
    return text.split(/\s+/).length;
  }, [debouncedSourceText]);

  useEffect(() => {
    if (sourceTextRef.current) {
      sourceTextRef.current.style.height = 'auto';
      sourceTextRef.current.style.height = `${Math.max(120, sourceTextRef.current.scrollHeight)}px`;
    }
  }, [currentTranslation.sourceText]);

  // 监控翻译完成
  useEffect(() => {
    if (!currentTranslation.isTranslating && currentTranslation.translatedText) {
      endOperation('translation_request');
    }
  }, [currentTranslation.isTranslating, currentTranslation.translatedText, endOperation]);

  return (
    <div className="w-full max-w-6xl mx-auto p-6">
      {/* 语言选择器和交换按钮 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              源语言
            </label>
            <LanguageSelector
              type="source"
              value={currentTranslation.sourceLanguage}
              onChange={(language) => dispatch(setSourceLanguage(language))}
              disabled={currentTranslation.isTranslating}
            />
          </div>

          <button
            onClick={handleSwapLanguages}
            disabled={currentTranslation.sourceLanguage === 'auto' || currentTranslation.isTranslating}
            className={`
              mt-6 p-2 rounded-full transition-all duration-300
              ${currentTranslation.sourceLanguage === 'auto' || currentTranslation.isTranslating
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-blue-600 hover:text-blue-700 hover:bg-blue-50'
              }
              ${isSwapping ? 'transform rotate-180' : ''}
            `}
            title="交换语言"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
          </button>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              目标语言
            </label>
            <LanguageSelector
              type="target"
              value={currentTranslation.targetLanguage}
              onChange={(language) => dispatch(setTargetLanguage(language))}
              disabled={currentTranslation.isTranslating}
            />
          </div>
        </div>
      </div>

      {/* 翻译区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* 源文本输入区域 */}
        <div className="bg-white rounded-lg border border-gray-300 shadow-sm">
          <div className="flex items-center justify-between p-3 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-700">
              输入文本
            </h3>
            <div className="flex items-center space-x-2">
              {currentTranslation.sourceText && (
                <button
                  onClick={handleCopySourceText}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title="复制文本"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              )}
              {currentTranslation.sourceText && (
                <button
                  onClick={handleClearText}
                  className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                  title="清空文本"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          <div className="p-4">
            <textarea
              ref={sourceTextRef}
              value={currentTranslation.sourceText}
              onChange={handleSourceTextChange}
              placeholder="请输入要翻译的文本..."
              disabled={currentTranslation.isTranslating}
              className="w-full min-h-[120px] p-3 text-sm border-0 resize-none focus:outline-none placeholder-gray-400"
              style={{ maxHeight: '400px' }}
            />

            <div className="flex items-center justify-between mt-3 pt-3 border-t border-gray-100">
              <div className="text-xs text-gray-500">
                {getCharacterCount()} 字符 | {getWordCount()} 单词
              </div>
              <button
                onClick={handleTranslate}
                disabled={!currentTranslation.sourceText.trim() || currentTranslation.isTranslating}
                className={`
                  px-4 py-2 text-sm font-medium rounded-md transition-colors
                  ${!currentTranslation.sourceText.trim() || currentTranslation.isTranslating
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-blue-600 text-white hover:bg-blue-700'
                  }
                `}
              >
                {currentTranslation.isTranslating ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    <span>翻译中...</span>
                  </div>
                ) : (
                  '翻译'
                )}
              </button>
            </div>

            {/* 翻译进度条 */}
            {currentTranslation.isTranslating && (
              <div className="mt-3">
                <div className="bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${currentTranslation.progress}%` }}
                  ></div>
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  进度: {currentTranslation.progress}%
                </div>
              </div>
            )}

            {/* 错误提示 */}
            {currentTranslation.error && (
              <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
                <div className="flex items-center">
                  <svg className="w-4 h-4 text-red-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span className="text-sm text-red-700">{currentTranslation.error}</span>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 翻译结果区域 */}
        <div className="bg-white rounded-lg border border-gray-300 shadow-sm">
          <div className="flex items-center justify-between p-3 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-700">
              翻译结果
            </h3>
            <div className="flex items-center space-x-2">
              {currentTranslation.translatedText && (
                <button
                  onClick={handleCopyTranslatedText}
                  className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                  title="复制翻译结果"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          <div className="p-4">
            {currentTranslation.isTranslating ? (
              <div className="flex items-center justify-center min-h-[120px]">
                <div className="text-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                  <div className="text-sm text-gray-500">正在翻译中...</div>
                </div>
              </div>
            ) : currentTranslation.translatedText ? (
              <div className="min-h-[120px]">
                <div className="p-3 text-sm text-gray-900 whitespace-pre-wrap">
                  {currentTranslation.translatedText}
                </div>
              </div>
            ) : (
              <div className="flex items-center justify-center min-h-[120px]">
                <div className="text-center text-gray-400">
                  <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M7 8h10m0 0V6a2 2 0 00-2-2H9a2 2 0 00-2 2v2m10 0v10a2 2 0 01-2 2H9a2 2 0 01-2-2V8m10 0H7" />
                  </svg>
                  <div className="text-sm">翻译结果将显示在这里</div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default TranslationArea;