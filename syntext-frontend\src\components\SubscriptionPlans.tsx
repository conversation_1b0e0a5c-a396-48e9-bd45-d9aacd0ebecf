import React, { useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import {
  fetchSubscriptionPlans,
  setSelectedPlan,
  setSelectedBillingCycle,
  type SubscriptionPlan,
} from '../store/slices/subscriptionSlice';

interface SubscriptionPlansProps {
  onPlanSelect?: (plan: SubscriptionPlan, billingCycle: 'monthly' | 'yearly') => void;
}

export const SubscriptionPlans: React.FC<SubscriptionPlansProps> = ({
  onPlanSelect,
}) => {
  const dispatch = useAppDispatch();
  const { plans, selectedPlan, selectedBillingCycle } = useAppSelector((state) => state.subscription);
  const [hoveredPlan, setHoveredPlan] = useState<string | null>(null);

  useEffect(() => {
    if (plans.list.length === 0 && !plans.loading) {
      dispatch(fetchSubscriptionPlans());
    }
  }, [dispatch, plans.list.length, plans.loading]);

  const handleBillingCycleChange = (cycle: 'monthly' | 'yearly') => {
    dispatch(setSelectedBillingCycle(cycle));
  };

  const handlePlanSelect = (plan: SubscriptionPlan) => {
    dispatch(setSelectedPlan(plan));
    onPlanSelect?.(plan, selectedBillingCycle);
  };

  const getPrice = (plan: SubscriptionPlan) => {
    return selectedBillingCycle === 'yearly' ? plan.yearlyPrice : plan.monthlyPrice;
  };

  const getMonthlyEquivalent = (plan: SubscriptionPlan) => {
    if (selectedBillingCycle === 'monthly') return plan.monthlyPrice;
    return Math.round((plan.yearlyPrice / 12) * 100) / 100;
  };

  const getSavings = (plan: SubscriptionPlan) => {
    if (selectedBillingCycle === 'monthly' || plan.monthlyPrice === 0) return 0;
    const yearlyMonthly = plan.monthlyPrice * 12;
    return Math.round(((yearlyMonthly - plan.yearlyPrice) / yearlyMonthly) * 100);
  };

  const getPlanIcon = (planType: string) => {
    const icons = {
      free: '🎯',
      basic: '⭐',
      premium: '💎',
      enterprise: '🚀',
    };
    return icons[planType as keyof typeof icons] || '📦';
  };

  const getPlanColor = (planType: string) => {
    const colors = {
      free: 'border-gray-200 bg-white',
      basic: 'border-blue-200 bg-blue-50',
      premium: 'border-purple-200 bg-purple-50',
      enterprise: 'border-yellow-200 bg-yellow-50',
    };
    return colors[planType as keyof typeof colors] || 'border-gray-200 bg-white';
  };

  const getButtonColor = (planType: string) => {
    const colors = {
      free: 'bg-gray-600 hover:bg-gray-700',
      basic: 'bg-blue-600 hover:bg-blue-700',
      premium: 'bg-purple-600 hover:bg-purple-700',
      enterprise: 'bg-yellow-600 hover:bg-yellow-700',
    };
    return colors[planType as keyof typeof colors] || 'bg-gray-600 hover:bg-gray-700';
  };

  if (plans.loading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <div className="text-sm text-gray-500">加载订阅套餐中...</div>
        </div>
      </div>
    );
  }

  if (plans.error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-600 mb-4">加载订阅套餐失败</div>
        <div className="text-sm text-gray-500">{plans.error}</div>
        <button
          onClick={() => dispatch(fetchSubscriptionPlans())}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          重试
        </button>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      {/* 计费周期选择 */}
      <div className="text-center mb-8">
        <div className="inline-flex items-center bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => handleBillingCycleChange('monthly')}
            className={`
              px-4 py-2 text-sm font-medium rounded-md transition-colors
              ${selectedBillingCycle === 'monthly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
              }
            `}
          >
            按月付费
          </button>
          <button
            onClick={() => handleBillingCycleChange('yearly')}
            className={`
              px-4 py-2 text-sm font-medium rounded-md transition-colors relative
              ${selectedBillingCycle === 'yearly'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
              }
            `}
          >
            按年付费
            <span className="absolute -top-2 -right-2 bg-green-500 text-white text-xs px-1.5 py-0.5 rounded">
              省钱
            </span>
          </button>
        </div>
      </div>

      {/* 套餐卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {plans.list.map((plan) => {
          const isSelected = selectedPlan?.id === plan.id;
          const savings = getSavings(plan);
          const monthlyEquivalent = getMonthlyEquivalent(plan);
          
          return (
            <div
              key={plan.id}
              className={`
                relative rounded-lg border-2 p-6 cursor-pointer transition-all duration-200
                ${isSelected 
                  ? 'border-blue-500 shadow-lg scale-105' 
                  : getPlanColor(plan.type)
                }
                ${hoveredPlan === plan.id ? 'shadow-md scale-102' : ''}
              `}
              onMouseEnter={() => setHoveredPlan(plan.id)}
              onMouseLeave={() => setHoveredPlan(null)}
              onClick={() => handlePlanSelect(plan)}
            >
              {/* 推荐标签 */}
              {plan.type === 'premium' && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <span className="bg-purple-600 text-white px-3 py-1 text-xs font-medium rounded-full">
                    推荐
                  </span>
                </div>
              )}

              {/* 省钱标签 */}
              {savings > 0 && selectedBillingCycle === 'yearly' && (
                <div className="absolute -top-3 -right-3">
                  <span className="bg-green-500 text-white px-2 py-1 text-xs font-medium rounded-full">
                    省{savings}%
                  </span>
                </div>
              )}

              <div className="text-center">
                {/* 图标和名称 */}
                <div className="mb-4">
                  <div className="text-3xl mb-2">{getPlanIcon(plan.type)}</div>
                  <h3 className="text-lg font-semibold text-gray-900">{plan.name}</h3>
                  <p className="text-sm text-gray-600 mt-1">{plan.description}</p>
                </div>

                {/* 价格 */}
                <div className="mb-6">
                  {plan.type === 'free' ? (
                    <div className="text-2xl font-bold text-gray-900">免费</div>
                  ) : (
                    <div>
                      <div className="text-3xl font-bold text-gray-900">
                        ¥{getPrice(plan)}
                        <span className="text-sm font-normal text-gray-600">
                          /{selectedBillingCycle === 'yearly' ? '年' : '月'}
                        </span>
                      </div>
                      {selectedBillingCycle === 'yearly' && monthlyEquivalent !== plan.monthlyPrice && (
                        <div className="text-sm text-gray-500">
                          相当于 ¥{monthlyEquivalent}/月
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {/* 功能特性 */}
                <div className="space-y-3 mb-6 text-left">
                  <div className="flex items-center text-sm">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>
                      {plan.monthlyCharacterLimit === -1 
                        ? '无限字符' 
                        : `${plan.monthlyCharacterLimit.toLocaleString()} 字符/月`
                      }
                    </span>
                  </div>
                  
                  <div className="flex items-center text-sm">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>
                      {plan.maxTranslationsPerDay === -1 
                        ? '无限翻译次数' 
                        : `${plan.maxTranslationsPerDay} 次翻译/天`
                      }
                    </span>
                  </div>

                  <div className="flex items-center text-sm">
                    <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    <span>{plan.maxConcurrentTranslations} 个并发翻译</span>
                  </div>

                  {plan.supportsBatchTranslation && (
                    <div className="flex items-center text-sm">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>批量翻译</span>
                    </div>
                  )}

                  {plan.prioritySupport && (
                    <div className="flex items-center text-sm">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>优先技术支持</span>
                    </div>
                  )}

                  {plan.apiAccess && (
                    <div className="flex items-center text-sm">
                      <svg className="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span>API 访问</span>
                    </div>
                  )}
                </div>

                {/* 选择按钮 */}
                <button
                  className={`
                    w-full py-2 px-4 rounded-lg text-white font-medium transition-colors
                    ${isSelected 
                      ? 'bg-blue-600 hover:bg-blue-700' 
                      : getButtonColor(plan.type)
                    }
                  `}
                >
                  {plan.type === 'free' ? '当前套餐' : '选择套餐'}
                </button>
              </div>
            </div>
          );
        })}
      </div>

      {/* 底部说明 */}
      <div className="mt-8 text-center text-sm text-gray-600">
        <p>所有套餐都包含基础翻译功能和50+种语言支持</p>
        <p className="mt-1">年付用户享受优惠价格，随时可以取消订阅</p>
      </div>
    </div>
  );
};

export default SubscriptionPlans;