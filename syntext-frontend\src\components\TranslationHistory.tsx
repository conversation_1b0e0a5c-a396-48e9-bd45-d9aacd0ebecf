import React, { useState, useEffect, useMemo } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import {
  fetchTranslationHistory,
  fetchFavorites,
  toggleFavorite,
  setSourceText,
  setSourceLanguage,
  setTargetLanguage
} from '../store/slices/translationSlice';
import type { Translation } from '../store/slices/translationSlice';
import VirtualizedList from './VirtualizedList';
import { usePerformanceMonitor, useDebounce } from '../utils/performance';

interface TranslationHistoryProps {
  onSelectTranslation?: (translation: Translation) => void;
}

export const TranslationHistory: React.FC<TranslationHistoryProps> = ({
  onSelectTranslation
}) => {
  const dispatch = useAppDispatch();
  const { history, favorites } = useAppSelector((state) => state.translation);
  const [activeTab, setActiveTab] = useState<'history' | 'favorites'>('history');
  const [searchTerm, setSearchTerm] = useState('');
  
  // 性能监控
  const { startOperation, endOperation } = usePerformanceMonitor('TranslationHistory');
  
  // 防抖搜索
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    if (activeTab === 'history' && history.translations.length === 0) {
      dispatch(fetchTranslationHistory({ page: 1, limit: 20 }));
    } else if (activeTab === 'favorites' && favorites.length === 0) {
      dispatch(fetchFavorites());
    }
  }, [dispatch, activeTab, history.translations.length, favorites.length]);

  const handleToggleFavorite = (translationId: string) => {
    dispatch(toggleFavorite(translationId));
  };

  const handleLoadMore = () => {
    if (!history.loading && history.translations.length < history.total) {
      const nextPage = Math.ceil(history.translations.length / history.limit) + 1;
      dispatch(fetchTranslationHistory({ page: nextPage, limit: history.limit }));
    }
  };

  const handleSelectTranslation = (translation: Translation) => {
    dispatch(setSourceText(translation.sourceText));
    dispatch(setSourceLanguage(translation.sourceLanguage));
    dispatch(setTargetLanguage(translation.targetLanguage));
    onSelectTranslation?.(translation);
  };

  // 使用 useMemo 优化搜索性能
  const currentTranslations = useMemo(() => {
    startOperation('search_filter');
    
    const translations = activeTab === 'history' ? history.translations : favorites;
    
    if (!debouncedSearchTerm) {
      endOperation('search_filter');
      return translations;
    }
    
    const filtered = translations.filter(translation =>
      translation.sourceText.toLowerCase().includes(debouncedSearchTerm.toLowerCase()) ||
      translation.translatedText.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
    );
    
    endOperation('search_filter');
    return filtered;
  }, [activeTab, history.translations, favorites, debouncedSearchTerm, startOperation, endOperation]);

  const getLanguageIcon = (languageCode: string) => {
    const icons: { [key: string]: string } = {
      'zh': '🇨🇳',
      'en': '🇺🇸',
      'ja': '🇯🇵',
      'ko': '🇰🇷',
      'fr': '🇫🇷',
      'de': '🇩🇪',
      'es': '🇪🇸',
      'it': '🇮🇹',
      'pt': '🇵🇹',
      'ru': '🇷🇺',
      'ar': '🇸🇦',
      'hi': '🇮🇳',
      'th': '🇹🇭',
      'vi': '🇻🇳',
      'tr': '🇹🇷',
    };
    return icons[languageCode] || '🌐';
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return '刚刚';
    } else if (diffInHours < 24) {
      return `${diffInHours}小时前`;
    } else if (diffInHours < 24 * 7) {
      const days = Math.floor(diffInHours / 24);
      return `${days}天前`;
    } else {
      return date.toLocaleDateString('zh-CN');
    }
  };

  const getQualityColor = (quality: number) => {
    if (quality >= 90) return 'text-green-600 bg-green-100';
    if (quality >= 80) return 'text-blue-600 bg-blue-100';
    if (quality >= 70) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  // 渲染单个翻译项目
  const renderTranslationItem = (translation: Translation) => (
    <div
      className="p-4 hover:bg-gray-50 transition-colors cursor-pointer border-b border-gray-200"
      onClick={() => handleSelectTranslation(translation)}
    >
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          {/* 语言信息和质量评分 */}
          <div className="flex items-center space-x-3 mb-2">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              <span className="text-lg">{getLanguageIcon(translation.sourceLanguage)}</span>
              <span>{translation.sourceLanguage.toUpperCase()}</span>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
              <span className="text-lg">{getLanguageIcon(translation.targetLanguage)}</span>
              <span>{translation.targetLanguage.toUpperCase()}</span>
            </div>

            <div className={`
              px-2 py-1 text-xs font-medium rounded-full
              ${getQualityColor(translation.quality)}
            `}>
              {translation.quality}%
            </div>

            <div className="text-xs text-gray-500">
              {translation.characterCount} 字符 | {translation.processingTime}ms
            </div>
          </div>

          {/* 翻译内容 */}
          <div className="space-y-2">
            <div className="text-sm text-gray-900">
              <span className="font-medium text-gray-700">原文：</span>
              <span className="line-clamp-2">{translation.sourceText}</span>
            </div>
            <div className="text-sm text-gray-700">
              <span className="font-medium text-gray-700">译文：</span>
              <span className="line-clamp-2">{translation.translatedText}</span>
            </div>
          </div>

          {/* 时间信息 */}
          <div className="mt-2 text-xs text-gray-500">
            {formatDate(translation.createdAt)}
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="flex items-center space-x-2 ml-4">
          <button
            onClick={(e) => {
              e.stopPropagation();
              handleToggleFavorite(translation.id);
            }}
            className={`
              p-2 rounded-full transition-colors
              ${translation.isFavorite
                ? 'text-red-500 hover:text-red-600 hover:bg-red-50'
                : 'text-gray-400 hover:text-red-500 hover:bg-red-50'
              }
            `}
            title={translation.isFavorite ? '取消收藏' : '添加收藏'}
          >
            <svg className="w-4 h-4" fill={translation.isFavorite ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
            </svg>
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation();
              navigator.clipboard.writeText(translation.translatedText);
            }}
            className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-colors"
            title="复制译文"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="w-full max-w-4xl mx-auto bg-white rounded-lg shadow-sm border border-gray-200">
      {/* 头部标签和搜索 */}
      <div className="border-b border-gray-200">
        <div className="flex items-center justify-between p-4">
          <div className="flex space-x-1">
            <button
              onClick={() => setActiveTab('history')}
              className={`
                px-4 py-2 text-sm font-medium rounded-md transition-colors
                ${activeTab === 'history'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }
              `}
            >
              翻译历史 ({history.total})
            </button>
            <button
              onClick={() => setActiveTab('favorites')}
              className={`
                px-4 py-2 text-sm font-medium rounded-md transition-colors
                ${activeTab === 'favorites'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                }
              `}
            >
              收藏夹 ({favorites.length})
            </button>
          </div>

          <div className="relative">
            <input
              type="text"
              placeholder="搜索翻译记录..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-64 pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <svg
              className="absolute left-3 top-2.5 w-4 h-4 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      {/* 翻译记录列表 */}
      <div>
        {history.loading && currentTranslations.length === 0 ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <div className="text-sm text-gray-500">加载中...</div>
            </div>
          </div>
        ) : currentTranslations.length === 0 ? (
          <div className="flex items-center justify-center py-12">
            <div className="text-center text-gray-500">
              <svg className="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <div className="text-sm">
                {debouncedSearchTerm ? '未找到匹配的翻译记录' : 
                 activeTab === 'history' ? '暂无翻译历史' : '暂无收藏翻译'}
              </div>
            </div>
          </div>
        ) : (
          <>
            {/* 使用虚拟化列表渲染大量数据 */}
            <VirtualizedList
              items={currentTranslations}
              renderItem={renderTranslationItem}
              itemHeight={180} // 估算每个项目的高度
              containerHeight={600} // 容器高度
              className="border-gray-200"
              onEndReached={handleLoadMore}
              onEndReachedThreshold={0.8}
            />

            {/* 加载更多指示器 */}
            {activeTab === 'history' && history.translations.length < history.total && (
              <div className="p-4 text-center border-t border-gray-200">
                {history.loading ? (
                  <div className="flex items-center justify-center space-x-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                    <span className="text-sm text-gray-500">加载更多数据...</span>
                  </div>
                ) : (
                  <div className="text-sm text-gray-500">
                    已加载 {history.translations.length}/{history.total} 条记录
                  </div>
                )}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default TranslationHistory;