import axios from 'axios';
import { store } from '../store';
import { refreshToken, resetAuth, setTokens } from '../store/slices/authSlice';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:6000/api/v1';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
let isRefreshing = false;
let failedQueue: Array<{ resolve: Function; reject: Function }> = [];

const processQueue = (error: any, token: string | null = null) => {
  failedQueue.forEach(({ resolve, reject }) => {
    if (error) {
      reject(error);
    } else {
      resolve(token);
    }
  });
  
  failedQueue = [];
};

api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;
    
    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        // 如果已经在刷新，将请求放入队列
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        }).then(() => {
          return api(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }
      
      originalRequest._retry = true;
      isRefreshing = true;
      
      try {
        const resultAction = await store.dispatch(refreshToken());
        
        if (refreshToken.fulfilled.match(resultAction)) {
          const { token: newToken, refreshToken: newRefreshToken } = resultAction.payload;
          store.dispatch(setTokens({ token: newToken, refreshToken: newRefreshToken }));
          
          // 更新原始请求的token
          originalRequest.headers.Authorization = `Bearer ${newToken}`;
          
          processQueue(null, newToken);
          return api(originalRequest);
        } else {
          // 刷新失败，清除状态并跳转到登录页
          processQueue(error, null);
          store.dispatch(resetAuth());
          window.location.href = '/login';
          return Promise.reject(error);
        }
      } catch (refreshError) {
        processQueue(refreshError, null);
        store.dispatch(resetAuth());
        window.location.href = '/login';
        return Promise.reject(refreshError);
      } finally {
        isRefreshing = false;
      }
    }
    
    return Promise.reject(error);
  }
);

// Translation API methods
export const translationApi = {
  translate: (data: {
    sourceText: string;
    sourceLanguage: string;
    targetLanguage: string;
    context?: string;
  }) => api.post('/translation/translate', data),

  batchTranslate: (data: {
    translations: Array<{
      sourceText: string;
      sourceLanguage: string;
      targetLanguage: string;
      context?: string;
    }>;
  }) => api.post('/translation/batch', data),

  getHistory: (page = 1, limit = 20) =>
    api.get(`/translation/history?page=${page}&limit=${limit}`),

  getFavorites: () => api.get('/translation/favorites'),

  toggleFavorite: (translationId: string) =>
    api.post(`/translation/${translationId}/favorite`),

  getLanguages: () => api.get('/translation/languages'),
};

// Auth API methods
export const authApi = {
  sendCode: (email: string) => api.post('/auth/send-code', { email }),
  verifyCode: (email: string, code: string) =>
    api.post('/auth/verify-code', { email, code }),
  refresh: (refreshToken: string) =>
    api.post('/auth/refresh', { refreshToken }),
  logout: () => api.post('/auth/logout'),
  getProfile: () => api.post('/auth/me'),
};

// User API methods
export const userApi = {
  getProfile: () => api.get('/user/profile'),
  updateProfile: (data: any) => api.put('/user/profile', data),
  getStats: () => api.get('/user/stats'),
  getSubscription: () => api.get('/user/subscription'),
};

// Payment API methods
export const paymentApi = {
  getPlans: () => api.get('/payment/plans'),
  createPayment: (data: any) => api.post('/payment/create', data),
  validateCoupon: (code: string) => api.post('/payment/validate-coupon', { code }),
};

// Admin API methods
export const adminApi = {
  // Dashboard stats
  getDashboardStats: () => api.get('/admin/dashboard/stats'),

  // User management
  getUsers: (page = 1, limit = 20, search?: string) =>
    api.get(`/admin/users?page=${page}&limit=${limit}${search ? `&search=${search}` : ''}`),
  getUserById: (id: string) => api.get(`/admin/users/${id}`),
  updateUser: (id: string, data: any) => api.put(`/admin/users/${id}`, data),
  deleteUser: (id: string) => api.delete(`/admin/users/${id}`),

  // Translation management
  getTranslations: (page = 1, limit = 20, filters?: any) =>
    api.get(`/admin/translations?page=${page}&limit=${limit}`, { params: filters }),
  getTranslationById: (id: string) => api.get(`/admin/translations/${id}`),
  deleteTranslation: (id: string) => api.delete(`/admin/translations/${id}`),

  // Coupon management
  getCoupons: (page = 1, limit = 20) =>
    api.get(`/admin/coupons?page=${page}&limit=${limit}`),
  createCoupon: (data: any) => api.post('/admin/coupons', data),
  updateCoupon: (id: string, data: any) => api.put(`/admin/coupons/${id}`, data),
  deleteCoupon: (id: string) => api.delete(`/admin/coupons/${id}`),

  // System management
  getSystemStats: () => api.get('/admin/system/stats'),
  getSystemLogs: (page = 1, limit = 50, level?: string) =>
    api.get(`/admin/system/logs?page=${page}&limit=${limit}${level ? `&level=${level}` : ''}`),
};

export default api;