import { AxiosError } from 'axios';



export interface ApiError {
  statusCode: number;
  message: string;
  error?: string;
  timestamp: string;
  path: string;
}

// 错误代码常量
export const ErrorCodes = {
  // 网络错误
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  CONNECTION_ERROR: 'CONNECTION_ERROR',

  // 认证错误
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',

  // 业务错误
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  RESOURCE_NOT_FOUND: 'RESOURCE_NOT_FOUND',
  RESOURCE_CONFLICT: 'RESOURCE_CONFLICT',

  // 系统错误
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
  UNKNOWN: 'UNKNOWN',

  // 翻译相关错误
  TRANSLATION_FAILED: 'TRANSLATION_FAILED',
  LANGUAGE_NOT_SUPPORTED: 'LANGUAGE_NOT_SUPPORTED',
  TEXT_TOO_LONG: 'TEXT_TOO_LONG',
  QUOTA_EXCEEDED: 'QUOTA_EXCEEDED',

  // 支付相关错误
  PAYMENT_FAILED: 'PAYMENT_FAILED',
  INVALID_COUPON: 'INVALID_COUPON',
  SUBSCRIPTION_EXPIRED: 'SUBSCRIPTION_EXPIRED',
} as const;

export type ErrorCode = typeof ErrorCodes[keyof typeof ErrorCodes];

// 错误类型定义
export interface AppError {
  code: ErrorCode;
  message: string;
  details?: any;
  timestamp: string;
  stack?: string;
}

// 错误消息映射
const ERROR_MESSAGES: Record<string, string> = {
  [ErrorCodes.NETWORK_ERROR]: '网络连接失败，请检查网络设置',
  [ErrorCodes.TIMEOUT_ERROR]: '请求超时，请稍后重试',
  [ErrorCodes.CONNECTION_ERROR]: '无法连接到服务器',
  
  [ErrorCodes.UNAUTHORIZED]: '未授权访问，请重新登录',
  [ErrorCodes.FORBIDDEN]: '权限不足，无法执行此操作',
  [ErrorCodes.TOKEN_EXPIRED]: '登录已过期，请重新登录',
  
  [ErrorCodes.VALIDATION_ERROR]: '输入数据格式错误',
  [ErrorCodes.RESOURCE_NOT_FOUND]: '请求的资源不存在',
  [ErrorCodes.RESOURCE_CONFLICT]: '资源冲突，请刷新后重试',
  
  [ErrorCodes.INTERNAL_ERROR]: '系统内部错误，请稍后重试',
  [ErrorCodes.SERVICE_UNAVAILABLE]: '服务暂时不可用，请稍后重试',
  
  [ErrorCodes.TRANSLATION_FAILED]: '翻译失败，请重试',
  [ErrorCodes.LANGUAGE_NOT_SUPPORTED]: '不支持的语言',
  [ErrorCodes.TEXT_TOO_LONG]: '文本长度超出限制',
  [ErrorCodes.QUOTA_EXCEEDED]: '使用配额已用完',
  
  [ErrorCodes.PAYMENT_FAILED]: '支付失败，请重试',
  [ErrorCodes.INVALID_COUPON]: '优惠券无效或已过期',
  [ErrorCodes.SUBSCRIPTION_EXPIRED]: '订阅已过期，请续费',
  [ErrorCodes.UNKNOWN]: '未知错误，请稍后重试',
};

// 错误处理工具类
export class ErrorHandler {
  // 解析API错误
  static parseApiError(error: AxiosError): AppError {
    const timestamp = new Date().toISOString();
    
    if (!error.response) {
      // 网络错误
      return {
        code: ErrorCodes.NETWORK_ERROR,
        message: ERROR_MESSAGES[ErrorCodes.NETWORK_ERROR],
        timestamp,
        stack: error.stack,
      };
    }

    const { status, data } = error.response;
    const apiError = data as ApiError;

    // 根据HTTP状态码映射错误
    let errorCode: ErrorCode;
    let message: string;

    switch (status) {
      case 400:
        errorCode = ErrorCodes.VALIDATION_ERROR;
        message = apiError.message || ERROR_MESSAGES[ErrorCodes.VALIDATION_ERROR];
        break;
      case 401:
        errorCode = ErrorCodes.UNAUTHORIZED;
        message = ERROR_MESSAGES[ErrorCodes.UNAUTHORIZED];
        break;
      case 403:
        errorCode = ErrorCodes.FORBIDDEN;
        message = ERROR_MESSAGES[ErrorCodes.FORBIDDEN];
        break;
      case 404:
        errorCode = ErrorCodes.RESOURCE_NOT_FOUND;
        message = ERROR_MESSAGES[ErrorCodes.RESOURCE_NOT_FOUND];
        break;
      case 409:
        errorCode = ErrorCodes.RESOURCE_CONFLICT;
        message = ERROR_MESSAGES[ErrorCodes.RESOURCE_CONFLICT];
        break;
      case 429:
        errorCode = ErrorCodes.QUOTA_EXCEEDED;
        message = ERROR_MESSAGES[ErrorCodes.QUOTA_EXCEEDED];
        break;
      case 500:
        errorCode = ErrorCodes.INTERNAL_ERROR;
        message = ERROR_MESSAGES[ErrorCodes.INTERNAL_ERROR];
        break;
      case 503:
        errorCode = ErrorCodes.SERVICE_UNAVAILABLE;
        message = ERROR_MESSAGES[ErrorCodes.SERVICE_UNAVAILABLE];
        break;
      default:
        errorCode = ErrorCodes.INTERNAL_ERROR;
        message = apiError.message || ERROR_MESSAGES[ErrorCodes.INTERNAL_ERROR];
    }

    return {
      code: errorCode,
      message,
      details: apiError,
      timestamp,
      stack: error.stack,
    };
  }

  // 解析通用错误
  static parseError(error: unknown): AppError {
    const timestamp = new Date().toISOString();

    if (error instanceof AxiosError) {
      return this.parseApiError(error);
    }

    if (error instanceof Error) {
      return {
        code: ErrorCodes.INTERNAL_ERROR,
        message: error.message || ERROR_MESSAGES[ErrorCodes.INTERNAL_ERROR],
        timestamp,
        stack: error.stack,
      };
    }

    return {
      code: ErrorCodes.INTERNAL_ERROR,
      message: ERROR_MESSAGES[ErrorCodes.INTERNAL_ERROR],
      details: error,
      timestamp,
    };
  }

  // 获取用户友好的错误消息
  static getUserMessage(error: AppError): string {
    return ERROR_MESSAGES[error.code] || error.message || '发生未知错误';
  }

  // 判断是否为可重试的错误
  static isRetryableError(error: AppError): boolean {
    const retryableCodes: ErrorCode[] = [
      ErrorCodes.NETWORK_ERROR,
      ErrorCodes.TIMEOUT_ERROR,
      ErrorCodes.CONNECTION_ERROR,
      ErrorCodes.INTERNAL_ERROR,
      ErrorCodes.SERVICE_UNAVAILABLE,
    ];
    return retryableCodes.includes(error.code);
  }

  // 判断是否需要重新登录
  static requiresReauth(error: AppError): boolean {
    const reauthCodes: ErrorCode[] = [
      ErrorCodes.UNAUTHORIZED,
      ErrorCodes.TOKEN_EXPIRED,
    ];
    return reauthCodes.includes(error.code);
  }

  // 记录错误到控制台（开发环境）或监控服务（生产环境）
  static logError(error: AppError, context?: string) {
    const logData = {
      ...error,
      context,
      userAgent: navigator.userAgent,
      url: window.location.href,
    };

    if (process.env.NODE_ENV === 'development') {
      console.error('Application Error:', logData);
    } else {
      // 生产环境发送到监控服务
      this.reportToMonitoring(logData);
    }
  }

  // 发送错误到监控服务
  private static async reportToMonitoring(errorData: any) {
    try {
      await fetch('/api/v1/errors/report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(errorData),
      });
    } catch (reportError) {
      console.error('Failed to report error to monitoring service:', reportError);
    }
  }
}

// React Hook for error handling
export const useErrorHandler = () => {
  const handleError = (error: unknown, context?: string) => {
    const appError = ErrorHandler.parseError(error);
    ErrorHandler.logError(appError, context);
    return appError;
  };

  const handleApiError = (error: AxiosError, context?: string) => {
    const appError = ErrorHandler.parseApiError(error);
    ErrorHandler.logError(appError, context);
    return appError;
  };

  return {
    handleError,
    handleApiError,
    parseError: ErrorHandler.parseError,
    parseApiError: ErrorHandler.parseApiError,
    getUserMessage: ErrorHandler.getUserMessage,
    isRetryableError: ErrorHandler.isRetryableError,
    requiresReauth: ErrorHandler.requiresReauth,
  };
};

// 异步操作错误处理装饰器
export const withErrorHandling = <T extends (...args: any[]) => Promise<any>>(
  fn: T,
  context?: string
): T => {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      const appError = ErrorHandler.parseError(error);
      ErrorHandler.logError(appError, context);
      throw appError;
    }
  }) as T;
};

// 全局错误处理器
export const setupGlobalErrorHandling = () => {
  // 处理未捕获的Promise拒绝
  window.addEventListener('unhandledrejection', (event) => {
    const error = ErrorHandler.parseError(event.reason);
    ErrorHandler.logError(error, 'unhandledrejection');
    
    // 阻止默认的控制台错误输出
    event.preventDefault();
  });

  // 处理未捕获的JavaScript错误
  window.addEventListener('error', (event) => {
    const error = ErrorHandler.parseError(event.error);
    ErrorHandler.logError(error, 'uncaught_error');
  });
};

export default ErrorHandler;
