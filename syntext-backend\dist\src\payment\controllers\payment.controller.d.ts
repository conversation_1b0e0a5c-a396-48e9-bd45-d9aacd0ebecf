import { PaymentService } from '../services/payment.service';
import { PaymentMethod } from '../../entities/payment.entity';
export declare class PaymentController {
    private readonly paymentService;
    constructor(paymentService: PaymentService);
    createPayment(req: any, body: {
        subscriptionId: string;
        amount: number;
        currency: string;
        method: PaymentMethod;
        description: string;
        couponId?: string;
        returnUrl?: string;
    }): Promise<{
        code: number;
        message: string;
        data: {
            paymentId: string;
            transactionId: string;
            amount: number;
            currency: string;
            method: PaymentMethod;
            paymentUrl: string;
            qrCode: string;
            status: import("../../entities/payment.entity").PaymentStatus;
        };
    }>;
    getPaymentDetails(paymentId: string): Promise<{
        code: number;
        message: string;
        data: import("../../entities/payment.entity").Payment;
    }>;
    checkPaymentStatus(paymentId: string): Promise<{
        code: number;
        message: string;
        data: {
            paymentId: string;
            status: import("../../entities/payment.entity").PaymentStatus;
            amount: number;
            currency: string;
            method: PaymentMethod;
            createdAt: Date;
            completedAt: Date;
        };
    }>;
    getUserPayments(req: any, limit?: string, offset?: string): Promise<{
        code: number;
        message: string;
        data: import("../../entities/payment.entity").Payment[];
    }>;
    handlePaymentCallback(paymentId: string, callbackData: any): Promise<{
        code: number;
        message: string;
    }>;
    refundPayment(paymentId: string, body: {
        amount?: number;
        reason?: string;
    }): Promise<{
        code: number;
        message: string;
        data: {
            success: boolean;
        };
    }>;
}
