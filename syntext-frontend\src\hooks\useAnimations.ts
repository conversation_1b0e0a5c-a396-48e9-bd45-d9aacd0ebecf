import { useEffect, useRef, useCallback, useState } from 'react';
import { animate, animateStagger, createIntersectionAnimation, keyframes } from '../utils/animations';

// 页面进入动画Hook
export const usePageEnterAnimation = (delay: number = 0) => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (ref.current) {
      const timer = setTimeout(() => {
        if (ref.current) {
          animate(ref.current, 'fadeIn', { delay });
        }
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [delay]);

  return ref;
};

// 滚动触发动画Hook
export const useScrollAnimation = (
  animationType: keyof typeof keyframes = 'fadeIn',
  options: IntersectionObserverInit = {}
) => {
  const ref = useRef<HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    if (ref.current) {
      observerRef.current = createIntersectionAnimation(
        ref.current,
        animationType,
        {},
        options
      );
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [animationType, options]);

  return ref;
};

// 列表项交错动画Hook
export const useStaggerAnimation = (
  animationType: keyof typeof keyframes = 'slideInUp',
  staggerDelay: number = 100
) => {
  const containerRef = useRef<HTMLElement>(null);
  const [isVisible, setIsVisible] = useState(false);

  const triggerAnimation = useCallback(() => {
    if (containerRef.current && !isVisible) {
      const children = Array.from(containerRef.current.children) as HTMLElement[];
      animateStagger(children, animationType, {}, staggerDelay);
      setIsVisible(true);
    }
  }, [animationType, staggerDelay, isVisible]);

  useEffect(() => {
    if (containerRef.current) {
      const observer = new IntersectionObserver(
        (entries) => {
          entries.forEach((entry) => {
            if (entry.isIntersecting) {
              triggerAnimation();
              observer.unobserve(entry.target);
            }
          });
        },
        { threshold: 0.1 }
      );

      observer.observe(containerRef.current);

      return () => observer.disconnect();
    }
  }, [triggerAnimation]);

  return { containerRef, triggerAnimation };
};

// 悬停动画Hook
export const useHoverAnimation = (
  hoverAnimation: keyof typeof keyframes = 'scaleIn',
  leaveAnimation: keyof typeof keyframes = 'scaleOut'
) => {
  const ref = useRef<HTMLElement>(null);

  const handleMouseEnter = useCallback(() => {
    if (ref.current) {
      animate(ref.current, hoverAnimation, { duration: 200 });
    }
  }, [hoverAnimation]);

  const handleMouseLeave = useCallback(() => {
    if (ref.current) {
      animate(ref.current, leaveAnimation, { duration: 150 });
    }
  }, [leaveAnimation]);

  return {
    ref,
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
  };
};

// 点击波纹效果Hook
export const useRippleEffect = (color: string = 'rgba(255, 255, 255, 0.6)') => {
  const ref = useRef<HTMLElement>(null);
  const [ripples, setRipples] = useState<Array<{
    x: number;
    y: number;
    id: number;
  }>>([]);

  const createRipple = useCallback((event: React.MouseEvent<HTMLElement>) => {
    if (ref.current) {
      const rect = ref.current.getBoundingClientRect();
      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      const id = Date.now();

      setRipples(prev => [...prev, { x, y, id }]);

      setTimeout(() => {
        setRipples(prev => prev.filter(ripple => ripple.id !== id));
      }, 600);
    }
  }, []);

  return {
    ref,
    ripples,
    createRipple,
    rippleColor: color,
  };
};

// 加载状态动画Hook
export const useLoadingAnimation = (isLoading: boolean) => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    if (ref.current) {
      if (isLoading) {
        animate(ref.current, 'pulse', { iterations: Infinity });
      } else {
        // 停止动画
        const animations = ref.current.getAnimations();
        animations.forEach(animation => animation.cancel());
      }
    }
  }, [isLoading]);

  return ref;
};

// 错误摇摆动画Hook
export const useErrorAnimation = () => {
  const ref = useRef<HTMLElement>(null);

  const triggerError = useCallback(() => {
    if (ref.current) {
      animate(ref.current, 'shake', { duration: 500 });
    }
  }, []);

  return { ref, triggerError };
};

// 成功动画Hook
export const useSuccessAnimation = () => {
  const ref = useRef<HTMLElement>(null);

  const triggerSuccess = useCallback(() => {
    if (ref.current) {
      animate(ref.current, 'bounce', { duration: 600 });
    }
  }, []);

  return { ref, triggerSuccess };
};

// 模态框动画Hook
export const useModalAnimation = (isOpen: boolean) => {
  const backdropRef = useRef<HTMLElement>(null);
  const contentRef = useRef<HTMLElement>(null);

  useEffect(() => {
    if (isOpen) {
      // 打开动画
      if (backdropRef.current) {
        animate(backdropRef.current, 'fadeIn', { duration: 200 });
      }
      if (contentRef.current) {
        animate(contentRef.current, 'scaleIn', { duration: 200, delay: 50 });
      }
    } else {
      // 关闭动画
      if (contentRef.current) {
        animate(contentRef.current, 'scaleOut', { duration: 150 });
      }
      if (backdropRef.current) {
        animate(backdropRef.current, 'fadeOut', { duration: 150, delay: 50 });
      }
    }
  }, [isOpen]);

  return { backdropRef, contentRef };
};

// 文本打字机效果Hook
export const useTypewriterEffect = (
  text: string,
  speed: number = 50,
  startDelay: number = 0
) => {
  const [displayText, setDisplayText] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  useEffect(() => {
    setDisplayText('');
    setIsTyping(true);

    const startTimer = setTimeout(() => {
      let index = 0;
      const timer = setInterval(() => {
        if (index < text.length) {
          setDisplayText(text.slice(0, index + 1));
          index++;
        } else {
          clearInterval(timer);
          setIsTyping(false);
        }
      }, speed);

      return () => clearInterval(timer);
    }, startDelay);

    return () => clearTimeout(startTimer);
  }, [text, speed, startDelay]);

  return { displayText, isTyping };
};

// 数字计数动画Hook
export const useCountAnimation = (
  endValue: number,
  duration: number = 1000,
  startDelay: number = 0
) => {
  const [currentValue, setCurrentValue] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    setCurrentValue(0);
    setIsAnimating(true);

    const startTimer = setTimeout(() => {
      const startTime = Date.now();
      const startValue = 0;

      const updateValue = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);
        
        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const value = Math.round(startValue + (endValue - startValue) * easeOutQuart);
        
        setCurrentValue(value);

        if (progress < 1) {
          requestAnimationFrame(updateValue);
        } else {
          setIsAnimating(false);
        }
      };

      requestAnimationFrame(updateValue);
    }, startDelay);

    return () => clearTimeout(startTimer);
  }, [endValue, duration, startDelay]);

  return { currentValue, isAnimating };
};

// 视差滚动效果Hook
export const useParallaxEffect = (speed: number = 0.5) => {
  const ref = useRef<HTMLElement>(null);

  useEffect(() => {
    const handleScroll = () => {
      if (ref.current) {
        const scrolled = window.pageYOffset;
        const parallax = scrolled * speed;
        ref.current.style.transform = `translateY(${parallax}px)`;
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed]);

  return ref;
};
