import React, { useEffect, useState } from 'react';
import { useAppSelector, useAppDispatch } from '../hooks';
import { fetchLanguages } from '../store/slices/translationSlice';
import { websocketService } from '../services/websocket';
import TranslationArea from '../components/TranslationArea';
import TranslationHistory from '../components/TranslationHistory';

const Translation: React.FC = () => {
  const dispatch = useAppDispatch();
  const { websocket } = useAppSelector((state) => state.translation);
  const { isAuthenticated } = useAppSelector((state) => state.auth);
  const [activeView, setActiveView] = useState<'translate' | 'history'>('translate');

  useEffect(() => {
    // 加载支持的语言列表
    dispatch(fetchLanguages());

    // 如果用户已登录，连接WebSocket
    if (isAuthenticated && !websocket.connected && !websocket.connecting) {
      websocketService.connect();
    }

    // 组件卸载时断开WebSocket连接
    return () => {
      if (websocket.connected) {
        websocketService.disconnect();
      }
    };
  }, [dispatch, isAuthenticated, websocket.connected, websocket.connecting]);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 头部导航 */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-3">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                  </svg>
                </div>
                <h1 className="text-xl font-semibold text-gray-900">SynText</h1>
              </div>

              <nav className="flex space-x-1">
                <button
                  onClick={() => setActiveView('translate')}
                  className={`
                    px-4 py-2 text-sm font-medium rounded-md transition-colors
                    ${activeView === 'translate'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }
                  `}
                >
                  翻译
                </button>
                <button
                  onClick={() => setActiveView('history')}
                  className={`
                    px-4 py-2 text-sm font-medium rounded-md transition-colors
                    ${activeView === 'history'
                      ? 'bg-blue-100 text-blue-700'
                      : 'text-gray-500 hover:text-gray-700 hover:bg-gray-100'
                    }
                  `}
                >
                  历史记录
                </button>
              </nav>
            </div>

            {/* WebSocket连接状态 */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center space-x-2">
                <div className={`
                  w-2 h-2 rounded-full transition-colors
                  ${websocket.connected 
                    ? 'bg-green-500' 
                    : websocket.connecting 
                      ? 'bg-yellow-500 animate-pulse' 
                      : 'bg-red-500'
                  }
                `}></div>
                <span className="text-xs text-gray-500">
                  {websocket.connected 
                    ? '已连接' 
                    : websocket.connecting 
                      ? '连接中...' 
                      : '未连接'
                  }
                </span>
              </div>

              <div className="text-sm text-gray-700">
                欢迎使用 SynText 翻译服务
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 主要内容区域 */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeView === 'translate' ? (
          <div className="space-y-6 sm:space-y-8">
            {/* 翻译区域 */}
            <TranslationArea />

            {/* 使用提示 */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6">
              <h3 className="text-base sm:text-lg font-medium text-blue-900 mb-3">
                使用说明
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3 sm:gap-4 text-xs sm:text-sm text-blue-800">
                <div className="flex items-start space-x-2">
                  <svg className="w-4 h-4 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>支持 50+ 种语言互译</span>
                </div>
                <div className="flex items-start space-x-2">
                  <svg className="w-4 h-4 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>实时翻译进度显示</span>
                </div>
                <div className="flex items-start space-x-2">
                  <svg className="w-4 h-4 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>自动保存翻译历史</span>
                </div>
                <div className="flex items-start space-x-2">
                  <svg className="w-4 h-4 mt-0.5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>支持收藏常用翻译</span>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <div className="space-y-4 sm:space-y-6">
            <div>
              <h2 className="text-xl sm:text-2xl font-bold text-gray-900 mb-2">翻译历史</h2>
              <p className="text-sm sm:text-base text-gray-600">查看您的翻译历史记录和收藏夹</p>
            </div>
            
            <TranslationHistory 
              onSelectTranslation={(_translation) => {
                setActiveView('translate');
                // 翻译内容已通过 dispatch 设置到 store 中
              }}
            />
          </div>
        )}
      </main>

      {/* 底部信息 */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              © 2024 SynText. 基于 DeepSeek AI 提供的智能翻译服务
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>服务状态</span>
              <div className={`
                px-2 py-1 rounded text-xs font-medium
                ${websocket.connected
                  ? 'bg-green-100 text-green-700'
                  : 'bg-red-100 text-red-700'
                }
              `}>
                {websocket.connected ? '正常运行' : '服务离线'}
              </div>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Translation;