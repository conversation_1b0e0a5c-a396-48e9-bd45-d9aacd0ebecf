import { HealthCheckError } from '../health-check/health-check.error';
/**
 * Error which gets thrown when the given storage threshold
 * has exceeded.
 * @publicApi
 */
export declare class StorageExceededError extends HealthCheckError {
    /**
     * Initializes the error
     *
     * @param {string} keyword The keyword (heap, rss, disk e.g.)
     * @param {any} cause The cause of the health check error
     *
     * @internal
     */
    constructor(keyword: string, cause: any);
}
