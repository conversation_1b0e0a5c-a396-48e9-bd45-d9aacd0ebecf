import React, { useState, useEffect } from 'react';
import { cn } from '../../utils/cn';
import MobileNav, { defaultNavItems } from './MobileNav';

interface ResponsiveLayoutProps {
  children: React.ReactNode;
  showMobileNav?: boolean;
  mobileNavItems?: typeof defaultNavItems;
  header?: React.ReactNode;
  sidebar?: React.ReactNode;
  className?: string;
}

// 检测设备类型的 Hook
const useDeviceType = () => {
  const [deviceType, setDeviceType] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');

  useEffect(() => {
    const checkDeviceType = () => {
      const width = window.innerWidth;
      if (width < 768) {
        setDeviceType('mobile');
      } else if (width < 1024) {
        setDeviceType('tablet');
      } else {
        setDeviceType('desktop');
      }
    };

    checkDeviceType();
    window.addEventListener('resize', checkDeviceType);
    return () => window.removeEventListener('resize', checkDeviceType);
  }, []);

  return deviceType;
};

// 检测触摸设备的 Hook
const useTouchDevice = () => {
  const [isTouchDevice, setIsTouchDevice] = useState(false);

  useEffect(() => {
    setIsTouchDevice('ontouchstart' in window || navigator.maxTouchPoints > 0);
  }, []);

  return isTouchDevice;
};

const ResponsiveLayout: React.FC<ResponsiveLayoutProps> = ({
  children,
  showMobileNav = true,
  mobileNavItems = defaultNavItems,
  header,
  sidebar,
  className,
}) => {
  const deviceType = useDeviceType();
  const isTouchDevice = useTouchDevice();
  const [sidebarOpen, setSidebarOpen] = useState(false);

  const isMobile = deviceType === 'mobile';
  const isTablet = deviceType === 'tablet';
  const isDesktop = deviceType === 'desktop';

  // 移动端时关闭侧边栏
  useEffect(() => {
    if (isMobile) {
      setSidebarOpen(false);
    }
  }, [isMobile]);

  return (
    <div className={cn(
      'min-h-screen bg-secondary-50',
      isTouchDevice && 'touch-manipulation',
      className
    )}>
      {/* 桌面端布局 */}
      {isDesktop && (
        <div className="flex h-screen">
          {/* 侧边栏 */}
          {sidebar && (
            <aside className={cn(
              'w-64 bg-white border-r border-secondary-200 transition-all duration-300',
              sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
            )}>
              {sidebar}
            </aside>
          )}

          {/* 主内容区域 */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* 头部 */}
            {header && (
              <header className="bg-white border-b border-secondary-200 safe-area-top">
                {header}
              </header>
            )}

            {/* 内容 */}
            <main className="flex-1 overflow-auto">
              <div className="container-responsive py-6">
                {children}
              </div>
            </main>
          </div>
        </div>
      )}

      {/* 平板端布局 */}
      {isTablet && (
        <div className="flex h-screen">
          {/* 可折叠侧边栏 */}
          {sidebar && (
            <>
              {/* 遮罩层 */}
              {sidebarOpen && (
                <div
                  className="fixed inset-0 bg-black/50 z-40 lg:hidden"
                  onClick={() => setSidebarOpen(false)}
                />
              )}
              
              <aside className={cn(
                'fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white border-r border-secondary-200',
                'transform transition-transform duration-300 ease-in-out lg:translate-x-0',
                sidebarOpen ? 'translate-x-0' : '-translate-x-full'
              )}>
                {sidebar}
              </aside>
            </>
          )}

          {/* 主内容区域 */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* 头部 */}
            {header && (
              <header className="bg-white border-b border-secondary-200 safe-area-top">
                <div className="flex items-center">
                  {sidebar && (
                    <button
                      onClick={() => setSidebarOpen(!sidebarOpen)}
                      className="lg:hidden p-2 rounded-md text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                      </svg>
                    </button>
                  )}
                  <div className="flex-1">
                    {header}
                  </div>
                </div>
              </header>
            )}

            {/* 内容 */}
            <main className="flex-1 overflow-auto">
              <div className="container-responsive py-6">
                {children}
              </div>
            </main>
          </div>
        </div>
      )}

      {/* 移动端布局 */}
      {isMobile && (
        <div className="flex flex-col min-h-screen">
          {/* 头部 */}
          {header && (
            <header className="bg-white border-b border-secondary-200 safe-area-top sticky top-0 z-30">
              {header}
            </header>
          )}

          {/* 内容 */}
          <main className={cn(
            'flex-1 overflow-auto',
            showMobileNav && 'pb-20' // 为底部导航留出空间
          )}>
            <div className="px-4 py-6">
              {children}
            </div>
          </main>

          {/* 移动端底部导航 */}
          {showMobileNav && (
            <MobileNav items={mobileNavItems} />
          )}
        </div>
      )}

      {/* 侧边栏切换按钮（仅在有侧边栏时显示） */}
      {sidebar && !isDesktop && (
        <button
          onClick={() => setSidebarOpen(!sidebarOpen)}
          className={cn(
            'fixed top-4 left-4 z-50 p-2 bg-white rounded-lg shadow-lg',
            'text-secondary-600 hover:text-secondary-900 hover:bg-secondary-100',
            'transition-all duration-200 active:scale-95',
            sidebarOpen && 'hidden'
          )}
        >
          <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          </svg>
        </button>
      )}
    </div>
  );
};

export default ResponsiveLayout;
export { useDeviceType, useTouchDevice };
