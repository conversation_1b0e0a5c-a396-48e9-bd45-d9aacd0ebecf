import { Injectable, NotFoundException, BadRequestException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserSubscription, SubscriptionStatus, BillingCycle } from '../../entities/user-subscription.entity';
import { SubscriptionPlan, PlanType } from '../../entities/subscription-plan.entity';
import { User } from '../../entities/user.entity';
import { SubscriptionPlanService } from './subscription-plan.service';

export interface CreateSubscriptionDto {
  userId: string;
  planType: PlanType;
  billingCycle: BillingCycle;
  autoRenewal?: boolean;
}

@Injectable()
export class SubscriptionService {
  private readonly logger = new Logger(SubscriptionService.name);

  constructor(
    @InjectRepository(UserSubscription)
    private userSubscriptionRepository: Repository<UserSubscription>,
    @InjectRepository(User)
    private userRepository: Repository<User>,
    private subscriptionPlanService: SubscriptionPlanService,
  ) {}

  async getUserSubscription(userId: string): Promise<UserSubscription | null> {
    return this.userSubscriptionRepository.findOne({
      where: { userId, status: SubscriptionStatus.ACTIVE },
      relations: ['plan'],
      order: { createdAt: 'DESC' },
    });
  }

  async createSubscription(createDto: CreateSubscriptionDto): Promise<UserSubscription> {
    const user = await this.userRepository.findOne({
      where: { id: createDto.userId },
    });

    if (!user) {
      throw new NotFoundException('用户不存在');
    }

    const plan = await this.subscriptionPlanService.getPlanByType(createDto.planType);
    if (!plan) {
      throw new NotFoundException('订阅套餐不存在');
    }

    // 检查是否已有活跃订阅
    const existingSubscription = await this.getUserSubscription(createDto.userId);
    if (existingSubscription) {
      throw new BadRequestException('用户已有活跃订阅');
    }

    const now = new Date();
    const amount = createDto.billingCycle === BillingCycle.YEARLY 
      ? plan.yearlyPrice 
      : plan.monthlyPrice;

    const endDate = new Date();
    if (createDto.billingCycle === BillingCycle.YEARLY) {
      endDate.setFullYear(endDate.getFullYear() + 1);
    } else {
      endDate.setMonth(endDate.getMonth() + 1);
    }

    const nextBillingDate = createDto.autoRenewal ? new Date(endDate) : new Date(endDate);

    const subscription = this.userSubscriptionRepository.create({
      userId: createDto.userId,
      planId: plan.id,
      status: SubscriptionStatus.PENDING,
      billingCycle: createDto.billingCycle,
      amount,
      startDate: now,
      endDate,
      nextBillingDate,
      autoRenewal: createDto.autoRenewal || false,
      monthlyCharactersUsed: 0,
      dailyCharactersUsed: 0,
      dailyTranslationsUsed: 0,
      lastUsageResetDate: new Date(),
    });

    const savedSubscription = await this.userSubscriptionRepository.save(subscription);

    this.logger.log(`Created subscription ${(savedSubscription as any).id} for user ${createDto.userId}`);

    const result = await this.userSubscriptionRepository.findOne({
      where: { id: (savedSubscription as any).id },
      relations: ['plan'],
    });
    return result!;
  }

  async activateSubscription(subscriptionId: string): Promise<UserSubscription> {
    const subscription = await this.userSubscriptionRepository.findOne({
      where: { id: subscriptionId },
      relations: ['plan'],
    });

    if (!subscription) {
      throw new NotFoundException('订阅不存在');
    }

    subscription.status = SubscriptionStatus.ACTIVE;
    await this.userSubscriptionRepository.save(subscription);

    this.logger.log(`Activated subscription ${subscriptionId}`);
    
    return subscription;
  }

  async cancelSubscription(userId: string, reason?: string): Promise<void> {
    const subscription = await this.getUserSubscription(userId);
    
    if (!subscription) {
      throw new NotFoundException('未找到活跃订阅');
    }

    subscription.status = SubscriptionStatus.CANCELLED;
    subscription.autoRenewal = false;
    
    if (reason) {
      subscription.metadata = {
        ...subscription.metadata,
        cancellationReason: reason,
        cancelledAt: new Date(),
      };
    }

    await this.userSubscriptionRepository.save(subscription);
    
    this.logger.log(`Cancelled subscription ${subscription.id} for user ${userId}`);
  }

  async renewSubscription(userId: string): Promise<UserSubscription> {
    const currentSubscription = await this.getUserSubscription(userId);
    
    if (!currentSubscription) {
      throw new NotFoundException('未找到活跃订阅');
    }

    const plan = await this.subscriptionPlanService.getPlanById(currentSubscription.planId);
    if (!plan) {
      throw new NotFoundException('订阅套餐不存在');
    }

    // 设置为过期状态
    currentSubscription.status = SubscriptionStatus.EXPIRED;
    await this.userSubscriptionRepository.save(currentSubscription);

    // 创建新的订阅
    const newEndDate = new Date();
    if (currentSubscription.billingCycle === BillingCycle.YEARLY) {
      newEndDate.setFullYear(newEndDate.getFullYear() + 1);
    } else {
      newEndDate.setMonth(newEndDate.getMonth() + 1);
    }

    const renewedSubscription = this.userSubscriptionRepository.create({
      userId,
      planId: plan.id,
      status: SubscriptionStatus.ACTIVE,
      billingCycle: currentSubscription.billingCycle,
      amount: currentSubscription.amount,
      startDate: new Date(),
      endDate: newEndDate,
      nextBillingDate: currentSubscription.autoRenewal ? new Date(newEndDate) : new Date(newEndDate),
      autoRenewal: currentSubscription.autoRenewal,
      monthlyCharactersUsed: 0,
      dailyCharactersUsed: 0,
      dailyTranslationsUsed: 0,
      lastUsageResetDate: new Date(),
    });

    const saved = await this.userSubscriptionRepository.save(renewedSubscription);

    this.logger.log(`Renewed subscription for user ${userId}`);

    const result = await this.userSubscriptionRepository.findOne({
      where: { id: (saved as any).id },
      relations: ['plan'],
    });
    return result!;
  }

  async checkAndExpireSubscriptions(): Promise<void> {
    const expiredSubscriptions = await this.userSubscriptionRepository.find({
      where: {
        status: SubscriptionStatus.ACTIVE,
        endDate: new Date(),
      },
    });

    for (const subscription of expiredSubscriptions) {
      if (subscription.autoRenewal) {
        try {
          await this.renewSubscription(subscription.userId);
        } catch (error) {
          this.logger.error(`Failed to auto-renew subscription ${subscription.id}:`, error);
          subscription.status = SubscriptionStatus.EXPIRED;
          await this.userSubscriptionRepository.save(subscription);
        }
      } else {
        subscription.status = SubscriptionStatus.EXPIRED;
        await this.userSubscriptionRepository.save(subscription);
      }
    }

    if (expiredSubscriptions.length > 0) {
      this.logger.log(`Processed ${expiredSubscriptions.length} expired subscriptions`);
    }
  }

  async getSubscriptionHistory(userId: string): Promise<UserSubscription[]> {
    return this.userSubscriptionRepository.find({
      where: { userId },
      relations: ['plan'],
      order: { createdAt: 'DESC' },
    });
  }
}